package com.fuyingedu.training.inner.controller;

import com.fuyingedu.training.common.model.CommResp;
import com.fuyingedu.training.inner.model.DetailResp;
import com.fuyingedu.training.inner.model.OrderResp;
import com.fuyingedu.training.inner.service.InnerScheduleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 排期管理
 */
@RestController
@RequestMapping("/inner/schedule")
public class InnerScheduleController {

    @Autowired
    private InnerScheduleService innerScheduleService;

    /**
     * 服务单列表
     * @param scheduleId 排期ID
     */
    @GetMapping("order/list")
    public CommResp<List<OrderResp>> listOrder(@RequestParam("scheduleId") Long scheduleId) {
        return innerScheduleService.listOrder(scheduleId);
    }

    /**
     * 服务单详情
     */
    @GetMapping("order/detail")
    public CommResp<DetailResp> detailOrder(@RequestParam("no") String no) {
        return innerScheduleService.detailOrder(no);
    }
}
