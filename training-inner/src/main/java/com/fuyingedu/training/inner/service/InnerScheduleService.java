package com.fuyingedu.training.inner.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fuyingedu.training.common.model.CommResp;
import com.fuyingedu.training.common.util.RespUtils;
import com.fuyingedu.training.entity.Clazz;
import com.fuyingedu.training.entity.Order;
import com.fuyingedu.training.entity.Schedule;
import com.fuyingedu.training.entity.Teacher;
import com.fuyingedu.training.inner.model.DetailResp;
import com.fuyingedu.training.inner.model.OrderResp;
import com.fuyingedu.training.mapper.ClazzMapper;
import com.fuyingedu.training.mapper.OrderMapper;
import com.fuyingedu.training.mapper.ScheduleMapper;
import com.fuyingedu.training.mapper.TeacherMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class InnerScheduleService {

    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private TeacherMapper teacherMapper;
    @Autowired
    private ClazzMapper clazzMapper;
    @Autowired
    private ScheduleMapper scheduleMapper;

    public CommResp<List<OrderResp>> listOrder(Long scheduleId) {
        List<Schedule> scheduleList = scheduleMapper.selectList(new LambdaQueryWrapper<Schedule>().select(Schedule::getId).eq(Schedule::getScheduleId, scheduleId));
        if (scheduleList.isEmpty()) {
            return RespUtils.success(Collections.emptyList());
        }
        List<Long> scheduleIds = scheduleList.stream().map(Schedule::getId).toList();
        List<Order> orderList = orderMapper.selectList(new LambdaQueryWrapper<Order>().select(
                Order::getUserId, Order::getOrderNo, Order::getTeacherId, Order::getClazzId
        ).in(Order::getScheduleId, scheduleIds));
        Set<Long> teacherIds = new HashSet<>(orderList.stream().map(Order::getTeacherId).filter(Objects::nonNull).toList());
        Set<Long> clazzIds = orderList.stream().map(Order::getClazzId).filter(Objects::nonNull).collect(Collectors.toSet());
        Map<Long, Teacher> teacheMap = Collections.emptyMap();
        Map<Long, Clazz> clazzMap = Collections.emptyMap();
        if (!clazzIds.isEmpty()) {
            clazzMap = clazzMapper.selectList(new LambdaQueryWrapper<Clazz>().select(
                    Clazz::getId, Clazz::getAssistantId
            ).in(Clazz::getId, clazzIds)).stream().collect(Collectors.toMap(Clazz::getId, c -> c));
            teacherIds.addAll(clazzMap.values().stream().map(Clazz::getAssistantId).filter(Objects::nonNull).toList());
        }
        if (!teacherIds.isEmpty()) {
            teacheMap = teacherMapper.selectList(new LambdaQueryWrapper<Teacher>().select(
                    Teacher::getId, Teacher::getUserId
            ).in(Teacher::getId, teacherIds)).stream().collect(Collectors.toMap(Teacher::getId, t -> t));
        }
        List<OrderResp> respList = new ArrayList<>();
        for (Order order : orderList) {
            OrderResp orderResp = new OrderResp();
            orderResp.setOrderNo(order.getOrderNo());
            orderResp.setUid(order.getUserId());
            if (order.getTeacherId() != null) {
                Teacher teacher = teacheMap.get(order.getTeacherId());
                orderResp.setTeacherUid(teacher.getUserId());
            }
            if (order.getClazzId() != null) {
                Clazz clazz = clazzMap.get(order.getClazzId());
                Teacher assistant = teacheMap.get(clazz.getAssistantId());
                orderResp.setAssistantUid(assistant.getUserId());
            }
            respList.add(orderResp);
        }
        return RespUtils.success(respList);
    }


    public CommResp<DetailResp> detailOrder(String no) {
        Order order = orderMapper.selectOne(new LambdaQueryWrapper<Order>().select(
                Order::getId, Order::getClazzId, Order::getTeacherId, Order::getConfirmStatus, Order::getConfirmTime
        ).eq(Order::getOrderNo, no));
        DetailResp resp = new DetailResp();
        if (order == null) {
            return RespUtils.warning(4000, "订单不存在");
        }
        resp.setConfirmStatus(order.getConfirmStatus());
        resp.setConfirmTime(order.getConfirmTime());
        if (order.getClazzId() != null) {
            Clazz clazz = clazzMapper.selectOne(new LambdaQueryWrapper<Clazz>().select(
                    Clazz::getId, Clazz::getAssistantId, Clazz::getClassName
            ).eq(Clazz::getId, order.getClazzId()));
            resp.setClazzName(clazz.getClassName());
            Teacher teacher = teacherMapper.selectOne(new LambdaQueryWrapper<Teacher>().select(
                    Teacher::getId, Teacher::getRealName
            ).eq(Teacher::getId, clazz.getAssistantId()));
            resp.setAssistantName(teacher.getRealName());
        }
        if (order.getTeacherId() != null) {
            Teacher teacher = teacherMapper.selectOne(new LambdaQueryWrapper<Teacher>().select(
                    Teacher::getId, Teacher::getRealName
            ).eq(Teacher::getId, order.getTeacherId()));
            resp.setTeacherName(teacher.getRealName());
        }
        return RespUtils.success(resp);
    }
}
