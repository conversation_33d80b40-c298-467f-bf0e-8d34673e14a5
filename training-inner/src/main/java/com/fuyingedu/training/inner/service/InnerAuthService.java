package com.fuyingedu.training.inner.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fuyingedu.training.common.model.CommResp;
import com.fuyingedu.training.common.util.DateUtils;
import com.fuyingedu.training.common.util.JwtUtils;
import com.fuyingedu.training.common.util.RespUtils;
import com.fuyingedu.training.entity.User;
import com.fuyingedu.training.front.feign.FuyingUserFeign;
import com.fuyingedu.training.front.model.feign.UnionItem;
import com.fuyingedu.training.front.model.feign.UserItem;
import com.fuyingedu.training.mapper.UserMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Map;

@Service
public class InnerAuthService {

    @Autowired
    private UserMapper userMapper;
    @Autowired
    private FuyingUserFeign fuyingUserFeign;

    public CommResp<String> getToken(Long phoneNo) {
        User user = userMapper.selectOne(new LambdaQueryWrapper<User>().select(
                User::getId
        ).eq(User::getPhoneNum, phoneNo));
        LocalDateTime expiredTime = LocalDateTime.now().plusDays(1);
        String token = JwtUtils.createToken(Map.of("userId", user.getId(), "type", "Admin",
                "expiredTime", DateUtils.toMillis(expiredTime)), expiredTime);
        return RespUtils.success(token);
    }

    public void syncUser(Long uid) {
        UserItem userItem = fuyingUserFeign.getUserByUid(uid);
        UnionItem unionId = fuyingUserFeign.getUnionId(uid);
        User user = new User();
        user.setId(userItem.getUid());
        user.setUserIcon(userItem.getAvatarUrl());
        user.setRealName(userItem.getNickname());
        user.setNickName(userItem.getNickname());
        user.setPhoneNum(Long.valueOf(userItem.getPhone()));
        if (unionId != null) {
            user.setUnionId(unionId.getThirdAuthId());
        }
        userMapper.insert(user);
    }
}
