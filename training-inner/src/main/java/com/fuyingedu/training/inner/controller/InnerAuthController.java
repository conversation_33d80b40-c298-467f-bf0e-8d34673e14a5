package com.fuyingedu.training.inner.controller;

import com.fuyingedu.training.common.model.CommResp;
import com.fuyingedu.training.common.util.RedisKey;
import com.fuyingedu.training.common.util.RespUtils;
import com.fuyingedu.training.front.manager.BaiJiaYunManager;
import com.fuyingedu.training.inner.service.InnerAuthService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;

import java.util.concurrent.TimeUnit;

/**
 * 设置接口
 */
@Slf4j
@RestController
@RequestMapping("inner/auth")
public class InnerAuthController {

    @Autowired
    private RedisTemplate<String, String> redisTemplate;
    @Autowired
    private InnerAuthService innerAuthService;
    @Autowired
    private BaiJiaYunManager baiJiaYunManager;

    /**
     * 前台设置短信验证码
     */
    @PostMapping("code")
    public CommResp<?> setCode(@RequestParam("phone") String phone,
                               @RequestParam("code") String code) {
        String codeKey = String.format(RedisKey.PHONE, phone);
        redisTemplate.opsForValue().set(codeKey, String.valueOf(code), 10, TimeUnit.MINUTES);
        return RespUtils.success();
    }

    /**
     * 后台获取Token
     */
    @GetMapping("token")
    public CommResp<String> getToken(@RequestParam("phone") Long phone) {
        return innerAuthService.getToken(phone);
    }

    /**
     * 刷新房间
     */
    @GetMapping("flush/room")
    public CommResp<?> flushRoom() {
        baiJiaYunManager.createGroup();
        return RespUtils.success();
    }

    /**
     * 同步傲爸妈接口用户
     */
    @PostMapping("sync/user")
    public CommResp<?> syncUser(@RequestParam("uid") Long uid) {
        innerAuthService.syncUser(uid);
        return RespUtils.success();
    }
}
