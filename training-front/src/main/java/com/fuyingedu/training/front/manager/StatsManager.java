package com.fuyingedu.training.front.manager;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fuyingedu.training.common.util.RespUtils;
import com.fuyingedu.training.entity.*;
import com.fuyingedu.training.front.model.stats.GroupResp;
import com.fuyingedu.training.front.model.stats.RewardResp;
import com.fuyingedu.training.mapper.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

@Component
public class StatsManager {

    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private StudentMapper studentMapper;
    @Autowired
    private UserMapper userMapper;
    @Autowired
    private GroupMapper groupMapper;
    @Autowired
    private WordPkRecordMapper wordPkRecordMapper;
    @Autowired
    private WordRewardMapper wordRewardMapper;
    @Autowired
    private OrderStatisticMapper orderStatisticMapper;

    @Cacheable(value = "stats:fxy:${#clazzId}")
    public List<RewardResp> fxyReward(Long clazzId) {
        List<Order> orderInfoList = orderMapper.selectList(new QueryWrapper<Order>().select(
                Order.ID, Order.STUDENT_ID, Order.USER_ID, Order.ORDER_REMARK
        ).eq(Order.CLAZZ_ID, clazzId));
        if (orderInfoList.isEmpty()) {
            return Collections.emptyList();
        }
        List<Long> ids = orderInfoList.stream().map(Order::getId).toList();
        Map<Long, Integer> orderStatsMap = orderStatisticMapper.selectList(new QueryWrapper<OrderStatistic>().select(
                OrderStatistic.ORDER_ID, OrderStatistic.FXY_REWARD
        ).in(OrderStatistic.ORDER_ID, ids)).stream().collect(Collectors.toMap(OrderStatistic::getOrderId, OrderStatistic::getFxyReward));
        Set<Long> userIds = orderInfoList.stream().map(Order::getUserId).collect(Collectors.toSet());
        Map<Long, User> userMap = userMapper.selectList(new QueryWrapper<User>().select(
                User.ID, User.REAL_NAME, User.USER_ICON
        ).in(User.ID, userIds)).stream().collect(Collectors.toMap(User::getId, item -> item));
        List<RewardResp> respList = new ArrayList<>(orderInfoList.size());
        for (Order item : orderInfoList) {
            RewardResp rewardResp = new RewardResp();
            User user = userMap.get(item.getUserId());
            rewardResp.setRealName(user.getRealName());
            rewardResp.setUserIcon(user.getUserIcon());
            if (StringUtils.hasLength(item.getOrderRemark())) {
                rewardResp.setRealName(item.getOrderRemark());
            }
            rewardResp.setReward(orderStatsMap.getOrDefault(item.getId(), 0));
            respList.add(rewardResp);
        }
        return respList.stream().sorted(Comparator.comparingInt(RewardResp::getReward).reversed()).toList();
    }

    @Cacheable(value = "fxy:group:${#clazzId}")
    public List<GroupResp> fxyGroupReward(Long clazzId) {
        List<Order> orderInfoList = orderMapper.selectList(new QueryWrapper<Order>().select(
                Order.ID, Order.GROUP_ID, Order.USER_ID
        ).eq(Order.CLAZZ_ID, clazzId));
        if (orderInfoList.isEmpty()) {
            return Collections.emptyList();
        }
        Map<Long, OrderStatistic> rewardMap = orderStatisticMapper.selectList(new QueryWrapper<OrderStatistic>().select(
                        OrderStatistic.ORDER_ID, OrderStatistic.FXY_REWARD
                ).in(OrderStatistic.ORDER_ID, orderInfoList.stream().map(Order::getId).toList()))
                .stream().collect(Collectors.toMap(OrderStatistic::getOrderId, v -> v));
        Map<Long, WordPkRecord> statsMap = new HashMap<>(rewardMap.size());
        for (Order item : orderInfoList) {
            WordPkRecord wordPkRecord = new WordPkRecord();
            wordPkRecord.setOrderId(item.getId());
            OrderStatistic orderStatistic = rewardMap.get(item.getId());
            if (orderStatistic != null) {
                wordPkRecord.setWinningNum(orderStatistic.getFxyReward());
            } else {
                wordPkRecord.setWinningNum(0);
            }
            statsMap.put(item.getId(), wordPkRecord);
        }
        return getGroupList(orderInfoList, statsMap);
    }

    @Cacheable(value = "stats:word:${#clazzId}")
    public List<RewardResp> statsWordList(Long clazzId) {
        List<Order> orderInfoList = orderMapper.selectList(new QueryWrapper<Order>().select(
                Order.ID, Order.STUDENT_ID, Order.USER_ID, Order.ORDER_REMARK
        ).eq(Order.CLAZZ_ID, clazzId));
        if (orderInfoList.isEmpty()) {
            return Collections.emptyList();
        }
        List<Long> ids = orderInfoList.stream().map(Order::getId).toList();
        Map<Long, Integer> orderStatsMap = wordRewardMapper.selectList(new QueryWrapper<WordReward>().select(
                WordReward.ORDER_ID, WordReward.REWARD_NUM
        ).in(WordReward.ORDER_ID, ids)).stream().collect(Collectors.toMap(WordReward::getOrderId, WordReward::getRewardNum));
        Set<Long> userIds = orderInfoList.stream().map(Order::getUserId).collect(Collectors.toSet());
        Map<Long, User> userMap = userMapper.selectList(new QueryWrapper<User>().select(
                User.ID, User.REAL_NAME, User.USER_ICON
        ).in(User.ID, userIds)).stream().collect(Collectors.toMap(User::getId, item -> item));
        List<RewardResp> respList = new ArrayList<>(orderInfoList.size());
        for (Order item : orderInfoList) {
            RewardResp rewardResp = new RewardResp();
            User user = userMap.get(item.getUserId());
            rewardResp.setRealName(user.getRealName());
            rewardResp.setUserIcon(user.getUserIcon());
            if (StringUtils.hasLength(item.getOrderRemark())) {
                rewardResp.setRealName(item.getOrderRemark());
            }
            rewardResp.setReward(orderStatsMap.getOrDefault(item.getId(), 0));
            respList.add(rewardResp);
        }
        return respList.stream().sorted(Comparator.comparingInt(RewardResp::getReward).reversed()).toList();
    }

    @Cacheable(value = "word:group:${#clazzId}")
    public List<GroupResp> wordGroupList(Long clazzId) {
        List<Order> orderInfoList = orderMapper.selectList(new QueryWrapper<Order>().select(
                Order.ID, Order.GROUP_ID, Order.USER_ID
        ).eq(Order.CLAZZ_ID, clazzId));
        if (orderInfoList.isEmpty()) {
            return Collections.emptyList();
        }
        Map<Long, WordReward> rewardMap = wordRewardMapper.selectList(new QueryWrapper<WordReward>().select(
                WordReward.ORDER_ID, WordReward.REWARD_NUM
                ).in(WordReward.ORDER_ID, orderInfoList.stream().map(Order::getId).toList()))
                .stream().collect(Collectors.toMap(WordReward::getOrderId, v -> v));
        Map<Long, WordPkRecord> statsMap = new HashMap<>(rewardMap.size());
        for (Order item : orderInfoList) {
            WordPkRecord wordPkRecord = new WordPkRecord();
            wordPkRecord.setOrderId(item.getId());
            WordReward wordReward = rewardMap.get(item.getId());
            if (wordReward != null) {
                wordPkRecord.setWinningNum(wordReward.getRewardNum());
            } else {
                wordPkRecord.setWinningNum(0);
            }
            statsMap.put(item.getId(), wordPkRecord);
        }
        return getGroupList(orderInfoList, statsMap);
    }

    @Cacheable(value = "pk:reward:${#pkId}:${#clazzId}")
    public List<RewardResp> pkRewardList(Long pkId, Long clazzId) {
        List<WordPkRecord> recordList = wordPkRecordMapper.selectList(new QueryWrapper<WordPkRecord>().select(
                WordPkRecord.ORDER_ID, WordPkRecord.WINNING_NUM
        ).eq(WordPkRecord.PK_ID, pkId));
        if (recordList.isEmpty()) {
            return Collections.emptyList();
        }
        Map<Long, Integer> orderStatsMap = recordList.stream().collect(Collectors.toMap(WordPkRecord::getOrderId, WordPkRecord::getWinningNum));
        List<Order> orderList = orderMapper.selectList(new QueryWrapper<Order>().select(
                Order.ID, Order.USER_ID, Order.STUDENT_ID, Order.TEACHER_ID, Order.ORDER_REMARK
        ).in(Order.ID, orderStatsMap.keySet()).eq(Order.CLAZZ_ID, clazzId));
        if (orderList.isEmpty()) {
            return Collections.emptyList();
        }
        return getRewardList(orderList, orderStatsMap);
    }

    @Cacheable(value = "pk:group:${#pkId}:${#clazzId}")
    public List<GroupResp> pkGroupList(Long pkId, Long clazzId) {
        Map<Long, WordPkRecord> orderStatsMap = wordPkRecordMapper.selectList(new QueryWrapper<WordPkRecord>().select(
                WordPkRecord.ORDER_ID, WordPkRecord.WINNING_NUM
        ).eq(WordPkRecord.PK_ID, pkId)).stream().collect(Collectors.toMap(WordPkRecord::getOrderId, v -> v));
        if (orderStatsMap.isEmpty()) {
            return Collections.emptyList();
        }
        List<Order> orderList = orderMapper.selectList(new QueryWrapper<Order>().select(
                Order.ID, Order.GROUP_ID, Order.USER_ID
        ).in(Order.ID, orderStatsMap.keySet()).eq(Order.CLAZZ_ID, clazzId));
        if (orderList.isEmpty()) {
            return Collections.emptyList();
        }
        return getGroupList(orderList, orderStatsMap);
    }

    public List<RewardResp> getRewardList(List<Order> orderList, Map<Long, Integer> orderStatsMap) {
        List<Long> studentIds = orderList.stream().map(Order::getStudentId).filter(Objects::nonNull).toList();
        Map<Long, String> studentMap = Collections.emptyMap();
        if (!studentIds.isEmpty()) {
            studentMap = studentMapper.selectList(new QueryWrapper<Student>().select(Student.ID, Student.REAL_NAME).in(Student.ID, studentIds))
                    .stream().collect(Collectors.toMap(Student::getId, Student::getRealName));
        }
        List<Long> userIds = orderList.stream().map(Order::getUserId).filter(Objects::nonNull).toList();
        Map<Long, User> userMap = userMapper.selectList(new QueryWrapper<User>().select(
                User.ID, User.REAL_NAME, User.USER_ICON
        ).in(User.ID, userIds)).stream().collect(Collectors.toMap(User::getId, item -> item));
        List<RewardResp> respList = new ArrayList<>(orderList.size());
        for (Order item : orderList) {
            RewardResp rewardResp = new RewardResp();
            User user = userMap.get(item.getUserId());
            rewardResp.setRealName(user.getRealName());
            rewardResp.setUserIcon(user.getUserIcon());
            String realName = studentMap.get(item.getStudentId());
            if (realName != null) {
                rewardResp.setRealName(realName);
            }
            if (StringUtils.hasLength(item.getOrderRemark())) {
                rewardResp.setRealName(item.getOrderRemark());
            }
            rewardResp.setReward(orderStatsMap.getOrDefault(item.getId(), 0));
            respList.add(rewardResp);
        }
        return respList.stream().sorted(Comparator.comparingInt(RewardResp::getReward).reversed()).toList();
    }

    private List<GroupResp> getGroupList(List<Order> orderList, Map<Long, WordPkRecord> orderStatsMap) {
        List<Long> groupIds = orderList.stream().map(Order::getGroupId).filter(Objects::nonNull).toList();
        if (groupIds.isEmpty()) {
            return Collections.emptyList();
        }
        Map<Long, String> groupMap = groupMapper.selectList(new QueryWrapper<Group>().select(Group.ID, Group.GROUP_NAME).in(Group.ID, groupIds))
                .stream().collect(Collectors.toMap(Group::getId, Group::getGroupName));
        Map<Long, GroupResp> groupRespMap = new HashMap<>(groupIds.size());
        Map<Long, List<WordPkRecord>> groupStatsMap = new HashMap<>(groupIds.size());
        for (Order item : orderList) {
            GroupResp groupResp = groupRespMap.get(item.getGroupId());
            if (groupResp == null) {
                groupResp = new GroupResp();
                groupResp.setGroupId(item.getGroupId());
                groupResp.setGroupName(groupMap.getOrDefault(item.getGroupId(), "未知小组"));
                groupResp.setReward(0);
                groupResp.setUserIconList(new ArrayList<>());
                groupRespMap.put(item.getGroupId(), groupResp);
                groupStatsMap.put(item.getGroupId(), new ArrayList<>());
            }
            WordPkRecord pkRecord = orderStatsMap.computeIfAbsent(item.getId(), k -> {
                WordPkRecord wordPkRecord = new WordPkRecord();
                wordPkRecord.setOrderId(item.getId());
                wordPkRecord.setWinningNum(0);
                return wordPkRecord;
            });
            pkRecord.setPkId(item.getUserId());
            groupStatsMap.get(item.getGroupId()).add(pkRecord);
            groupResp.setReward(groupResp.getReward() + pkRecord.getWinningNum());
        }
        List<GroupResp> respList = groupRespMap.values().stream().sorted(Comparator.comparingInt(GroupResp::getReward).reversed()).toList();
        List<Long> userIds = new ArrayList<>(9);
        for (int i = 0; i < Math.min(3, respList.size()); i++) {
            GroupResp groupResp = respList.get(i);
            List<WordPkRecord> recordList = groupStatsMap.get(groupResp.getGroupId());
            recordList = recordList.stream().sorted(Comparator.comparingInt(WordPkRecord::getWinningNum).reversed()).toList();
            for (int j = 0; j < Math.min(3, recordList.size()); j++) {
                userIds.add(recordList.get(j).getPkId());
            }
        }
        Map<Long, String> userIconMap = userMapper.selectList(new QueryWrapper<User>().select(
                User.ID, User.USER_ICON
        ).in(User.ID, userIds)).stream().collect(Collectors.toMap(User::getId, User::getUserIcon));
        for (int i = 0; i < Math.min(3, respList.size()); i++) {
            GroupResp groupResp = respList.get(i);
            List<WordPkRecord> recordList = groupStatsMap.get(groupResp.getGroupId());
            for (int j = 0; j < Math.min(3, recordList.size()); j++) {
                WordPkRecord pkRecord = recordList.get(j);
                groupResp.getUserIconList().add(userIconMap.get(pkRecord.getPkId()));
            }
        }
        return respList;
    }
}
