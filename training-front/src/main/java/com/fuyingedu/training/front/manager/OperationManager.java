package com.fuyingedu.training.front.manager;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fuyingedu.training.entity.Clazz;
import com.fuyingedu.training.entity.OperationLog;
import com.fuyingedu.training.entity.Order;
import com.fuyingedu.training.mapper.ClazzMapper;
import com.fuyingedu.training.mapper.OperationLogMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
@Slf4j
public class OperationManager {

    @Autowired
    private OperationLogMapper operationLogMapper;
    @Autowired
    private ClazzMapper clazzMapper;

    public void modifySchedule(Long userId, Order prevOrder, Long next) {
        OperationLog operationLog = new OperationLog();
        operationLog.setBatchId(System.currentTimeMillis()).setUserId(userId)
                .setOrderId(prevOrder.getId())
                .setPrevSchedule(prevOrder.getScheduleId())
                .setNextSchedule(next)
                .setPrevTeacher(prevOrder.getTeacherId())
                .setPrevAssistant(getAssistantId(prevOrder.getClazzId()))
                .setOperationType(1);
        operationLogMapper.insert(operationLog);
    }

    public void allocTeacher(Long userId, List<Order> oldOrderList, List<Order> orderList) {
        Long batchId = System.currentTimeMillis();
        Map<Long, Order> oldOrderMap = oldOrderList.stream().collect(Collectors.toMap(Order::getId, Function.identity()));
        Set<Long> clazzIds = orderList.stream().map(Order::getClazzId).filter(Objects::nonNull).collect(Collectors.toSet());
        Map<Long, Clazz> clazzMap = Collections.emptyMap();
        if (!clazzIds.isEmpty()) {
            clazzMap = clazzMapper.selectList(new QueryWrapper<Clazz>().select(
                    Clazz.ID, Clazz.ASSISTANT_ID
            ).in(Clazz.ID, clazzIds)).stream().collect(Collectors.toMap(Clazz::getId, v -> v));
        }
        List<OperationLog> logList = new ArrayList<>(orderList.size());
        for (Order order : orderList) {
            Order oldOrder = oldOrderMap.get(order.getId());
            Long assistantId = null;
            Clazz clazz = clazzMap.get(order.getClazzId());
            if (clazz != null) {
                assistantId = clazz.getAssistantId();
            }
            OperationLog operationLog = new OperationLog();
            operationLog.setBatchId(batchId).setUserId(userId)
                    .setOrderId(order.getId())
                    .setPrevSchedule(oldOrder.getScheduleId())
                    .setNextSchedule(oldOrder.getScheduleId())
                    .setPrevTeacher(oldOrder.getTeacherId())
                    .setNextTeacher(order.getTeacherId())
                    .setPrevAssistant(assistantId)
                    .setOperationType(2);
            logList.add(operationLog);
        }
        operationLogMapper.insert(logList);
    }

    public void cancelOrder(Long userId, Order oldOrder) {
        OperationLog operationLog = new OperationLog();
        operationLog.setBatchId(System.currentTimeMillis()).setUserId(userId)
                .setOrderId(oldOrder.getId())
                .setPrevSchedule(oldOrder.getScheduleId())
                .setPrevTeacher(oldOrder.getTeacherId())
                .setPrevAssistant(getAssistantId(oldOrder.getClazzId()))
                .setOperationType(3);
        operationLogMapper.insert(operationLog);
    }

    public void updateTeacher(Long userId, Order oldOrder, Long teacherId) {
        OperationLog operationLog = new OperationLog();
        operationLog.setBatchId(System.currentTimeMillis()).setUserId(userId)
                .setOrderId(oldOrder.getId())
                .setPrevSchedule(oldOrder.getScheduleId())
                .setNextSchedule(oldOrder.getScheduleId())
                .setPrevTeacher(oldOrder.getTeacherId())
                .setNextTeacher(teacherId)
                .setPrevAssistant(getAssistantId(oldOrder.getClazzId()))
                .setOperationType(6);
        operationLogMapper.insert(operationLog);
    }

    public void allocAssistant(Long userId, List<Order> allocOrderList, List<Order> oldOrderList) {
        Long batchId = System.currentTimeMillis();
        Map<Long, Order> oldOrderMap = oldOrderList.stream().collect(Collectors.toMap(Order::getId, Function.identity()));
        Set<Long> clazzIdSet = new HashSet<>();
        clazzIdSet.addAll(oldOrderList.stream().map(Order::getClazzId).filter(Objects::nonNull).toList());
        clazzIdSet.addAll(allocOrderList.stream().map(Order::getClazzId).filter(Objects::nonNull).toList());
        Map<Long, Clazz> clazzMap = clazzMapper.selectList(new LambdaQueryWrapper<Clazz>().select(
                Clazz::getId, Clazz::getAssistantId
        ).in(Clazz::getId, clazzIdSet)).stream().collect(Collectors.toMap(Clazz::getId, c -> c));
        List<OperationLog> logList = new ArrayList<>();
        for (Order order : allocOrderList) {
            Order oldOrder = oldOrderMap.get(order.getId());
            OperationLog operationLog = new OperationLog()
                    .setBatchId(batchId).setUserId(userId).setOrderId(order.getId()).setOperationType(8)
                    .setPrevSchedule(oldOrder.getScheduleId()).setNextSchedule(oldOrder.getScheduleId())
                    .setPrevContent(oldOrder.getClazzId()).setNextContent(order.getClazzId());
            Long prevAssistantId = null, nextAssistantId = null;
            if (oldOrder.getClazzId() != null) {
                Clazz clazz = clazzMap.get(oldOrder.getClazzId());
                prevAssistantId = clazz.getAssistantId();
            }
            if (order.getClazzId() != null) {
                Clazz clazz = clazzMap.get(order.getClazzId());
                nextAssistantId = clazz.getAssistantId();
            }
            operationLog.setPrevAssistant(prevAssistantId).setNextAssistant(nextAssistantId);
            logList.add(operationLog);
        }
        operationLogMapper.insert(logList);
    }

    public void allocGroup(Long userId, List<Order> allocList, List<Order> oldList) {
        Long batchId = System.currentTimeMillis();
        Map<Long, Order> oldOrderMap = oldList.stream().collect(Collectors.toMap(Order::getId, Function.identity()));
        List<OperationLog> logList = new ArrayList<>();
        for (Order order : allocList) {
            Order oldOrder = oldOrderMap.get(order.getId());
            OperationLog operationLog = new OperationLog();
            operationLog.setBatchId(batchId).setUserId(userId).setOrderId(order.getId()).setOperationType(9)
                    .setPrevSchedule(oldOrder.getScheduleId()).setNextSchedule(oldOrder.getScheduleId())
                    .setPrevContent(oldOrder.getGroupId()).setNextContent(order.getGroupId());
            logList.add(operationLog);
        }
        operationLogMapper.insert(logList);
    }

    public void saveLog(Long userId, Long orderId, Integer type, Long objId) {
        Long batchId = System.currentTimeMillis();
        OperationLog operationLog = new OperationLog();
        operationLog.setBatchId(batchId).setUserId(userId).setOrderId(orderId)
                .setNextContent(objId).setOperationType(type);
        operationLogMapper.insert(operationLog);
    }

    public void saveLog(Long userId, Long orderId, Integer type) {
        saveLog(userId, orderId, type, null);
    }

    public void saveLog(Long userId, Set<Long> orderIds, Integer type) {
        Long batchId = System.currentTimeMillis();
        List<OperationLog> logList = orderIds.stream().map(orderId -> {
            OperationLog operationLog = new OperationLog();
            operationLog.setBatchId(batchId).setUserId(userId).setOrderId(orderId).setOperationType(type);
            return operationLog;
        }).toList();
        operationLogMapper.insert(logList);
    }

    public void saveLog(Long userId, Map<Long, Long> orderMap, Integer type) {
        Long batchId = System.currentTimeMillis();
        List<OperationLog> logList = new ArrayList<>(orderMap.size());
        for (Map.Entry<Long, Long> entry : orderMap.entrySet()) {
            OperationLog operationLog = new OperationLog();
            operationLog.setBatchId(batchId).setUserId(userId).setOrderId(entry.getKey())
                    .setNextContent(entry.getValue()).setOperationType(type);
            logList.add(operationLog);
        }
        operationLogMapper.insert(logList);
    }

    private Long getAssistantId(Long clazzId) {
        Long assistantId = null;
        if (clazzId != null) {
            Clazz clazz = clazzMapper.selectOne(new LambdaQueryWrapper<Clazz>().select(
                    Clazz::getAssistantId, Clazz::getId
            ).eq(Clazz::getId, clazzId));
            assistantId = clazz.getAssistantId();
        }
        return assistantId;
    }
}
