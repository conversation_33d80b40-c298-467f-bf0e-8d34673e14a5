package com.fuyingedu.training.front.feign;

import com.aliyun.openservices.shade.com.alibaba.fastjson.util.ParameterizedTypeImpl;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.type.TypeFactory;
import com.fuyingedu.training.common.exception.WebBaseException;
import feign.FeignException;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import feign.Response;
import feign.codec.DecodeException;
import feign.codec.Decoder;
import feign.codec.ErrorDecoder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpStatus;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.Reader;
import java.lang.reflect.Type;
import java.nio.charset.StandardCharsets;
import java.util.Objects;

@Slf4j
@Configuration
public class FeignConfig {

    @Autowired
    private ObjectMapper objectMapper;

    @Bean
    public RequestInterceptor requestInterceptor() {
        return new RequestInterceptor() {
            @Override
            public void apply(RequestTemplate requestTemplate) {
                requestTemplate.header("Internal-Access", "dHJhaW5pbmctYWRtaW46JEJvMlU2dSY=");
            }
        };
    }

    @Bean
    public Decoder decoder() {
        return new Decoder() {
            @Override
            public Object decode(Response response, Type type) throws IOException, FeignException {
                Reader reader = response.body().asReader(StandardCharsets.UTF_8);
                StringBuilder sb = new StringBuilder();
                BufferedReader bufferedReader = new BufferedReader(reader);
                String str;
                while ((str = bufferedReader.readLine()) != null) {
                    sb.append(str);
                }
                if (type.getTypeName().equals(String.class.getTypeName())) {
                    return sb.toString();
                }
                try {
                    JsonNode jsonNode = objectMapper.readTree(sb.toString());
                    if (!"200".equals(jsonNode.get("code").asText())) {
                        throw new WebBaseException(4000, jsonNode.get("msg").asText());
                    }
                    JsonNode data = jsonNode.get("data");
                    if (data == null) {
                        return null;
                    }
                    JavaType javaType = TypeFactory.defaultInstance().constructType(type);
                    JsonNode dataList = data.get("list");
                    return objectMapper.convertValue(Objects.requireNonNullElse(dataList, data), javaType);
                } catch (Exception e) {
                    log.error("[{}]-[{}]", response.request().url(), sb);
                    throw new WebBaseException(4000, "Feign调用失败", e);
                }
            }
        };
    }

    @Bean
    public ErrorDecoder errorDecoder() {
        return new ErrorDecoder() {
            @Override
            public Exception decode(String methodKey, Response response) {
                String requestUrl = response.request().url();
                int status = response.status();
                String errorMsg  = response.reason();
                try {
                    Reader reader = response.body().asReader(StandardCharsets.UTF_8);
                    JsonNode jsonNode = objectMapper.readTree(reader);
                    JsonNode message = jsonNode.get("message");
                    if (message != null) {
                        errorMsg = message.asText();
                    }
                    log.error("[{}]-[{}]-[{}]", requestUrl, status, jsonNode);
                } catch (Exception e) {
                    log.error("[{}]-[{}]-[{}]", requestUrl, status, errorMsg, e);
                    throw new WebBaseException(4000, "外部接口异常");
                }
                throw new WebBaseException(4000, errorMsg);
            }
        };
    }

}
