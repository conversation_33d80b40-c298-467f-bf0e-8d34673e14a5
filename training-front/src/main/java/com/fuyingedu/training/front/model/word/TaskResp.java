package com.fuyingedu.training.front.model.word;

import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Getter
@Setter
public class TaskResp {

    /**
     * 词书ID
     */
    private Long wordId;
    /**
     * 1 学练测 2 复习 3 直播
     */
    private Byte taskType;

    private String taskName;

    /**
     * 直播名称
     */
    private String liveName;

    /**
     * 直播信息
     */
    private String liveInfo;

    /**
     * 今日完成状态 1-未开始 2-进行中 3-已完成 4-已结束未完成 5-超时完成 (直播 1-未开始 2-已开始 3-已结束（可回看） 4-直播结束（不可回看））
     */
    private Byte taskStatus;

    /**
     * 1 - 已结束 2 - 当天 3 - 未开始
     */
    private Byte taskState;

    /**
     * 第几天的任务
     */
    private Integer days;

    /**
     * 实际任务日期
     */
    private LocalDate realDate;

    /**
     * 任务总天数
     */
    private Integer totalDays;

    /**
     * 开始时间：直播
     */
    private LocalDateTime startTime;

    /**
     * 距离开始时间的秒数
     */
    private Long distance;

    /**
     * 任务积分 小红花
     */
    private Integer taskReward;
    /**
     * Word鹰积分值
     */
    private Integer wordReward;
    /**
     * 直播房间号
     */
    private String liveRoom;

    /**
     * 直播密码
     */
    private String livePassword;

    /**
     * 回放链接
     */
    private String repeatUrl;
    /**
     * 直播方式 1-公司直播 2-导师直播
     * 字典Key：LIVE_TYPE
     */
    private Byte liveType;

    /**
     * 直播端口 1-腾讯会议 2-保利威 3-百家云
     * 字典Key：LIVE_PORT
     */
    private Byte livePort;

    /**
     * 当前完成步骤 1-3  1/2/3 完成了学/练/用 (直播，1-表示签到了直播)
     */
    private Integer finishStep;
}
