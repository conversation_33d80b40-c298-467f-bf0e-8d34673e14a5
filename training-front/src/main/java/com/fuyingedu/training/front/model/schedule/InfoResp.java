package com.fuyingedu.training.front.model.schedule;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;

@ToString
@Getter
@Setter
public class InfoResp {

    /**
     * 1-未开始 2-进行中 3-已结束 4-没有分配导师 5-没有分班
     */
    private Byte scheduleStatus;

    /**
     * 排期名称
     */
    private String scheduleName;

    /**
     * 确认情况 1-未确认 2-自己确认 3-助教确认 4-不来
     */
    private Byte confirmStatus;

    /**
     * 确认时间
     */
    private LocalDateTime confirmTime;

    /**
     * 老师名称
     */
    private String teacherName;

    /**
     * 老师手机号
     */
    private Long phoneNum;

    /**
     * 用户头像
     */
    private String userIcon;

    /**
     * 微信二维码
     */
    private String wxUrl;

    private String wxUserId;

    /**
     * 是否需要绑定扶小鹰 0-不需要 1-需要
     */
    private Byte needFxy;

    /**
     * 是否需要绑定学员 0-不需要 1-需要
     */
    private Byte needBinding;
}
