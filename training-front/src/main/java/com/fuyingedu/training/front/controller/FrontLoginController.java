package com.fuyingedu.training.front.controller;

import com.fuyingedu.training.common.model.CommResp;
import com.fuyingedu.training.common.util.RespUtils;
import com.fuyingedu.training.front.model.login.CodeReq;
import com.fuyingedu.training.front.model.login.LoginReq;
import com.fuyingedu.training.front.model.login.LoginResp;
import com.fuyingedu.training.front.service.FrontLoginService;
import jakarta.validation.Valid;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 登录
 */
@RestController
@RequestMapping("front/login")
public class FrontLoginController {

    @Autowired
    private FrontLoginService frontLoginService;

    /**
     * 登录
     */
    @PostMapping
    public CommResp<LoginResp> login(@Valid @RequestBody LoginReq loginReq) {
        return frontLoginService.login(loginReq);
    }

    /**
     * 发送验证码
     */
    @PostMapping("send/code")
    public CommResp<?> sendCode(@RequestBody @Valid CodeReq codeReq) {
        frontLoginService.sendCode(codeReq);
        return RespUtils.success();
    }

    /**
     * 获取微信SessionKey
     */
    @GetMapping("session/key")
    public CommResp<LoginResp> getSessionKey(@RequestParam("loginCode") String code) throws WxErrorException {
        return frontLoginService.getSessionKey(code);
    }
}
