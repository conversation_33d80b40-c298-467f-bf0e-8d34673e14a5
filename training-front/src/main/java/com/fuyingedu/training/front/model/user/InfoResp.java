package com.fuyingedu.training.front.model.user;

import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
public class InfoResp {

    private Long userId;

    /**
     * 用户头像
     */
    private String userIcon;

    /**
     * 姓名
     */
    private String realName;

    /**
     * 签到次数 -1 表示排期没有配置签到
     */
    private Integer enrollmentNum;

    /**
     * 打卡次数 -1 表示排期没有配置打卡
     */
    private Integer punchNum;

    /**
     * 作业次数 -1 表示排期没有配置作业
     */
    private Integer homeworkNum;

    /**
     * 优秀作业次数
     */
    private Integer goodHomeworkNum;
    /**
     * 任务积分 小红花
     */
    private Integer taskReward;

    /**
     * 单词训练营分数
     */
    private Integer wordReward;
    /**
     * 奖状次数
     */
    private Integer medalNum;

    /**
     * 参加直播次数
     */
    private Long liveNum;

    /**
     * 奖状名称
     */
    private String medalName;
    /**
     * 模板名称
     */
    private String templateName;
    /**
     * 奖状图片
     */
    private String medalIcon;
    /**
     * 奖状备注
     */
    private String mediaContent;

    /**
     * 获取奖状时间
     */
    private LocalDateTime medalTime;

    /**
     * 扶小鹰小太阳
     */
    private Integer fxyReward;
}
