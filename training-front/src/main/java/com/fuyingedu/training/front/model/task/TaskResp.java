package com.fuyingedu.training.front.model.task;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDate;

@ToString
@Getter
@Setter
public class TaskResp {


    private Long id;
    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 任务开始时间
     */
    private LocalDate startDate;
    /**
     * 任务截止时间
     */
    private LocalDate endDate;
    /**
     * 任务积分
     */
    private Integer taskReward;

    /**
     * 扶小鹰小太阳
     */
    private Integer fxyReward;
    /**
     * Word鹰积分值
     */
    private Integer wordReward;


    /**
     * 任务类型 1-签到 2-打卡 3-作业 4-直播
     */
    private Byte taskType;

    /**
     * 完成的次数
     */
    private Integer doneNum;

    /**
     * 作业状态：1 - 未开始 2 - 进行中 3 - 已完成 4 - 已逾期
     */
    private Integer homeworkState;

    /**
     * 总次数
     */
    private Integer totalNum;

}
