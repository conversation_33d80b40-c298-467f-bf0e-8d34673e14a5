package com.fuyingedu.training.front.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fuyingedu.training.common.enums.RespMetaEnum;
import com.fuyingedu.training.common.enums.StudentType;
import com.fuyingedu.training.common.enums.TaskType;
import com.fuyingedu.training.common.exception.WebBaseException;
import com.fuyingedu.training.common.model.CommResp;
import com.fuyingedu.training.common.util.RespUtils;
import com.fuyingedu.training.entity.Group;
import com.fuyingedu.training.entity.Order;
import com.fuyingedu.training.entity.TaskSubmitRecord;
import com.fuyingedu.training.front.model.clazz.GroupResp;
import com.fuyingedu.training.front.model.clazz.HomeworkItemResp;
import com.fuyingedu.training.front.model.group.HomeworkRemarkResp;
import com.fuyingedu.training.mapper.GroupMapper;
import com.fuyingedu.training.mapper.OrderMapper;
import com.fuyingedu.training.mapper.TaskSubmitRecordMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@Slf4j
public class FrontGroupService {

    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private TaskSubmitRecordMapper taskSubmitRecordMapper;
    @Autowired
    private FrontClazzService frontClazzService;
    @Autowired
    private GroupMapper groupMapper;

    public CommResp<HomeworkRemarkResp> homeworkList(Long userId, Long orderId, Byte homeworkType) {
        Order order = orderMapper.selectOne(new QueryWrapper<Order>().select(
                Order.ID, Order.GROUP_ID, Order.STUDENT_TYPE, Order.STUDENT_TYPE, Order.CLAZZ_ID
        ).eq(Order.ID, orderId));
        if (order == null || order.getGroupId() == null
                || !StudentType.VOLUNTEER.getCode().equals(order.getStudentType())) {
            throw new WebBaseException(RespMetaEnum.PARAM_ERROR);
        }
        Set<Long> orderIds = orderMapper.selectList(new QueryWrapper<Order>().select(Order.ID)
                        .eq(StudentType.NORMAL.getCode().equals(order.getStudentType()), Order.GROUP_ID, order.getGroupId())
                        .eq(StudentType.VOLUNTEER.getCode().equals(order.getStudentType()),  Order.CLAZZ_ID, order.getClazzId()))
                .stream().map(Order::getId).collect(Collectors.toSet());
        List<TaskSubmitRecord> taskSubmitRecords = taskSubmitRecordMapper.selectList(new QueryWrapper<TaskSubmitRecord>().select(
                                TaskSubmitRecord.ID,
                                TaskSubmitRecord.TASK_ID,
                                TaskSubmitRecord.ORDER_ID,
                                TaskSubmitRecord.RECORD_TYPE, TaskSubmitRecord.TASK_TYPE,
                                TaskSubmitRecord.SUBMIT_CONTENT,
                                TaskSubmitRecord.SUBMIT_URLS,
                                TaskSubmitRecord.CREATED_TIME, TaskSubmitRecord.LIKE_NUM
                        ).in(TaskSubmitRecord.ORDER_ID, orderIds)
                        .eq(Byte.valueOf((byte) 2).equals(homeworkType), TaskSubmitRecord.RECORD_TYPE, homeworkType)
                        .in(TaskSubmitRecord.TASK_TYPE, TaskType.HOMEWORK.getCode(), TaskType.PUNCH.getCode())
                        .orderByDesc(TaskSubmitRecord.ID)
        );
        HomeworkRemarkResp homeworkRemarkResp = new HomeworkRemarkResp();
        if (taskSubmitRecords == null || taskSubmitRecords.isEmpty()) {
            return RespUtils.success(homeworkRemarkResp);
        }
        List<HomeworkItemResp> respList = frontClazzService.getHomeworkList(orderId, taskSubmitRecords);
        homeworkRemarkResp.setRemarkList(respList.stream().filter(item -> !CollectionUtils.isEmpty(item.getRemarkList())).toList());
        homeworkRemarkResp.setUnRemarkList(respList.stream().filter(item -> CollectionUtils.isEmpty(item.getRemarkList())).toList());
        return RespUtils.success(homeworkRemarkResp);
    }

    public CommResp<List<GroupResp>> list(Long clazzId) {
        List<Group> groupList = groupMapper.selectList(new QueryWrapper<Group>().select(
                Group.ID,
                Group.GROUP_NAME
        ).eq(Group.CLAZZ_ID, clazzId));
        return RespUtils.success(groupList.stream().map(group -> {
            GroupResp itemResp = new GroupResp();
            itemResp.setId(group.getId());
            itemResp.setGroupName(group.getGroupName());
            return itemResp;
        }).toList());
    }
}
