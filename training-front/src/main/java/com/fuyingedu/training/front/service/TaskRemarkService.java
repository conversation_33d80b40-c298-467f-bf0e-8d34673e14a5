package com.fuyingedu.training.front.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.fuyingedu.training.common.enums.RespMetaEnum;
import com.fuyingedu.training.common.enums.StudentType;
import com.fuyingedu.training.common.enums.TaskType;
import com.fuyingedu.training.common.exception.WebBaseException;
import com.fuyingedu.training.common.util.AsyncUtils;
import com.fuyingedu.training.entity.*;
import com.fuyingedu.training.front.manager.OperationManager;
import com.fuyingedu.training.front.manager.WxMaManager;
import com.fuyingedu.training.front.model.media.MediaConvertor;
import com.fuyingedu.training.front.model.task.remark.SaveReq;
import com.fuyingedu.training.mapper.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;

@Service
@Slf4j
public class TaskRemarkService {

    @Autowired
    private TaskRecordRemarkMapper taskRecordRemarkMapper;
    @Autowired
    private TaskRecordLabelMapper taskRecordLabelMapper;
    @Autowired
    private TaskSubmitRecordMapper taskSubmitRecordMapper;
    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private OrderStatisticMapper orderStatisticMapper;
    @Autowired
    private WxMaManager wxMaManager;
    @Autowired
    private OperationManager operationManager;

    @Transactional(rollbackFor = Exception.class)
    public void remark(Long userId, SaveReq saveReq) {
        Order order = orderMapper.selectOne(new QueryWrapper<Order>().select(
                Order.STUDENT_TYPE
                ).eq(Order.ID, saveReq.getOrderId()));
        if (order == null || !StudentType.VOLUNTEER.getCode().equals(order.getStudentType())) {
            throw new WebBaseException(RespMetaEnum.PARAM_ERROR);
        }
        boolean remark = save(userId, saveReq);
        if (remark) {
            orderStatisticMapper.update(new UpdateWrapper<OrderStatistic>()
                    .setSql(String.format("%s = %s + 1", OrderStatistic.REMARK_NUM, OrderStatistic.REMARK_NUM))
                    .eq(OrderStatistic.ORDER_ID, saveReq.getOrderId())
            );
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean save(Long userId, SaveReq saveReq) {
        TaskSubmitRecord record = taskSubmitRecordMapper.selectOne(new QueryWrapper<TaskSubmitRecord>()
                .select(TaskSubmitRecord.ID, TaskSubmitRecord.ORDER_ID, TaskSubmitRecord.TASK_TYPE)
                .eq(TaskSubmitRecord.ID, saveReq.getRecordId()));
        if (record == null) {
            throw new WebBaseException(RespMetaEnum.PARAM_ERROR);
        }
        if (TaskType.PUNCH.getCode().equals(record.getTaskType()) || TaskType.HOMEWORK.getCode().equals(record.getTaskType())) {
            int update = taskSubmitRecordMapper.update(new UpdateWrapper<TaskSubmitRecord>()
                .set(TaskSubmitRecord.RECORD_TYPE, saveReq.getRecordType() == null ? 1 : saveReq.getRecordType())
                .eq(TaskSubmitRecord.ID, saveReq.getRecordId()));
            if (update > 0 && Byte.valueOf((byte) 2).equals(saveReq.getRecordType())) {
                operationManager.saveLog(userId, record.getOrderId(), 17, record.getId());
            }
        }
        if (saveReq.getRemarkId() != null) {
            update(userId, saveReq);
            return false;
        }
        boolean remark = false;
        Order order = orderMapper.selectOne(new QueryWrapper<Order>().select(Order.ID, Order.USER_ID).eq(Order.ID, record.getOrderId()));
        if (StringUtils.hasLength(saveReq.getRemark())) {
            TaskRecordRemark taskRecordRemark = new TaskRecordRemark();
            taskRecordRemark.setRecordId(saveReq.getRecordId());
            taskRecordRemark.setRemarkContent(saveReq.getRemark());
            if (!CollectionUtils.isEmpty(saveReq.getMediaUrlList())) {
                taskRecordRemark.setRemarkUrls(MediaConvertor.getMediaUrls(saveReq.getMediaUrlList()));
            }
            taskRecordRemark.setCreateUserId(userId);
            int insert = taskRecordRemarkMapper.insert(taskRecordRemark);
            if (insert > 0) {
                remark = true;
            }
            if (!CollectionUtils.isEmpty(saveReq.getLabelIdList())) {
                List<TaskRecordLabel> taskLabelList = saveReq.getLabelIdList().stream().map(labelId -> {
                    TaskRecordLabel taskRecordLabel = new TaskRecordLabel();
                    taskRecordLabel.setRemarkId(taskRecordRemark.getId());
                    taskRecordLabel.setLabelId(labelId);
                    taskRecordLabel.setOrderId(order.getId());
                    taskRecordLabel.setCreateUserId(userId);
                    return taskRecordLabel;
                }).toList();
                taskRecordLabelMapper.insert(taskLabelList);
            }
        }
        if (remark) {
            orderStatisticMapper.update(new UpdateWrapper<OrderStatistic>()
                    .setSql(String.format("%s = %s + 1", OrderStatistic.REMARKED_NUM, OrderStatistic.REMARKED_NUM))
                    .eq(OrderStatistic.ORDER_ID, order.getId())
            );
            AsyncUtils.execute(() -> wxMaManager.remarkMessage(saveReq.getRecordId(), userId), "微信消息");
        }
        return remark;
    }

    private void update(Long userId, SaveReq saveReq) {
        if (!StringUtils.hasLength(saveReq.getRemark())) {
            return;
        }
        TaskRecordRemark taskRecordRemark = new TaskRecordRemark();
        taskRecordRemark.setId(saveReq.getRemarkId());
        taskRecordRemark.setRemarkContent(saveReq.getRemark());
        if (!CollectionUtils.isEmpty(saveReq.getMediaUrlList())) {
            taskRecordRemark.setRemarkUrls(MediaConvertor.getMediaUrls(saveReq.getMediaUrlList()));
        }
        taskRecordRemarkMapper.updateById(taskRecordRemark);
        taskRecordLabelMapper.delete(new QueryWrapper<TaskRecordLabel>()
                .eq(TaskRecordLabel.REMARK_ID, saveReq.getRemarkId()));
        if (!CollectionUtils.isEmpty(saveReq.getLabelIdList())) {
            List<TaskRecordLabel> taskLabelList = saveReq.getLabelIdList().stream().map(labelId -> {
                TaskRecordLabel taskRecordLabel = new TaskRecordLabel();
                taskRecordLabel.setRemarkId(taskRecordRemark.getId());
                taskRecordLabel.setLabelId(labelId);
                taskRecordLabel.setOrderId(saveReq.getOrderId());
                taskRecordLabel.setCreateUserId(userId);
                return taskRecordLabel;
            }).toList();
            taskRecordLabelMapper.insert(taskLabelList);
        }
    }
}
