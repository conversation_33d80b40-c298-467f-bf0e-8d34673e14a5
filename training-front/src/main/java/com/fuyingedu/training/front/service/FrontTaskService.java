package com.fuyingedu.training.front.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.fuyingedu.training.common.enums.*;
import com.fuyingedu.training.common.exception.WebBaseException;
import com.fuyingedu.training.common.model.CommResp;
import com.fuyingedu.training.common.util.DateUtils;
import com.fuyingedu.training.common.util.JsonUtils;
import com.fuyingedu.training.common.util.RespUtils;
import com.fuyingedu.training.dto.task.TaskDoneNumRet;
import com.fuyingedu.training.dto.task.UploadItem;
import com.fuyingedu.training.entity.*;
import com.fuyingedu.training.front.manager.BaiJiaYunManager;
import com.fuyingedu.training.front.manager.CheckAuthManager;
import com.fuyingedu.training.front.manager.RewardManager;
import com.fuyingedu.training.front.model.live.LiveConvertor;
import com.fuyingedu.training.front.model.media.MediaConvertor;
import com.fuyingedu.training.front.model.media.MediaResp;
import com.fuyingedu.training.front.model.task.*;
import com.fuyingedu.training.front.model.task.remark.RemarkResp;
import com.fuyingedu.training.front.model.word.TaskItem;
import com.fuyingedu.training.mapper.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class FrontTaskService {

    @Autowired
    private TaskMapper taskMapper;
    @Autowired
    private LiveMapper liveMapper;
    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private TeacherMapper teacherMapper;
    @Autowired
    private ScheduleTeacherMapper scheduleTeacherMapper;
    @Autowired
    private TaskSubmitRecordMapper taskSubmitRecordMapper;
    @Autowired
    private FrontOrderService frontOrderService;
    @Autowired
    private OrderStatisticMapper orderStatisticMapper;
    @Autowired
    private TaskDraftMapper taskDraftMapper;
    @Autowired
    private TaskRecordLabelMapper taskRecordLabelMapper;
    @Autowired
    private TaskRecordRemarkMapper taskRecordRemarkMapper;
    @Autowired
    private LabelMapper labelMapper;
    @Autowired
    private TaskRecordLikeMapper taskRecordLikeMapper;
    @Autowired
    private UserMapper userMapper;
    @Autowired
    private RewardManager rewardManager;
    @Autowired
    private FrontTaskStatisticService frontTaskStatisticService;
    @Autowired
    private CheckAuthManager checkAuthManager;
    @Autowired
    private ScheduleMapper scheduleMapper;
    @Autowired
    private BaiJiaYunManager baiJiaYunManager;
    @Autowired
    private LiveSignMapper liveSignMapper;
    @Autowired
    private FrontWordService frontWordService;

    public CommResp<List<TaskItemResp>> taskList(Long orderId, LocalDate date) {
        if (date == null) {
            date = LocalDate.now();
        }
        LocalDate queryDate = date;
        Order order = orderMapper.selectOne(new QueryWrapper<Order>().select(
                Order.SCHEDULE_ID, Order.ID, Order.USER_ID, Order.TEACHER_ID, Order.CLAZZ_ID, Order.ORDER_REMARK
        ).eq(Order.ID, orderId).in(Order.ORDER_STATUS, OrderStatus.NORMAL.getCode(), OrderStatus.MAY_REFUND.getCode()));
        Schedule schedule = scheduleMapper.selectOne(new QueryWrapper<Schedule>().select(
                Schedule.ID, Schedule.LIVE_TYPE, Schedule.LIVE_PORT, Schedule.LIVE_ROOM
        ).eq(Schedule.ID, order.getScheduleId()));
        List<Task> taskList = getTaskList(order, date, date);
        taskList = taskList.stream().filter(item -> !TaskType.WORD.getCode().equals(item.getTaskType())).toList();
        Map<Byte, List<Task>> typeTaskMap = taskList.stream().collect(Collectors.groupingBy(Task::getTaskType));
        List<TaskItemResp> respList = new ArrayList<>();
        Set<Long> recordSet = Collections.emptySet();
        if (!taskList.isEmpty()) {
            List<Long> taskIds = taskList.stream().map(Task::getId).toList();
            recordSet = taskSubmitRecordMapper.selectList(new QueryWrapper<TaskSubmitRecord>().select(
                                    TaskSubmitRecord.TASK_ID
                            ).eq(TaskSubmitRecord.ORDER_ID, orderId)
                            .and(e -> e.eq(TaskSubmitRecord.SUBMIT_DATE, queryDate).or().eq(TaskSubmitRecord.TASK_TYPE, TaskType.HOMEWORK.getCode()))
                            .in(TaskSubmitRecord.TASK_ID, taskIds))
                    .stream().map(TaskSubmitRecord::getTaskId).collect(Collectors.toSet());
        }
        // 签到
        List<Task> punchTaskList = typeTaskMap.getOrDefault(TaskType.ENROLLMENT.getCode(), Collections.emptyList());
        setRespList(respList, punchTaskList, recordSet);
        // 直播
        List<Live> liveList = liveMapper.selectList(new QueryWrapper<Live>().select(
                        Live.ID, Live.LIVE_NAME, Live.LIVE_ROOM, Live.START_TIME, Live.LIVE_DURATION,
                        Live.REPEAT_START_TIME, Live.REPEAT_END_TIME, Live.LIVE_INFO, Live.LIVE_ROOM,
                        Live.REPEAT_URL, Live.LIVE_PASSWORD
                ).eq(Live.SCHEDULE_ID, order.getScheduleId()).ge(Live.START_TIME, queryDate)
                .lt(Live.START_TIME, queryDate.plusDays(1)).orderByAsc(Live.START_TIME));
        if (!liveList.isEmpty()) {
            String liveInfo = baiJiaYunManager.getLiveInfo(schedule, order);
            Map<Long, LiveSign> liveSignMap = liveSignMapper.selectList(new QueryWrapper<LiveSign>().select(
                            LiveSign.LIVE_ID, LiveSign.TASK_REWARD, LiveSign.FXY_REWARD, LiveSign.WORD_REWARD
                    ).in(LiveSign.LIVE_ID, liveList.stream().map(Live::getId).toList()).eq(LiveSign.SIGN_STATUS, (byte) 2))
                    .stream().collect(Collectors.toMap(LiveSign::getLiveId, Function.identity()));
            respList.addAll(liveList.stream().map(item -> {
                TaskItemResp resp = toTaskItemResp(item);
                LiveSign liveSign = liveSignMap.get(item.getId());
                if (liveSign != null) {
                    resp.setTaskReward(liveSign.getTaskReward());
                    resp.setFxyReward(liveSign.getFxyReward());
                    resp.setWordReward(liveSign.getWordReward());
                }
                resp.setLiveType(schedule.getLiveType());
                resp.setLivePort(schedule.getLivePort());
                if (liveInfo != null) {
                    resp.setLiveInfo(liveInfo);
                }
                return resp;
            }).toList());
        }
        // 打卡
        List<Task> enrollmentTaskList = typeTaskMap.getOrDefault(TaskType.PUNCH.getCode(), Collections.emptyList());
        setRespList(respList, enrollmentTaskList, recordSet);
        // 作业
        List<Task> homeworkTaskList = typeTaskMap.getOrDefault(TaskType.HOMEWORK.getCode(), Collections.emptyList());
        setRespList(respList, homeworkTaskList, recordSet);
        // 接口签到
        List<Task> outTaskList = typeTaskMap.getOrDefault(TaskType.OUT_PUNCH.getCode(), Collections.emptyList());
        setRespList(respList, outTaskList, recordSet);
        for (TaskItemResp item : respList) {
            item.setTaskDate(queryDate);
            if (queryDate.isBefore(LocalDate.now())) {
                item.setDateType((byte) 1);
            } else if (queryDate.isEqual(LocalDate.now())) {
                item.setDateType((byte) 2);
            } else {
                item.setDateType((byte) 3);
            }
        }
        return RespUtils.success(respList);
    }

    private List<Task> getTaskList(Order order, LocalDate startDate, LocalDate endDate) {
        Set<Long> taskIds = frontOrderService.getOrderTaskIds(order);
        if (CollectionUtils.isEmpty(taskIds)) {
            return Collections.emptyList();
        }
        return taskMapper.selectList(new QueryWrapper<Task>().select(
                                Task.ID, Task.TASK_NAME, Task.START_DATE, Task.END_DATE, Task.TASK_REWARD, Task.FXY_REWARD,
                                Task.TASK_TYPE, Task.START_TIME, Task.END_TIME, Task.WORD_REWARD
                        ).in(Task.ID, taskIds)
                        .le(startDate != null, Task.START_DATE, startDate)
                        .ge(endDate != null, Task.END_DATE, endDate)
                        .orderByAsc(Task.ID)
        );
    }

    public CommResp<List<TaskResp>> allTaskListFxy(Long orderId) {
        Order order = orderMapper.selectOne(new QueryWrapper<Order>().select(
                Order.SCHEDULE_ID, Order.ID, Order.USER_ID, Order.TEACHER_ID, Order.CLAZZ_ID
        ).eq(Order.ID, orderId).in(Order.ORDER_STATUS, OrderStatus.NORMAL.getCode(), OrderStatus.MAY_REFUND.getCode()));
        List<TaskResp> respList = new ArrayList<>();
        List<Task> taskList = getTaskList(order, null, null);
        if (taskList.isEmpty()) {
            return RespUtils.success(respList);
        }
        Map<Long, Integer> taskDoneNumMap = taskSubmitRecordMapper.groupTaskDoneNum(orderId, taskList.stream().map(Task::getId).toList())
                .stream().collect(Collectors.toMap(TaskDoneNumRet::getTaskId, TaskDoneNumRet::getNum));
        for (Task task : taskList) {
            TaskResp taskResp = toTaskResp(task);
            Integer downNum = taskDoneNumMap.getOrDefault(task.getId(), 0);
            taskResp.setDoneNum(downNum);
            if (TaskType.HOMEWORK.getCode().equals(task.getTaskType())) {
                if (LocalDateTime.now().isBefore(task.getStartDate().atTime(task.getStartTime()))) {
                    taskResp.setHomeworkState(1);
                } else if (downNum == 1) {
                    taskResp.setHomeworkState(3);
                } else if (LocalDateTime.now().isAfter(task.getEndDate().atTime(task.getEndTime()))) {
                    taskResp.setHomeworkState(4);
                } else {
                    taskResp.setHomeworkState(2);
                }
                taskResp.setTotalNum(1);
            }
            if (!TaskType.WORD.getCode().equals(task.getTaskType())) {
                respList.add(taskResp);
            }
        }
        return RespUtils.success(respList);
    }

    public CommResp<List<TaskResp>> allTaskList(Long userId, Long orderId) {
        Order order = orderMapper.selectOne(new QueryWrapper<Order>().select(
                Order.SCHEDULE_ID, Order.ID, Order.USER_ID, Order.TEACHER_ID, Order.CLAZZ_ID
        ).eq(Order.ID, orderId).in(Order.ORDER_STATUS, OrderStatus.NORMAL.getCode(), OrderStatus.MAY_REFUND.getCode()));
        if (userId != null) {
            checkAuthManager.checkOrder(userId, order.getId(), order.getUserId());
        }
        List<TaskResp> respList = new ArrayList<>();
        List<Live> liveList = liveMapper.selectList(new QueryWrapper<Live>().select(
                Live.START_TIME
        ).eq(Live.SCHEDULE_ID, order.getScheduleId()));
        if (!liveList.isEmpty()) {
            TaskResp liveResp = new TaskResp();
            liveResp.setTaskName("直播计划");
            liveResp.setTaskType((byte) 4);
            liveResp.setTotalNum(liveList.size());
            long count = liveList.stream().filter(live -> live.getStartTime().isBefore(LocalDateTime.now())).count();
            liveResp.setDoneNum((int) count);
            respList.add(liveResp);
        }
        List<Task> taskList = getTaskList(order, null, null);
        if (taskList.isEmpty()) {
            return RespUtils.success(respList);
        }
        Map<Long, Integer> taskDoneNumMap = taskSubmitRecordMapper.groupTaskDoneNum(orderId, taskList.stream().map(Task::getId).toList())
                .stream().collect(Collectors.toMap(TaskDoneNumRet::getTaskId, TaskDoneNumRet::getNum));
        for (Task task : taskList) {
            TaskResp taskResp = toTaskResp(task);
            Integer downNum = taskDoneNumMap.getOrDefault(task.getId(), 0);
            taskResp.setDoneNum(downNum);
            if (TaskType.HOMEWORK.getCode().equals(task.getTaskType())) {
                if (LocalDateTime.now().isBefore(task.getStartDate().atTime(task.getStartTime()))) {
                    taskResp.setHomeworkState(1);
                } else if (downNum == 1) {
                    taskResp.setHomeworkState(3);
                } else if (LocalDateTime.now().isAfter(task.getEndDate().atTime(task.getEndTime()))) {
                    taskResp.setHomeworkState(4);
                } else {
                    taskResp.setHomeworkState(2);
                }
                taskResp.setTotalNum(1);
            }
            if (TaskType.WORD.getCode().equals(task.getTaskType())) {
                List<TaskItem> taskItems = frontWordService.taskList(order.getScheduleId());
                taskResp.setTotalNum(taskItems.size() - 1);
                int days = taskItems.stream().filter(t -> t.getRealDate().isBefore(LocalDate.now())).mapToInt(TaskItem::getDays).max().orElse(0);
                taskResp.setDoneNum(Math.max(days, 0));
                respList.addFirst(taskResp);
            } else {
                respList.add(taskResp);
            }
        }
        return RespUtils.success(respList);
    }

    private TaskResp toTaskResp(Task task) {
        TaskResp taskResp = new TaskResp();
        taskResp.setId(task.getId());
        taskResp.setTaskName(task.getTaskName());
        taskResp.setStartDate(task.getStartDate());
        taskResp.setEndDate(task.getEndDate());
        taskResp.setTaskReward(task.getTaskReward());
        taskResp.setFxyReward(task.getFxyReward());
        taskResp.setWordReward(task.getWordReward());
        taskResp.setTaskType(task.getTaskType());
        taskResp.setTotalNum(DateUtils.days(task.getStartDate(), task.getEndDate()));
        return taskResp;
    }

    private void setRespList(List<TaskItemResp> respList, List<Task> taskList, Set<Long> historyTaskIds) {
        for (Task task : taskList) {
            TaskItemResp taskItemResp = toTaskItemResp(task);
            if (!historyTaskIds.contains(task.getId())) {
                taskItemResp.setTaskStatus((byte) 1);
            } else {
                taskItemResp.setTaskStatus((byte) 2);
            }
            respList.add(taskItemResp);
        }
    }

    private TaskItemResp toTaskItemResp(Live live) {
        TaskItemResp taskItemResp = new TaskItemResp();
        taskItemResp.setId(live.getId());
        taskItemResp.setTaskName(live.getLiveName());
        taskItemResp.setStartDate(live.getStartTime());
        taskItemResp.setTaskStatus(LiveConvertor.getLiveStatus(live));
        taskItemResp.setTaskType((byte) 4);
        LocalDateTime now = LocalDateTime.now();
        taskItemResp.setDistance(DateUtils.seconds(now, live.getStartTime()));
        taskItemResp.setLiveInfo(live.getLiveInfo());
        taskItemResp.setLiveRoom(live.getLiveRoom());
        taskItemResp.setLivePassword(live.getLivePassword());
        taskItemResp.setRepeatUrl(live.getRepeatUrl());
        return taskItemResp;

    }

    private TaskItemResp toTaskItemResp(Task task) {
        TaskItemResp taskItemResp = new TaskItemResp();
        taskItemResp.setId(task.getId());
        taskItemResp.setTaskName(task.getTaskName());
        taskItemResp.setStartDate(task.getStartDate().atStartOfDay());
        if (task.getEndTime() != null) {
            taskItemResp.setEndDate(task.getEndDate().atTime(task.getEndTime()));
        } else {
            taskItemResp.setEndDate(task.getEndDate().atTime(23, 59, 59));
        }
        taskItemResp.setTaskReward(task.getTaskReward());
        taskItemResp.setFxyReward(task.getFxyReward());
        taskItemResp.setWordReward(task.getWordReward());
        taskItemResp.setTaskType(task.getTaskType());
        return taskItemResp;
    }

    @Transactional(rollbackFor = Exception.class)
    public void enrollmentSave(Long userId, EnrollmentSaveReq saveReq) {
        TaskSubmitRecord record = new TaskSubmitRecord();
        record.setSubmitDate(LocalDate.now());
        Task task = checkTask(saveReq.getTaskId(), TaskType.ENROLLMENT);
        saveRecord(userId, saveReq.getOrderId(), task, OrderStatistic.ENROLLMENT_NUM, record);
    }

    private Task checkTask(Long taskId, TaskType taskType) {
        Task task = taskMapper.selectOne(new QueryWrapper<Task>().select(
                Task.ID, Task.FXY_REWARD, Task.TASK_REWARD, Task.WORD_REWARD, Task.TASK_TYPE,
                Task.START_TIME, Task.END_DATE, Task.START_DATE, Task.END_TIME, Task.TASK_NAME
        ).eq(Task.ID, taskId).eq(Task.TASK_TYPE, taskType.getCode()));
        if (task == null) {
            throw new WebBaseException(RespMetaEnum.PARAM_ERROR);
        }
        if (taskType.getCode().equals(TaskType.PUNCH.getCode()) || taskType.getCode().equals(TaskType.ENROLLMENT.getCode())) {
            if (task.getStartDate().isAfter(LocalDate.now())) {
                throw new WebBaseException(4000, "不在任务时间范围内");
            }
            if (task.getEndDate().isBefore(LocalDate.now())) {
                throw new WebBaseException(4000, "不在任务时间范围内");
            }
            if (task.getStartTime() != null && task.getEndTime() != null) {
                if (task.getStartTime().isAfter(LocalTime.now())) {
                    throw new WebBaseException(4000, "不在任务时间范围内");
                } else if (task.getEndTime().isBefore(LocalTime.now())) {
                    throw new WebBaseException(4000, "不在任务时间范围内");
                }
            }
        }
        if (taskType.getCode().equals(TaskType.HOMEWORK.getCode())) {
            if (task.getStartTime() == null || task.getEndTime() == null) {
                throw new WebBaseException(4000, "任务时间未设置");
            }
            LocalDateTime startTime = task.getStartDate().atTime(task.getStartTime());
            LocalDateTime endTime = task.getEndDate().atTime(task.getEndTime());
            if (startTime.isAfter(LocalDateTime.now()) || endTime.isBefore(LocalDateTime.now())) {
                throw new WebBaseException(4000, "不在任务时间范围内");
            }
        }
        return task;
    }

    public CommResp<TaskRecordResp> enrollmentList(Long orderId, Long taskId) {
        return RespUtils.success(getTaskRecordResp(orderId, taskId));
    }

    public CommResp<TaskRecordResp> punchList(Long orderId, Long taskId) {
        return RespUtils.success(getTaskRecordResp(orderId, taskId));
    }

    private TaskRecordResp getTaskRecordResp(Long orderId, Long taskId) {
        Task task = taskMapper.selectOne(new QueryWrapper<Task>().select(
                Task.TASK_NAME, Task.TASK_CONTENT, Task.START_DATE, Task.END_DATE,
                Task.END_TIME, Task.START_TIME, Task.CASE_URLS, Task.UPLOAD_ITEMS, Task.TASK_TYPE
        ).eq(Task.ID, taskId));
        TaskRecordResp taskRecordResp = new TaskRecordResp();
        taskRecordResp.setTaskName(task.getTaskName());
        taskRecordResp.setTaskContent(task.getTaskContent());
        taskRecordResp.setTotalNum(DateUtils.days(task.getStartDate(), task.getEndDate()));
        taskRecordResp.setStartTime(task.getStartTime());
        taskRecordResp.setEndTime(task.getEndTime());
        if (StringUtils.hasLength(task.getCaseUrls())) {
            taskRecordResp.setPunchCaseList(MediaConvertor.getMediaList(task.getCaseUrls()));
        }
        if (!TaskType.OUT_PUNCH.getCode().equals(task.getTaskType()) && StringUtils.hasLength(task.getUploadItems())) {
            taskRecordResp.setUploadItemList(JsonUtils.parseJsonToList(task.getUploadItems(), UploadItem.class));
        }
        LocalDate now = LocalDate.now();
        LocalDate firstDay = task.getStartDate();
        LocalDate lastDay = task.getEndDate().plusDays(1);
        Map<LocalDate, Long> punchDateMap = taskSubmitRecordMapper.selectList(new QueryWrapper<TaskSubmitRecord>().select(
                                TaskSubmitRecord.SUBMIT_DATE, TaskSubmitRecord.ID
                        ).eq(TaskSubmitRecord.TASK_ID, taskId).eq(TaskSubmitRecord.ORDER_ID, orderId)
                        .ge(TaskSubmitRecord.SUBMIT_DATE, firstDay).lt(TaskSubmitRecord.SUBMIT_DATE, lastDay))
                .stream().collect(Collectors.toMap(TaskSubmitRecord::getSubmitDate, TaskSubmitRecord::getId));
        taskRecordResp.setPunchNum(punchDateMap.size());
        List<TaskRecordResp.Item> itemList = new ArrayList<>();
        while (firstDay.isBefore(lastDay)) {
            TaskRecordResp.Item item = new TaskRecordResp.Item();
            item.setPunchDate(firstDay);
            Long punchId = punchDateMap.get(firstDay);
            item.setId(punchId);
            item.setTaskStatus(punchId != null ? (byte) 2 : (byte) 1);
            item.setToday(firstDay.isEqual(now) ? (byte) 1 : (byte) 0);
            itemList.add(item);
            if (firstDay.isEqual(now)) {
                taskRecordResp.setTaskStatus(item.getTaskStatus());
            } else if (firstDay.isAfter(now)) {
                item.setTaskStatus((byte) 3);
            }
            firstDay = firstDay.plusDays(1);
        }
        byte submitStatus = 0;
        if (task.getStartDate().isAfter(LocalDate.now())
                || task.getEndDate().isBefore(LocalDate.now())
                || (task.getStartTime() != null && task.getStartTime().isAfter(LocalTime.now()))
                || (task.getEndTime() != null && task.getEndTime().isBefore(LocalTime.now()))) {
            submitStatus = 1;
        }
        taskRecordResp.setSubmitStatus(submitStatus);
        taskRecordResp.setItemList(itemList);
        return taskRecordResp;
    }

    public void submitTask(RecordSaveReq req) {
        if (TaskType.ENROLLMENT.getCode().equals(req.getTaskType())) {
            EnrollmentSaveReq enrollmentSaveReq = new EnrollmentSaveReq();
            enrollmentSaveReq.setTaskId(req.getTaskId());
            enrollmentSaveReq.setOrderId(req.getOrderId());
            enrollmentSave(null, enrollmentSaveReq);
        } else if (TaskType.PUNCH.getCode().equals(req.getTaskType())) {
            punchSave(null, req);
        } else if (TaskType.HOMEWORK.getCode().equals(req.getTaskType())) {
            homeworkSave(null, req);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void punchSave(Long userId, RecordSaveReq recordSaveReq) {
        TaskSubmitRecord record = new TaskSubmitRecord();
        record.setSubmitDate(LocalDate.now());
        if (!CollectionUtils.isEmpty(recordSaveReq.getPunchUrls())) {
            record.setSubmitUrls(MediaConvertor.getMediaUrls(recordSaveReq.getPunchUrls()));
        } else {
            record.setSubmitUrls(JsonUtils.formatObjToJson(Collections.emptyList()));
        }
        record.setSubmitContent(recordSaveReq.getPunchContent());
        Task task = checkTask(recordSaveReq.getTaskId(), TaskType.PUNCH);
        saveRecord(userId, recordSaveReq.getOrderId(), task, OrderStatistic.PUNCH_NUM, record);
    }

    public void draftSave(RecordSaveReq draftSaveReq) {
        TaskDraft taskDraft = taskDraftMapper.selectOne(new QueryWrapper<TaskDraft>().select(
                TaskDraft.ID
        ).eq(TaskDraft.TASK_ID, draftSaveReq.getTaskId()).eq(TaskDraft.ORDER_ID, draftSaveReq.getOrderId()));
        if (taskDraft == null) {
            taskDraft = new TaskDraft();
            taskDraft.setTaskId(draftSaveReq.getTaskId());
            taskDraft.setOrderId(draftSaveReq.getOrderId());
        }
        taskDraft.setDraftContent(JsonUtils.formatObjToJson(draftSaveReq));
        taskDraftMapper.insertOrUpdate(taskDraft);
    }

    public CommResp<RecordResp> draftDetail(Long orderId, Long taskId) {
        TaskDraft taskDraft = taskDraftMapper.selectOne(new QueryWrapper<TaskDraft>().select(
                TaskDraft.DRAFT_CONTENT
        ).eq(TaskDraft.TASK_ID, taskId).eq(TaskDraft.ORDER_ID, orderId));
        if (taskDraft == null) {
            return RespUtils.success(null);
        }
        return RespUtils.success(JsonUtils.parseJsonToObj(taskDraft.getDraftContent(), RecordResp.class));
    }

    public CommResp<DetailResp> punchDetail(Long id, Long orderId) {
        TaskSubmitRecord punchRecord = taskSubmitRecordMapper.selectOne(new QueryWrapper<TaskSubmitRecord>().select(
                TaskSubmitRecord.SUBMIT_CONTENT, TaskSubmitRecord.SUBMIT_URLS, TaskSubmitRecord.CREATED_TIME, TaskSubmitRecord.LIKE_NUM,
                TaskSubmitRecord.RECORD_TYPE, TaskSubmitRecord.ORDER_ID, TaskSubmitRecord.TASK_ID, TaskSubmitRecord.ID
        ).eq(TaskSubmitRecord.ID, id));
        DetailResp punchDetailResp = toDetailResp(punchRecord);
        if (orderId == null) {
            orderId = punchRecord.getOrderId();
        }
        Task task = taskMapper.selectOne(new QueryWrapper<Task>().select(Task.TASK_NAME).eq(Task.ID, punchRecord.getTaskId()));
        punchDetailResp.setTaskName(task.getTaskName());
        TaskRecordLike taskRecordLike = taskRecordLikeMapper.selectOne(new QueryWrapper<TaskRecordLike>().select(
                TaskRecordLike.ID
        ).eq(TaskRecordLike.RECORD_ID, punchRecord.getId()).eq(TaskRecordLike.ORDER_ID, orderId));
        if (taskRecordLike != null) {
            punchDetailResp.setLike((byte) 1);
        } else {
            punchDetailResp.setLike((byte) 0);
        }
        punchDetailResp.setRemarkList(getRemarkList(orderId, id));
        return RespUtils.success(punchDetailResp);
    }

    public Map<Long, List<RemarkResp>> getRemarkList(Long orderId, List<Long> recordIds) {
        List<TaskRecordRemark> remarkList = taskRecordRemarkMapper.selectList(new QueryWrapper<TaskRecordRemark>().select(
                TaskRecordRemark.ID, TaskRecordRemark.RECORD_ID, TaskRecordRemark.REMARK_CONTENT, TaskRecordRemark.REMARK_URLS,
                TaskRecordRemark.CREATE_USER_ID, TaskRecordRemark.CREATED_TIME
        ).in(TaskRecordRemark.RECORD_ID, recordIds));
        if (remarkList.isEmpty()) {
            return Collections.emptyMap();
        }
        List<Long> userIds = remarkList.stream().map(TaskRecordRemark::getCreateUserId).distinct().toList();
        Map<Long, User> userMap = userMapper.selectList(new QueryWrapper<User>().select(
                User.ID, User.REAL_NAME, User.USER_ICON
        ).in(User.ID, userIds)).stream().collect(Collectors.toMap(User::getId, Function.identity()));
        List<Long> remarkIds = remarkList.stream().map(TaskRecordRemark::getId).toList();
        List<TaskRecordLabel> labelList = taskRecordLabelMapper.selectList(new QueryWrapper<TaskRecordLabel>().select(
                TaskRecordLabel.REMARK_ID, TaskRecordLabel.LABEL_ID
        ).in(TaskRecordLabel.REMARK_ID, remarkIds));
        Map<Long, List<TaskRecordLabel>> recordLabelMap = labelList.stream().collect(Collectors.groupingBy(TaskRecordLabel::getRemarkId));
        Map<Long, Label> labelMap = Collections.emptyMap();
        if (!labelList.isEmpty()) {
            List<Long> labelIds = labelList.stream().map(TaskRecordLabel::getLabelId).distinct().toList();
            labelMap = labelMapper.selectList(new QueryWrapper<Label>().select(
                    Label.ID, Label.LABEL_NAME, Label.MEDIA_URL, Label.LABEL_TYPE
            ).in(Label.ID, labelIds)).stream().collect(Collectors.toMap(Label::getId, Function.identity()));
        }
        Order order = orderMapper.selectOne(new LambdaQueryWrapper<Order>().select(Order::getId, Order::getClazzId, Order::getScheduleId, Order::getGroupId)
                .eq(Order::getId, orderId));
        Order monitor = null;
        if (order.getClazzId() != null && order.getGroupId() != null) {
            monitor = orderMapper.selectOne(new LambdaQueryWrapper<Order>().select(Order::getId, Order::getUserId, Order::getOrderRemark)
                    .eq(Order::getClazzId, order.getClazzId()).eq(Order::getGroupId, order.getGroupId())
                    .eq(Order::getStudentType, 2).last("limit 1"));
        }
        Map<Long, Teacher> teacherMap = teacherMapper.selectList(new QueryWrapper<Teacher>().select(Teacher.ID, Teacher.USER_ID, Teacher.REAL_NAME)
                .in(Teacher.USER_ID, userIds)).stream().collect(Collectors.toMap(Teacher::getUserId, Function.identity()));
        Map<Long, ScheduleTeacher> scheduleTeacherMap = Collections.emptyMap();
        if (!teacherMap.isEmpty()) {
            scheduleTeacherMap = scheduleTeacherMapper.selectList(new LambdaQueryWrapper<ScheduleTeacher>().select(
                                    ScheduleTeacher::getId, ScheduleTeacher::getTeacherId, ScheduleTeacher::getTeacherType
                            ).eq(ScheduleTeacher::getScheduleId, order.getScheduleId())
                            .in(ScheduleTeacher::getTeacherId, teacherMap.values().stream().map(Teacher::getId).toList()))
                    .stream().collect(Collectors.toMap(ScheduleTeacher::getTeacherId, Function.identity()));
        }

        Map<Long, List<RemarkResp>> remarkMap = new HashMap<>(recordIds.size());
        for (TaskRecordRemark remark : remarkList) {
            List<RemarkResp> remarkRespList = remarkMap.computeIfAbsent(remark.getRecordId(), k -> new ArrayList<>());
            RemarkResp remarkResp = new RemarkResp();
            remarkResp.setRemarkContent(remark.getRemarkContent());
            remarkResp.setRemarkUrlList(MediaConvertor.getMediaList(remark.getRemarkUrls()));
            remarkResp.setCreatedTime(remark.getCreatedTime());
            remarkResp.setId(remark.getId());
            List<TaskRecordLabel> labelListByRemark = recordLabelMap.get(remark.getId());
            int goodLabelNum = 0, badLabelNum = 0;
            if (labelListByRemark != null) {
                List<RemarkResp.Label> labelRespList = new ArrayList<>(labelListByRemark.size());
                for (TaskRecordLabel label : labelListByRemark) {
                    RemarkResp.Label labelResp = new RemarkResp.Label();
                    labelResp.setId(label.getLabelId());
                    Label recordLabel = labelMap.get(label.getLabelId());
                    labelResp.setLabelName(recordLabel.getLabelName());
                    labelResp.setMediaUrl(MediaConvertor.getMediaUrl(recordLabel.getMediaUrl()));
                    if (Byte.valueOf((byte) 1).equals(recordLabel.getLabelType())) {
                        goodLabelNum++;
                    } else {
                        badLabelNum++;
                    }
                    labelRespList.add(labelResp);
                }
                remarkResp.setLabelList(labelRespList);
            }
            remarkResp.setGoodLabelNum(goodLabelNum);
            remarkResp.setBadLabelNum(badLabelNum);
            User remarkUser = userMap.get(remark.getCreateUserId());
            remarkResp.setRemarkUserId(remark.getCreateUserId());

            Teacher teacher = teacherMap.get(remark.getCreateUserId());
            if (teacher != null) {
                ScheduleTeacher scheduleTeacher = scheduleTeacherMap.get(teacher.getId());
                if (scheduleTeacher != null) {
                    remarkResp.setRemarkUserName(teacher.getRealName());
                }
            }
            if (remarkResp.getRemarkUserName() == null) {
                if (monitor != null && monitor.getUserId().equals(remark.getCreateUserId()) && StringUtils.hasLength(monitor.getOrderRemark())) {
                    remarkResp.setRemarkUserName(monitor.getOrderRemark());
                } else {
                    remarkResp.setRemarkUserName(remarkUser.getRealName());
                }
            }
            remarkResp.setRemarkUserIcon(remarkUser.getUserIcon());
            remarkRespList.add(remarkResp);
        }
        return remarkMap;
    }

    private List<RemarkResp> getRemarkList(Long orderId, Long recordId) {
        return getRemarkList(orderId, Collections.singletonList(recordId)).get(recordId);
    }

    private DetailResp toDetailResp(TaskSubmitRecord punchRecord) {
        DetailResp detailResp = new DetailResp();
        detailResp.setRecordId(punchRecord.getId());
        detailResp.setLikeNum(punchRecord.getLikeNum());
        detailResp.setRecordType(punchRecord.getRecordType());
        detailResp.setContent(punchRecord.getSubmitContent());
        if (StringUtils.hasLength(punchRecord.getSubmitUrls())) {
            List<MediaResp> mediaList = MediaConvertor.getMediaList(punchRecord.getSubmitUrls());
            detailResp.setMediaList(mediaList);
        }
        detailResp.setCreatedTime(punchRecord.getCreatedTime());
        return detailResp;
    }

    public CommResp<HomeworkResp> homework(Long orderId, Long taskId) {
        Task task = taskMapper.selectOne(new QueryWrapper<Task>().select(
                Task.TASK_NAME, Task.TASK_CONTENT, Task.START_DATE, Task.END_DATE, Task.CASE_URLS
        ).eq(Task.ID, taskId).eq(Task.TASK_TYPE, TaskType.HOMEWORK.getCode()));
        HomeworkResp homeworkResp = new HomeworkResp();
        homeworkResp.setTaskName(task.getTaskName());
        homeworkResp.setTaskContent(task.getTaskContent());
        homeworkResp.setStartDate(task.getStartDate());
        homeworkResp.setEndDate(task.getEndDate());
        if (StringUtils.hasLength(task.getCaseUrls())) {
            homeworkResp.setHomeworkCaseList(MediaConvertor.getMediaList(task.getCaseUrls()));
        }
        TaskSubmitRecord homeworkRecord = taskSubmitRecordMapper.selectOne(new QueryWrapper<TaskSubmitRecord>().select(
                TaskSubmitRecord.ID, TaskSubmitRecord.RECORD_TYPE
        ).eq(TaskSubmitRecord.TASK_ID, taskId).eq(TaskSubmitRecord.ORDER_ID, orderId));
        if (homeworkRecord != null) {
            homeworkResp.setId(homeworkRecord.getId());
            homeworkResp.setTaskStatus(TaskStatus.DONE.getCode());
        } else {
            homeworkResp.setTaskStatus(TaskStatus.UNDO.getCode());
        }
        byte submitStatus = 0;
        if (task.getStartDate().isAfter(LocalDate.now()) || task.getEndDate().isBefore(LocalDate.now())) {
            submitStatus = 1;
        }
        homeworkResp.setSubmitStatus(submitStatus);
        return RespUtils.success(homeworkResp);
    }

    @Transactional(rollbackFor = Exception.class)
    public void homeworkSave(Long userId, RecordSaveReq recordSaveReq) {
        TaskSubmitRecord record = new TaskSubmitRecord();
        record.setSubmitDate(LocalDate.now());
        record.setSubmitContent(recordSaveReq.getPunchContent());
        if (!CollectionUtils.isEmpty(recordSaveReq.getPunchUrls())) {
            record.setSubmitUrls(MediaConvertor.getMediaUrls(recordSaveReq.getPunchUrls()));
        } else {
            record.setSubmitUrls(JsonUtils.formatObjToJson(Collections.emptyList()));
        }
        Task task = checkTask(recordSaveReq.getTaskId(), TaskType.HOMEWORK);
        saveRecord(userId, recordSaveReq.getOrderId(), task, OrderStatistic.HOMEWORK_NUM, record);
    }

    private void saveRecord(Long userId, Long orderId, Task task, String taskType, TaskSubmitRecord record) {
        Order order = orderMapper.selectOne(new QueryWrapper<Order>().select(
                        Order.ID, Order.USER_ID, Order.CLAZZ_ID)
                .eq(Order.ID, orderId)
                .in(Order.ORDER_STATUS, OrderStatus.NORMAL.getCode(), OrderStatus.MAY_REFUND.getCode())
        );
        if (userId == null) {
            userId = order.getUserId();
        }
        record.setUserId(userId);
        checkAuthManager.checkOrder(userId, order.getId(), order.getUserId());
        if (order.getClazzId() == null || order.getClazzId() <= 0) {
            throw new WebBaseException(4000, "您还没有分班，不能进行操作");
        }
        TaskSubmitRecord taskSubmitRecord = taskSubmitRecordMapper.selectOne(new QueryWrapper<TaskSubmitRecord>().select(
                                TaskSubmitRecord.ID
                        ).eq(TaskSubmitRecord.TASK_ID, task.getId())
                        .eq(TaskSubmitRecord.ORDER_ID, orderId)
                        .eq(!TaskType.HOMEWORK.getCode().equals(task.getTaskType()), TaskSubmitRecord.SUBMIT_DATE, LocalDate.now())
        );
        if (taskSubmitRecord != null) {
            if (TaskType.HOMEWORK.getCode().equals(task.getTaskType()) || task.getTaskType().equals(TaskType.PUNCH.getCode())) {
                record.setId(taskSubmitRecord.getId());
                taskSubmitRecordMapper.updateById(record);
                return;
            } else {
                throw new WebBaseException(4000, "不能重复提交");
            }
        }
        record.setClazzId(order.getClazzId());
        record.setOrderId(orderId);
        record.setTaskId(task.getId());
        record.setTaskType(task.getTaskType());
        int insert = taskSubmitRecordMapper.insert(record);
        if (insert > 0) {
            // 增加作业次数和积分
            int update = orderStatisticMapper.update(new UpdateWrapper<OrderStatistic>()
                    .setSql(String.format("%s = %s + 1", taskType, taskType))
                    .eq(OrderStatistic.ORDER_ID, record.getOrderId())
            );
            if (update > 0) {
                rewardManager.saveReward(record.getOrderId(), userId, order.getUserId(),
                        task.getTaskReward(), task.getFxyReward(), task.getWordReward(),
                        String.format("完成'%s'", task.getTaskName()));
            }
            // 添加每天的记录
            frontTaskStatisticService.addRecord(order.getClazzId(), task.getId());
        }
    }


    @Transactional(rollbackFor = Exception.class)
    public CommResp<LikeResp> like(String openId, LikeReq likeReq) {
        Order order = checkAuthManager.checkOrder(openId, likeReq.getOrderId());
        Integer like = like(order.getClazzId(), order.getId(), likeReq.getRecordId());
        return RespUtils.success(new LikeResp(like));
    }

    @Transactional(rollbackFor = Exception.class)
    public CommResp<LikeResp> like(Long userId, LikeReq likeReq) {
        Order order = orderMapper.selectOne(new QueryWrapper<Order>().select(
                Order.CLAZZ_ID, Order.USER_ID, Order.ID
        ).eq(Order.ID, likeReq.getOrderId()));
        checkAuthManager.checkOrder(userId, order.getId(), order.getUserId());
        Integer likeNum = like(order.getClazzId(), order.getId(), likeReq.getRecordId());
        return RespUtils.success(new LikeResp(likeNum));
    }

    private Integer like(Long clazzId, Long orderId, Long recordId) {
        TaskSubmitRecord record = taskSubmitRecordMapper.selectOne(new QueryWrapper<TaskSubmitRecord>().select(
                TaskSubmitRecord.ID
        ).eq(TaskSubmitRecord.ID, recordId).eq(TaskSubmitRecord.CLAZZ_ID, clazzId));
        if (record == null) {
            throw new WebBaseException(RespMetaEnum.PARAM_ERROR);
        }
        TaskRecordLike taskRecordLike = taskRecordLikeMapper.selectOne(new QueryWrapper<TaskRecordLike>().select(
                TaskRecordLike.ID
        ).eq(TaskRecordLike.RECORD_ID, recordId).eq(TaskRecordLike.ORDER_ID, orderId));
        if (taskRecordLike != null) {
            throw new WebBaseException(4000, "不能重复点赞");
        }
        taskRecordLike = new TaskRecordLike();
        taskRecordLike.setRecordId(recordId);
        taskRecordLike.setOrderId(orderId);
        int insert = taskRecordLikeMapper.insert(taskRecordLike);
        if (insert > 0) {
            taskSubmitRecordMapper.update(new UpdateWrapper<TaskSubmitRecord>()
                    .setSql(String.format("%s = %s + 1", TaskSubmitRecord.LIKE_NUM, TaskSubmitRecord.LIKE_NUM))
                    .eq(TaskSubmitRecord.ID, recordId)
            );
        }
        record = taskSubmitRecordMapper.selectOne(new QueryWrapper<TaskSubmitRecord>().select(
                TaskSubmitRecord.LIKE_NUM
        ).eq(TaskSubmitRecord.ID, recordId));
        return record.getLikeNum();
    }

    @Transactional(rollbackFor = Exception.class)
    public CommResp<LikeResp> unlike(String openId, LikeReq likeReq) {
        Order order = checkAuthManager.checkOrder(openId, likeReq.getOrderId());
        Integer likeNum = unlike(order.getId(), likeReq.getRecordId());
        return RespUtils.success(new LikeResp(likeNum));
    }

    @Transactional(rollbackFor = Exception.class)
    public CommResp<LikeResp> unlike(Long userId, LikeReq likeReq) {
        Order order = orderMapper.selectOne(new QueryWrapper<Order>().select(
                Order.ID, Order.USER_ID
        ).eq(Order.ID, likeReq.getOrderId()));
        if (order == null) {
            throw new WebBaseException(RespMetaEnum.NO_AUTH);
        }
        checkAuthManager.checkOrder(userId, order.getId(), order.getUserId());
        Integer unlike = unlike(order.getId(), likeReq.getRecordId());
        return RespUtils.success(new LikeResp(unlike));
    }

    private Integer unlike(Long orderId, Long recordId) {
        TaskRecordLike taskRecordLike = taskRecordLikeMapper.selectOne(new QueryWrapper<TaskRecordLike>().select(
                TaskRecordLike.ID
        ).eq(TaskRecordLike.RECORD_ID, recordId).eq(TaskRecordLike.ORDER_ID, orderId));
        if (taskRecordLike != null) {
            int delete = taskRecordLikeMapper.delete(new QueryWrapper<TaskRecordLike>().eq(TaskRecordLike.ID, taskRecordLike.getId()));
            if (delete > 0) {
                taskSubmitRecordMapper.update(new UpdateWrapper<TaskSubmitRecord>()
                        .setSql(String.format("%s = %s - 1", TaskSubmitRecord.LIKE_NUM, TaskSubmitRecord.LIKE_NUM))
                        .eq(TaskSubmitRecord.ID, recordId)
                );
            }
        }
        TaskSubmitRecord record = taskSubmitRecordMapper.selectOne(new QueryWrapper<TaskSubmitRecord>().select(
                TaskSubmitRecord.LIKE_NUM
        ).eq(TaskSubmitRecord.ID, recordId));
        return record.getLikeNum();
    }

    public CommResp<List<LiveItemResp>> liveList(Long orderId) {
        Order order = orderMapper.selectOne(new QueryWrapper<Order>().select(
                Order.SCHEDULE_ID, Order.USER_ID, Order.ID, Order.CLAZZ_ID, Order.ORDER_REMARK
        ).eq(Order.ID, orderId));
        Schedule schedule = scheduleMapper.selectOne(new QueryWrapper<Schedule>().select(
                Schedule.ID, Schedule.LIVE_PORT, Schedule.LIVE_TYPE, Schedule.LIVE_ROOM).eq(Schedule.ID, order.getScheduleId()));
        List<Live> liveList = liveMapper.selectList(new QueryWrapper<Live>().select(
                Live.ID, Live.LIVE_NAME, Live.START_TIME, Live.LIVE_DURATION,
                Live.REPEAT_START_TIME, Live.REPEAT_END_TIME, Live.LIVE_INFO,
                Live.LIVE_ROOM, Live.LIVE_PASSWORD, Live.REPEAT_URL
        ).eq(Live.SCHEDULE_ID, order.getScheduleId()).orderByAsc(Live.START_TIME));
        Map<Long, LiveSign> liveSignMap = Collections.emptyMap();
        if (!liveList.isEmpty()) {
            liveSignMap = liveSignMapper.selectList(new QueryWrapper<LiveSign>().select(
                    LiveSign.LIVE_ID, LiveSign.TASK_REWARD, LiveSign.FXY_REWARD, LiveSign.WORD_REWARD
            ).in(LiveSign.LIVE_ID, liveList.stream().map(Live::getId).toList()).eq(LiveSign.SIGN_STATUS, (byte) 2))
            .stream().collect(Collectors.toMap(LiveSign::getLiveId, Function.identity()));
        }
        String liveInfo = baiJiaYunManager.getLiveInfo(schedule, order);
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime end = LocalDateTime.of(now.toLocalDate(), LocalTime.MAX);
        List<LiveItemResp> liveItemRespList = new ArrayList<>();
        for (Live live : liveList) {
            LiveItemResp liveItemResp = new LiveItemResp();
            liveItemResp.setLiveName(live.getLiveName());
            liveItemResp.setStartTime(live.getStartTime());
            liveItemResp.setLiveInfo(live.getLiveInfo());
            if (liveInfo != null) {
                liveItemResp.setLiveInfo(liveInfo);
            }
            liveItemResp.setLiveRoom(live.getLiveRoom());
            liveItemResp.setLivePassword(live.getLivePassword());
            liveItemResp.setRepeatUrl(live.getRepeatUrl());
            liveItemResp.setDistance(DateUtils.seconds(now, live.getStartTime()));
            liveItemResp.setLivePort(schedule.getLivePort());
            liveItemResp.setLiveType(schedule.getLiveType());
            if (live.getStartTime().isAfter(end)) {
                // 未开始
                liveItemResp.setLiveStatus((byte) 1);
            } else if (live.getStartTime().isAfter(now)) {
                // 今日开播
                liveItemResp.setLiveStatus((byte) 2);
            } else if (now.isAfter(live.getStartTime()) && live.getStartTime().plusMinutes(live.getLiveDuration()).isAfter(now)) {
                // 直播中
                liveItemResp.setLiveStatus((byte) 3);
            } else if (live.getRepeatStartTime() != null && live.getRepeatStartTime().isBefore(now) && (
                    live.getRepeatEndTime() == null || live.getRepeatEndTime().isAfter(now)
            )) {
                // 直播结束（可回看）
                liveItemResp.setLiveStatus((byte) 4);
            } else {
                liveItemResp.setLiveStatus((byte) 5);
            }
            LiveSign liveSign = liveSignMap.get(live.getId());
            if (liveSign != null) {
                liveItemResp.setWordReward(liveSign.getWordReward());
            } else {
                liveItemResp.setWordReward(0);
            }
            liveItemRespList.add(liveItemResp);
        }
        return RespUtils.success(liveItemRespList);
    }

    public CommResp<DetailResp> homeworkDetail(Long orderId, Long taskId) {
        TaskSubmitRecord homeworkRecord = taskSubmitRecordMapper.selectOne(new QueryWrapper<TaskSubmitRecord>().select(
                TaskSubmitRecord.SUBMIT_CONTENT, TaskSubmitRecord.SUBMIT_URLS, TaskSubmitRecord.CREATED_TIME, TaskSubmitRecord.LIKE_NUM,
                TaskSubmitRecord.RECORD_TYPE, TaskSubmitRecord.ID, TaskSubmitRecord.TASK_ID
        ).eq(TaskSubmitRecord.ORDER_ID, orderId).eq(TaskSubmitRecord.TASK_ID, taskId));
        if (homeworkRecord == null) {
            return RespUtils.success(new DetailResp());
        }
        DetailResp homeworkDetailResp = toDetailResp(homeworkRecord);
        Task task = taskMapper.selectOne(new QueryWrapper<Task>().select(Task.TASK_NAME).eq(Task.ID, homeworkRecord.getTaskId()));
        homeworkDetailResp.setTaskName(task.getTaskName());
        TaskRecordLike taskRecordLike = taskRecordLikeMapper.selectOne(new QueryWrapper<TaskRecordLike>().select(
                TaskRecordLike.ID
        ).eq(TaskRecordLike.RECORD_ID, homeworkRecord.getId()).eq(TaskRecordLike.ORDER_ID, orderId));
        if (taskRecordLike != null) {
            homeworkDetailResp.setLike((byte) 1);
        } else {
            homeworkDetailResp.setLike((byte) 0);
        }
        homeworkDetailResp.setRemarkList(getRemarkList(orderId, homeworkRecord.getId()));
        return RespUtils.success(homeworkDetailResp);
    }

    public CommResp<List<DetailResp>> homeworkList(Long orderId) {
        List<TaskSubmitRecord> recordList = taskSubmitRecordMapper.selectList(new QueryWrapper<TaskSubmitRecord>().select(
                TaskSubmitRecord.SUBMIT_CONTENT, TaskSubmitRecord.SUBMIT_URLS, TaskSubmitRecord.CREATED_TIME, TaskSubmitRecord.LIKE_NUM,
                TaskSubmitRecord.RECORD_TYPE, TaskSubmitRecord.ID, TaskSubmitRecord.TASK_ID
        ).eq(TaskSubmitRecord.ORDER_ID, orderId).eq(TaskSubmitRecord.TASK_TYPE, TaskType.HOMEWORK.getCode()).orderByDesc(TaskSubmitRecord.ID));
        if (recordList.isEmpty()) {
            return RespUtils.success(Collections.emptyList());
        }
        Set<Long> taskIds = recordList.stream().map(TaskSubmitRecord::getTaskId).collect(Collectors.toSet());
        Map<Long, Task> taskMap = taskMapper.selectList(new QueryWrapper<Task>().select(Task.ID, Task.TASK_NAME).in(Task.ID, taskIds))
                .stream().collect(Collectors.toMap(Task::getId, Function.identity()));
        List<Long> recordIds = recordList.stream().map(TaskSubmitRecord::getId).toList();
        Set<Long> likeSet = taskRecordLikeMapper.selectList(new QueryWrapper<TaskRecordLike>().select(
                TaskRecordLike.ID
        ).eq(TaskRecordLike.RECORD_ID, recordIds)).stream().map(TaskRecordLike::getRecordId).collect(Collectors.toSet());
        Map<Long, List<RemarkResp>> remarkMap = getRemarkList(orderId, recordIds);
        List<DetailResp> respList = new ArrayList<>();
        for (TaskSubmitRecord homeworkRecord : recordList) {
            DetailResp homeworkDetailResp = toDetailResp(homeworkRecord);
            Task task = taskMap.get(homeworkRecord.getTaskId());
            homeworkDetailResp.setTaskName(task.getTaskName());
            if (likeSet.contains(homeworkRecord.getId())) {
                homeworkDetailResp.setLike((byte) 1);
            } else {
                homeworkDetailResp.setLike((byte) 0);
            }
            homeworkDetailResp.setRemarkList(remarkMap.get(homeworkRecord.getId()));
            respList.add(homeworkDetailResp);
        }
        return RespUtils.success(respList);
    }
}
