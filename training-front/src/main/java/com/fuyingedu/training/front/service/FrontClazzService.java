package com.fuyingedu.training.front.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fuyingedu.training.common.enums.*;
import com.fuyingedu.training.common.exception.WebBaseException;
import com.fuyingedu.training.common.model.CommResp;
import com.fuyingedu.training.common.util.RespUtils;
import com.fuyingedu.training.entity.*;
import com.fuyingedu.training.front.manager.CheckAuthManager;
import com.fuyingedu.training.front.manager.TeacherManager;
import com.fuyingedu.training.front.model.clazz.*;
import com.fuyingedu.training.front.model.media.MediaConvertor;
import com.fuyingedu.training.front.model.task.remark.RemarkResp;
import com.fuyingedu.training.mapper.*;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.cp.api.WxCpService;
import me.chanjar.weixin.cp.bean.external.WxCpGroupJoinWayInfo;
import me.chanjar.weixin.cp.bean.external.WxCpGroupJoinWayResult;
import me.chanjar.weixin.cp.bean.external.WxCpUserExternalGroupChatInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class FrontClazzService {

    @Autowired
    private TaskRecordLikeMapper taskRecordLikeMapper;
    @Autowired
    private FrontTaskService frontTaskService;
    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private ClazzMapper clazzMapper;
    @Autowired
    private TeacherMapper teacherMapper;
    @Autowired
    private UserMapper userMapper;
    @Autowired
    private GroupMapper groupMapper;
    @Autowired
    private TaskSubmitRecordMapper taskSubmitRecordMapper;
    @Autowired
    private FrontOrderService frontOrderService;
    @Autowired
    private TaskMapper taskMapper;
    @Autowired
    private CheckAuthManager checkAuthManager;
    @Autowired
    private UserWxGroupMapper userWxGroupMapper;
    @Autowired
    private TeacherManager teacherManager;
    @Autowired
    private WxCpService wxCpService;
    @Autowired
    private CampMapper campMapper;

    public CommResp<InfoResp> info(Long orderId) throws WxErrorException {
        Order order = orderMapper.selectOne(new QueryWrapper<Order>().select(
                Order.ID, Order.TEACHER_ID, Order.CLAZZ_ID,
                Order.USER_ID, Order.GROUP_ID, Order.STUDENT_TYPE, Order.CAMP_ID
        ).eq(Order.ID, orderId).in(Order.ORDER_STATUS, OrderStatus.NORMAL.getCode(), OrderStatus.MAY_REFUND.getCode()));
        InfoResp infoResp = new InfoResp();
        infoResp.setUserId(order.getUserId());
        infoResp.setStudentType(order.getStudentType());
        if (order.getClazzId() == null) {
            return RespUtils.success(infoResp);
        }
        Camp camp = campMapper.selectOne(new QueryWrapper<Camp>().select(
                Camp.ID, Camp.WX_PRIORITY
        ).eq(Camp.ID, order.getCampId()));
        Clazz clazz = clazzMapper.selectOne(new QueryWrapper<Clazz>().select(
                Clazz.ID, Clazz.CLASS_NAME, Clazz.WX_URL, Clazz.TEACHER_ID, Clazz.ASSISTANT_ID, Clazz.WX_GROUP_ID, Clazz.WX_URL
        ).eq(Clazz.ID, order.getClazzId()));
        infoResp.setClazzId(clazz.getId());
        infoResp.setClazzName(clazz.getClassName());
        infoResp.setWxUrl(MediaConvertor.getMediaUrl(clazz.getWxUrl()));
        if (WxPriority.TEACHER.getCode().equals(camp.getWxPriority())) {
            infoResp.setTeacherList(getTeacherList(order.getTeacherId(), TeacherType.TEACHER));
        } else {
            infoResp.setTeacherList(getTeacherList(clazz.getAssistantId(), TeacherType.ASSISTANT));
        }
        List<Order> orderList = orderMapper.selectList(new QueryWrapper<Order>().select(
                Order.ID, Order.USER_ID, Order.GROUP_ID, Order.STUDENT_TYPE, Order.ORDER_REMARK
        ).eq(Order.CLAZZ_ID, order.getClazzId()).in(Order.ORDER_STATUS, OrderStatus.NORMAL.getCode(), OrderStatus.MAY_REFUND.getCode()));
        infoResp.setClassmateList(getClassmateList(orderList, order.getId()));
        if (order.getGroupId() != null) {
            Group group = groupMapper.selectOne(new QueryWrapper<Group>().select(
                    Group.GROUP_NAME
            ).eq(Group.ID, order.getGroupId()));
            infoResp.setGroupId(order.getGroupId());
            infoResp.setGroupName(group.getGroupName());
        }
        if (clazz.getWxGroupId() != null) {
            UserWxGroup userWxGroup = userWxGroupMapper.selectOne(new QueryWrapper<UserWxGroup>().select(
                    UserWxGroup.GROUP_ID,
                    UserWxGroup.GROUP_NAME
            ).eq(UserWxGroup.ID, clazz.getWxGroupId()));
            infoResp.setWxGroupId(userWxGroup.getGroupId());
            infoResp.setWxGroupName(userWxGroup.getGroupName());
            if (!StringUtils.hasLength(clazz.getWxUrl())) {
                WxCpGroupJoinWayInfo wayInfo = new WxCpGroupJoinWayInfo();
                WxCpGroupJoinWayInfo.JoinWay joinWay = new WxCpGroupJoinWayInfo.JoinWay();
                joinWay.setScene(1);
                joinWay.setAutoCreateRoom(0);
                joinWay.setChatIdList(Collections.singletonList(userWxGroup.getGroupId()));
                wayInfo.setJoinWay(joinWay);
                WxCpGroupJoinWayResult wxCpGroupJoinWayResult = wxCpService.getExternalContactService().addJoinWay(wayInfo);
                WxCpGroupJoinWayInfo joinWayInfo = wxCpService.getExternalContactService().getJoinWay(wxCpGroupJoinWayResult.getConfigId());
                infoResp.setWxUrl(MediaConvertor.getMediaUrl(joinWayInfo.getJoinWay().getQrCode()));
                clazzMapper.update(new UpdateWrapper<Clazz>().set(Clazz.WX_URL, infoResp.getWxUrl()).eq(Clazz.ID, clazz.getId()));
            }
            if (!StringUtils.hasLength(userWxGroup.getGroupName())) {
                WxCpUserExternalGroupChatInfo groupChat = wxCpService.getExternalContactService().getGroupChat(userWxGroup.getGroupId(), 0);
                infoResp.setGroupName(groupChat.getGroupChat().getName());
                userWxGroupMapper.update(new UpdateWrapper<UserWxGroup>().set(UserWxGroup.GROUP_NAME, infoResp.getGroupName())
                        .eq(UserWxGroup.ID, clazz.getWxGroupId()));
            }
        }
        return RespUtils.success(infoResp);
    }

    private InfoResp.Teacher toTeacher(Teacher teacher, User user, TeacherType teacherType) {
        InfoResp.Teacher respTeacher = new InfoResp.Teacher();
        respTeacher.setTeacherType(teacherType.getCode());
        respTeacher.setRealName(teacher.getRealName());
        respTeacher.setPhoneNum(user.getPhoneNum());
        respTeacher.setWxUserId(teacherManager.getWxUrl(teacher.getId(), user.getPhoneNum(), teacher.getWxUrl()));
        respTeacher.setUserIcon(user.getUserIcon());
        return respTeacher;
    }

    private List<Classmate> getClassmateList(List<Order> orderList, Long myOrderId) {
        if (orderList.isEmpty()) {
            return Collections.emptyList();
        }
        List<Long> userIds = orderList.stream().map(Order::getUserId).toList();
        Map<Long, User> userMap = userMapper.selectList(new QueryWrapper<User>().select(
                User.ID, User.REAL_NAME, User.USER_ICON
        ).in(User.ID, userIds)).stream().collect(Collectors.toMap(User::getId, user -> user));
        Map<Long, String> groupNameMap = Collections.emptyMap();
        List<Long> groupIds = orderList.stream().map(Order::getGroupId).filter(Objects::nonNull).toList();
        if (!groupIds.isEmpty()) {
            groupNameMap = groupMapper.selectList(new QueryWrapper<Group>().select(
                            Group.ID,
                            Group.GROUP_NAME
                    ).in(Group.ID, groupIds))
                    .stream().collect(Collectors.toMap(Group::getId, Group::getGroupName));
        }
        final Map<Long, String> finalGroupNameMap = groupNameMap;
        return orderList.stream().map(order -> {
            Classmate classmate = new Classmate();
            classmate.setOrderId(order.getId());
            classmate.setGroupId(order.getGroupId());
            classmate.setGroupName(finalGroupNameMap.get(order.getGroupId()));
            User user = userMap.get(order.getUserId());
            classmate.setUserId(user.getId());
            classmate.setRealName(user.getRealName());
            if (StringUtils.hasLength(order.getOrderRemark())) {
                classmate.setRealName(order.getOrderRemark());
            }
            classmate.setUserIcon(user.getUserIcon());
            classmate.setMyself(order.getId().equals(myOrderId) ? (byte) 1 : (byte) 0);
            classmate.setStudentType(order.getStudentType());
            return classmate;
        }).sorted((o1, o2) -> {
            if (myOrderId.equals(o1.getOrderId())) {
                return -1;
            } else if (myOrderId.equals(o2.getOrderId())) {
                return 1;
            }
            return o2.getStudentType() - o1.getStudentType();
        }).toList();
    }

    private List<InfoResp.Teacher> getTeacherList(Long teacherId, TeacherType teacherType) {
        Teacher teacher = teacherMapper.selectOne(new QueryWrapper<Teacher>().select(
                Teacher.ID, Teacher.REAL_NAME, Teacher.WX_URL, Teacher.USER_ID
        ).eq(Teacher.ID, teacherId));
        User user = userMapper.selectOne(new QueryWrapper<User>().select(
                User.ID, User.USER_ICON, User.PHONE_NUM
        ).in(User.ID, teacher.getUserId()));
        return Collections.singletonList(toTeacher(teacher, user, teacherType));
    }

    public void groupUpdate(Long userId, GroupInfoResp groupInfoResp) {
        Order order = orderMapper.selectOne(new QueryWrapper<Order>().select(
                Order.ID, Order.USER_ID
        ).eq(Order.ID, groupInfoResp.getOrderId()).in(Order.ORDER_STATUS, OrderStatus.NORMAL.getCode(), OrderStatus.MAY_REFUND.getCode())
                .eq(Order.STUDENT_TYPE, StudentType.VOLUNTEER.getCode()).eq(Order.GROUP_ID, groupInfoResp.getGroupId())
        );
        if (order == null) {
            throw new WebBaseException(4000, "没有修改小组名称的权限");
        }
        checkAuthManager.checkOrder(userId, order.getId(), order.getUserId());
        groupMapper.update(new UpdateWrapper<Group>().set(Group.GROUP_NAME, groupInfoResp.getGroupName())
                .eq(Group.ID, groupInfoResp.getGroupId()));
    }


    public CommResp<List<HomeworkItemResp>> classmateHomeworkList(ClassmateHomeworkListReq homeworkListReq) {
        IPage<TaskSubmitRecord> page = new Page<>(homeworkListReq.getPageNum(), homeworkListReq.getPageSize());
        page = taskSubmitRecordMapper.selectPage(page, new QueryWrapper<TaskSubmitRecord>().select(
                                TaskSubmitRecord.ID,
                                TaskSubmitRecord.TASK_ID,
                                TaskSubmitRecord.ORDER_ID,
                                TaskSubmitRecord.RECORD_TYPE,
                                TaskSubmitRecord.SUBMIT_CONTENT,
                                TaskSubmitRecord.SUBMIT_URLS,
                                TaskSubmitRecord.CREATED_TIME, TaskSubmitRecord.LIKE_NUM
                        ).eq(TaskSubmitRecord.ORDER_ID, homeworkListReq.getClassmateOrderId())
                        .in(TaskSubmitRecord.TASK_TYPE, TaskType.HOMEWORK.getCode(), TaskType.PUNCH.getCode())
                        .orderByDesc(Order.ID)
        );
        return getHomeworkList(page, homeworkListReq.getOrderId());
    }

    public CommResp<List<HomeworkItemResp>> homeworkList(HomeworkListReq homeworkListReq) {
        Order order = orderMapper.selectOne(new QueryWrapper<Order>().select(
                Order.ID, Order.CLAZZ_ID, Order.SCHEDULE_ID, Order.TEACHER_ID, Order.GROUP_ID,
                Order.USER_ID, Order.STUDENT_TYPE, Order.STUDENT_ID
        ).eq(Order.ID, homeworkListReq.getOrderId()).in(Order.ORDER_STATUS, OrderStatus.NORMAL.getCode(), OrderStatus.MAY_REFUND.getCode()));
        if (order.getClazzId() == null) {
            return RespUtils.success(Collections.emptyList());
        }
        Set<Long> orderIds = null;
        if (homeworkListReq.getGroupId() != null) {
            orderIds = orderMapper.selectList(new QueryWrapper<Order>().select(
                            Order.ID
                    ).eq(Order.CLAZZ_ID, order.getClazzId()).eq(Order.GROUP_ID, homeworkListReq.getGroupId()))
                    .stream().map(Order::getId).collect(Collectors.toSet());
            if (orderIds.isEmpty()) {
                return RespUtils.success(Collections.emptyList());
            }
        }
        if (StringUtils.hasLength(homeworkListReq.getUserName())) {
            orderIds = orderMapper.selectList(new QueryWrapper<Order>().select(
                    Order.ID
            ).eq(Order.CLAZZ_ID, order.getClazzId())
            .in(orderIds != null, Order.ID, orderIds)
            .like(Order.ORDER_REMARK, homeworkListReq.getUserName()))
                    .stream().map(Order::getId).collect(Collectors.toSet());
            if (orderIds.isEmpty()) {
                return RespUtils.success(Collections.emptyList());
            }
        }
        Set<Long> taskIds = null;
        if (StringUtils.hasLength(homeworkListReq.getHomeworkName())) {
            taskIds = frontOrderService.getOrderTaskIds(order);
            if (taskIds.isEmpty()) {
                return RespUtils.success(Collections.emptyList());
            }
            taskIds = taskMapper.selectList(new QueryWrapper<Task>().select(
                    Task.ID
            ).in(Task.ID, taskIds)
            .like(Task.TASK_NAME, homeworkListReq.getHomeworkName()))
                    .stream().map(Task::getId).collect(Collectors.toSet());
            if (taskIds.isEmpty()) {
                return RespUtils.success(Collections.emptyList());
            }
        }
        IPage<TaskSubmitRecord> page = new Page<>(homeworkListReq.getPageNum(), homeworkListReq.getPageSize());
        page = taskSubmitRecordMapper.selectPage(page, new QueryWrapper<TaskSubmitRecord>().select(
                                TaskSubmitRecord.ID,
                                TaskSubmitRecord.TASK_ID,
                                TaskSubmitRecord.ORDER_ID,
                                TaskSubmitRecord.RECORD_TYPE, TaskSubmitRecord.TASK_TYPE,
                                TaskSubmitRecord.SUBMIT_CONTENT,
                                TaskSubmitRecord.SUBMIT_URLS,
                                TaskSubmitRecord.CREATED_TIME, TaskSubmitRecord.LIKE_NUM
                        ).eq(TaskSubmitRecord.CLAZZ_ID, order.getClazzId())
                        .eq(Byte.valueOf((byte) 2).equals(homeworkListReq.getHomeworkType()),
                                TaskSubmitRecord.RECORD_TYPE, homeworkListReq.getHomeworkType())
                        .in(taskIds != null, TaskSubmitRecord.TASK_ID, taskIds)
                        .in(TaskSubmitRecord.TASK_TYPE, TaskType.HOMEWORK.getCode(), TaskType.PUNCH.getCode())
                        .in(orderIds != null, TaskSubmitRecord.ORDER_ID, orderIds)
                        .orderBy(true, "asc".equals(homeworkListReq.getSortOrder()), homeworkListReq.getSortField())
        );
        return getHomeworkList(page, homeworkListReq.getOrderId());
    }

    public CommResp<List<HomeworkItemResp>> myHomeworkList(Long orderId) {
        List<TaskSubmitRecord> recordList = taskSubmitRecordMapper.selectList(new QueryWrapper<TaskSubmitRecord>().select(
                                TaskSubmitRecord.ID,
                                TaskSubmitRecord.TASK_ID,
                                TaskSubmitRecord.ORDER_ID,
                                TaskSubmitRecord.RECORD_TYPE, TaskSubmitRecord.TASK_TYPE,
                                TaskSubmitRecord.SUBMIT_CONTENT,
                                TaskSubmitRecord.SUBMIT_URLS,
                                TaskSubmitRecord.CREATED_TIME, TaskSubmitRecord.LIKE_NUM
                        ).eq(TaskSubmitRecord.ORDER_ID, orderId)
                        .in(TaskSubmitRecord.TASK_TYPE, TaskType.HOMEWORK.getCode(), TaskType.PUNCH.getCode())
                        .orderByDesc(TaskSubmitRecord.ID)
        );
        List<HomeworkItemResp> respList = getHomeworkList(orderId, recordList);
        return RespUtils.success(respList);
    }

    private CommResp<List<HomeworkItemResp>> getHomeworkList(IPage<TaskSubmitRecord> page, Long myOrderId) {
        if (page.getRecords().isEmpty()) {
            return RespUtils.success(page.getCurrent(), page.getSize(), page.getTotal(), Collections.emptyList());
        }
        List<HomeworkItemResp> respList = getHomeworkList(myOrderId, page.getRecords());
        return RespUtils.success(page.getCurrent(), page.getSize(), page.getTotal(), respList);
    }

    public List<HomeworkItemResp> getHomeworkList(Long myOrderId, List<TaskSubmitRecord> taskSubmitRecords) {
        if (taskSubmitRecords.isEmpty()) {
            return Collections.emptyList();
        }
        List<Long> taskIds = taskSubmitRecords.stream().map(TaskSubmitRecord::getTaskId).distinct().toList();
        Map<Long, Task> taskMap = taskMapper.selectList(new QueryWrapper<Task>().select(
                Task.ID, Task.TASK_NAME
        ).in(Task.ID, taskIds)).stream().collect(Collectors.toMap(Task::getId, v -> v));
        List<Long> orderIds = taskSubmitRecords.stream().map(TaskSubmitRecord::getOrderId).distinct().toList();
        Map<Long, Order> orderMap = orderMapper.selectList(new QueryWrapper<Order>().select(
                Order.ID, Order.USER_ID, Order.ORDER_REMARK, Order.GROUP_ID
        ).in(Order.ID, orderIds)).stream().collect(Collectors.toMap(Order::getId, v -> v));
        Map<Long, User> userMap = userMapper.selectList(new QueryWrapper<User>().select(
                        User.ID, User.REAL_NAME, User.USER_ICON
                ).in(User.ID, orderMap.values().stream().map(Order::getUserId).toList()))
                .stream().collect(Collectors.toMap(User::getId, user -> user));
        Map<Long, Group> groupMap = groupMapper.selectList(new QueryWrapper<Group>().select(
                Group.ID, Group.GROUP_NAME
        ).in(Group.ID, orderMap.values().stream().map(Order::getGroupId).toList()))
                .stream().collect(Collectors.toMap(Group::getId, group -> group));
        List<Long> recordIds = taskSubmitRecords.stream().map(TaskSubmitRecord::getId).toList();
        Map<Long, List<RemarkResp>> remarkMap = frontTaskService.getRemarkList(myOrderId, recordIds);
        Set<Long> likeTaskIds = taskRecordLikeMapper.selectList(new QueryWrapper<TaskRecordLike>().select(
                        TaskRecordLike.RECORD_ID
                ).eq(TaskRecordLike.ORDER_ID, myOrderId).in(TaskRecordLike.RECORD_ID, recordIds))
                .stream().map(TaskRecordLike::getRecordId).collect(Collectors.toSet());
        return taskSubmitRecords.stream().map(homeworkRecord -> {
            Task task = taskMap.get(homeworkRecord.getTaskId());
            HomeworkItemResp homeworkItemResp = toHomeworkItemResp(homeworkRecord, task);
            Order order = orderMap.get(homeworkRecord.getOrderId());
            User user = userMap.get(order.getUserId());
            homeworkItemResp.setRealName(user.getRealName());
            if (StringUtils.hasLength(order.getOrderRemark())) {
                homeworkItemResp.setRealName(order.getOrderRemark());
            }
            homeworkItemResp.setUserIcon(user.getUserIcon());
            homeworkItemResp.setMyself((byte) (homeworkRecord.getOrderId().equals(myOrderId) ? 1 : 0));
            homeworkItemResp.setLike(likeTaskIds.contains(homeworkRecord.getId()) ? (byte) 1 : 0);
            homeworkItemResp.setRemarkList(remarkMap.get(homeworkRecord.getId()));
            homeworkItemResp.setGroupId(order.getGroupId());
            Group group = groupMap.get(order.getGroupId());
            if (group != null) {
                homeworkItemResp.setGroupName(group.getGroupName());
            }
            return homeworkItemResp;
        }).toList();
    }

    private HomeworkItemResp toHomeworkItemResp(TaskSubmitRecord homeworkRecord, Task task) {
        HomeworkItemResp homeworkItemResp = new HomeworkItemResp();
        homeworkItemResp.setId(homeworkRecord.getId());
        homeworkItemResp.setTaskName(task.getTaskName());
        homeworkItemResp.setCreatedTime(homeworkRecord.getCreatedTime());
        homeworkItemResp.setContent(homeworkRecord.getSubmitContent());
        homeworkItemResp.setLikeNum(homeworkRecord.getLikeNum());
        homeworkItemResp.setHomeworkType(homeworkRecord.getRecordType());
        homeworkItemResp.setTaskType(homeworkRecord.getTaskType());
        if (StringUtils.hasLength(homeworkRecord.getSubmitUrls())) {
            homeworkItemResp.setMediaUrlList(MediaConvertor.getMediaList(homeworkRecord.getSubmitUrls()));
        }
        return homeworkItemResp;
    }

    public CommResp<List<LikeResp>> likeList(Long recordId) {
        List<TaskRecordLike> likeList = taskRecordLikeMapper.selectList(new QueryWrapper<TaskRecordLike>().select(
                TaskRecordLike.ORDER_ID, TaskRecordLike.CREATED_TIME
        ).eq(TaskRecordLike.RECORD_ID, recordId).orderByDesc(TaskRecordLike.ID));
        if (CollectionUtils.isEmpty(likeList)) {
            return RespUtils.success(Collections.emptyList());
        }
        List<Long> orderIds = likeList.stream().map(TaskRecordLike::getOrderId).distinct().toList();
        Map<Long, Order> orderMap = orderMapper.selectList(new QueryWrapper<Order>().select(
                Order.ID, Order.USER_ID, Order.ORDER_REMARK
        ).in(Order.ID, orderIds)).stream().collect(Collectors.toMap(Order::getId, Function.identity()));
        List<Long> userIds = orderMap.values().stream().map(Order::getUserId).distinct().toList();
        Map<Long, User> userMap = userMapper.selectList(new QueryWrapper<User>().select(
                User.ID, User.REAL_NAME, User.USER_ICON
        ).in(User.ID, userIds)).stream().collect(Collectors.toMap(User::getId, Function.identity()));
        List<LikeResp> likeRespList = new ArrayList<>();
        for (TaskRecordLike like : likeList) {
            LikeResp likeResp = new LikeResp();
            likeResp.setCreatedTime(like.getCreatedTime());
            Order order = orderMap.get(like.getOrderId());
            User user = userMap.get(order.getUserId());
            if (StringUtils.hasLength(order.getOrderRemark())) {
                likeResp.setUserName(order.getOrderRemark());
            } else {
                likeResp.setUserName(user.getRealName());
            }
            likeResp.setUserIcon(user.getUserIcon());
            likeRespList.add(likeResp);
        }
        return RespUtils.success(likeRespList);
    }

    public CommResp<HomeworkItemResp> homeworkDetail(Long myOrderId, Long recordId) {
        TaskSubmitRecord record = taskSubmitRecordMapper.selectOne(new QueryWrapper<TaskSubmitRecord>().select(
                                TaskSubmitRecord.ID,
                                TaskSubmitRecord.TASK_ID,
                                TaskSubmitRecord.ORDER_ID,
                                TaskSubmitRecord.RECORD_TYPE, TaskSubmitRecord.TASK_TYPE,
                                TaskSubmitRecord.SUBMIT_CONTENT,
                                TaskSubmitRecord.SUBMIT_URLS,
                                TaskSubmitRecord.CREATED_TIME, TaskSubmitRecord.LIKE_NUM
                        ).eq(TaskSubmitRecord.ID, recordId)
        );
        Task task = taskMapper.selectOne(new QueryWrapper<Task>().select(
                Task.ID, Task.TASK_NAME
        ).eq(Task.ID, record.getTaskId()));
        Order order = orderMapper.selectOne(new QueryWrapper<Order>().select(
                Order.ID, Order.USER_ID, Order.ORDER_REMARK
        ).eq(Order.ID, record.getOrderId()));
        User user = userMapper.selectOne(new QueryWrapper<User>().select(
                User.ID, User.REAL_NAME, User.USER_ICON
        ).eq(User.ID, order.getUserId()));
        Map<Long, List<RemarkResp>> remarkMap = frontTaskService.getRemarkList(myOrderId, Collections.singletonList(recordId));
        Set<Long> likeTaskIds = taskRecordLikeMapper.selectList(new QueryWrapper<TaskRecordLike>().select(
                        TaskRecordLike.RECORD_ID
                ).eq(TaskRecordLike.ORDER_ID, myOrderId).eq(TaskRecordLike.RECORD_ID, recordId))
                .stream().map(TaskRecordLike::getRecordId).collect(Collectors.toSet());
        HomeworkItemResp homeworkItemResp = toHomeworkItemResp(record, task);
        homeworkItemResp.setRealName(user.getRealName());
        if (StringUtils.hasLength(order.getOrderRemark())) {
            homeworkItemResp.setRealName(order.getOrderRemark());
        }
        homeworkItemResp.setUserIcon(user.getUserIcon());
        homeworkItemResp.setMyself((byte) (record.getOrderId().equals(myOrderId) ? 1 : 0));
        homeworkItemResp.setLike(likeTaskIds.contains(record.getId()) ? (byte) 1 : 0);
        homeworkItemResp.setRemarkList(remarkMap.get(record.getId()));
        return RespUtils.success(homeworkItemResp);
    }
}
