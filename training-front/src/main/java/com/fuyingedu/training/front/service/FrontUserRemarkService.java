package com.fuyingedu.training.front.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fuyingedu.training.common.model.CommResp;
import com.fuyingedu.training.common.model.PageReq;
import com.fuyingedu.training.common.util.RespUtils;
import com.fuyingedu.training.entity.UserRemarkCommon;
import com.fuyingedu.training.mapper.UserRemarkCommonMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class FrontUserRemarkService {

    @Autowired
    private UserRemarkCommonMapper userRemarkCommonMapper;

    public void addCommonRemark(Long userId, String content) {
        UserRemarkCommon userRemarkCommon = new UserRemarkCommon();
        userRemarkCommon.setUserId(userId);
        userRemarkCommon.setRemarkContent(content);
        userRemarkCommonMapper.insert(userRemarkCommon);
    }

    public void deleteCommonRemark(Long userId, Long commonRemarkId) {
        userRemarkCommonMapper.delete(new QueryWrapper<UserRemarkCommon>()
                .eq(UserRemarkCommon.ID, commonRemarkId).eq(UserRemarkCommon.USER_ID, userId));
    }

    public CommResp<List<UserRemarkCommon>> commonRemarkList(Long userId, PageReq pageReq) {
        IPage<UserRemarkCommon> page = new Page<>(pageReq.getPageNum(), pageReq.getPageSize());
        page = userRemarkCommonMapper.selectPage(page, new QueryWrapper<UserRemarkCommon>().select(
                UserRemarkCommon.ID, UserRemarkCommon.REMARK_CONTENT
        ).eq(UserRemarkCommon.USER_ID, userId).orderByDesc(UserRemarkCommon.ID));
        return RespUtils.success(page.getCurrent(), page.getSize(), page.getTotal(), page.getRecords());
    }

}
