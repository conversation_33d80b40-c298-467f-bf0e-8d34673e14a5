package com.fuyingedu.training.front.controller;

import com.fuyingedu.training.common.annotation.Login;
import com.fuyingedu.training.common.model.CommResp;
import com.fuyingedu.training.common.model.PageReq;
import com.fuyingedu.training.common.util.RespUtils;
import com.fuyingedu.training.entity.UserRemarkCommon;
import com.fuyingedu.training.front.model.user.remark.CommonAddReq;
import com.fuyingedu.training.front.model.user.remark.DeleteReq;
import com.fuyingedu.training.front.service.FrontUserRemarkService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 常用评语
 */
@RestController
@RequestMapping("front/user/remark")
public class FrontUserRemarkController {

    @Autowired
    private FrontUserRemarkService frontUserRemarkService;

    /**
     * 添加常用评语
     */
    @PostMapping("common/add")
    public CommResp<?> addCommonRemark(@Login Long userId, @RequestBody CommonAddReq addReq) {
        frontUserRemarkService.addCommonRemark(userId, addReq.getRemarkContent());
        return RespUtils.success();
    }

    /**
     * 我的常用评语列表
     */
    @GetMapping("common/list")
    public CommResp<List<UserRemarkCommon>> commonRemarkList(@Login Long userId, PageReq pageReq) {
        return frontUserRemarkService.commonRemarkList(userId, pageReq);
    }

    /**
     * 删除常用评语
     */
    @PostMapping("delete/common")
    public CommResp<?> deleteCommonRemark(@Login Long userId, @RequestBody DeleteReq deleteReq) {
        frontUserRemarkService.deleteCommonRemark(userId, deleteReq.getRemarkCommonId());
        return RespUtils.success();
    }
}
