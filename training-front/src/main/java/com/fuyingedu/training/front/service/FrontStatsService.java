package com.fuyingedu.training.front.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fuyingedu.training.common.exception.WebBaseException;
import com.fuyingedu.training.common.model.CommResp;
import com.fuyingedu.training.common.util.RespUtils;
import com.fuyingedu.training.entity.Clazz;
import com.fuyingedu.training.entity.Order;
import com.fuyingedu.training.entity.Schedule;
import com.fuyingedu.training.front.manager.StatsManager;
import com.fuyingedu.training.front.model.stats.GroupResp;
import com.fuyingedu.training.front.model.stats.RewardResp;
import com.fuyingedu.training.front.model.stats.ScheduleResp;
import com.fuyingedu.training.mapper.ClazzMapper;
import com.fuyingedu.training.mapper.OrderMapper;
import com.fuyingedu.training.mapper.ScheduleMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Comparator;
import java.util.List;

@Service
public class FrontStatsService {

    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private StatsManager statsManager;
    @Autowired
    private ScheduleMapper scheduleMapper;
    @Autowired
    private ClazzMapper clazzMapper;

    public CommResp<ScheduleResp<RewardResp>> reward(Long orderId) {
        Order order = orderMapper.selectOne(new QueryWrapper<Order>().select(
                Order.CLAZZ_ID, Order.ID, Order.SCHEDULE_ID
        ).eq(Order.ID, orderId));
        if (order == null || order.getClazzId() == null) {
            throw new WebBaseException(4000, "没有分班，无法统计");
        }
        List<RewardResp> respList = statsManager.statsWordList(order.getClazzId());
        respList = respList.stream().sorted(Comparator.comparingInt(RewardResp::getReward).reversed()).toList();
        return RespUtils.success(setScheduleInfo(order.getScheduleId(), respList));
    }

    public CommResp<ScheduleResp<RewardResp>> rewardByClazzId(Long clazzId) {
        Clazz clazz = clazzMapper.selectOne(new QueryWrapper<Clazz>().select(
                Clazz.ID, Clazz.SCHEDULE_ID
        ).eq(Clazz.ID, clazzId));
        List<RewardResp> respList = statsManager.statsWordList(clazz.getId());
        respList = respList.stream().sorted(Comparator.comparingInt(RewardResp::getReward).reversed()).toList();
        return RespUtils.success(setScheduleInfo(clazz.getScheduleId(), respList));
    }

    public CommResp<ScheduleResp<GroupResp>> group(Long orderId) {
        Order order = orderMapper.selectOne(new QueryWrapper<Order>().select(
                Order.CLAZZ_ID, Order.ID, Order.SCHEDULE_ID
        ).eq(Order.ID, orderId));
        if (order == null || order.getClazzId() == null) {
            throw new WebBaseException(4000, "没有分班，无法统计");
        }
        List<GroupResp> respList = statsManager.wordGroupList(order.getClazzId());
        return RespUtils.success(setScheduleInfo(order.getScheduleId(), respList));
    }

    public CommResp<ScheduleResp<GroupResp>> groupByClazzId(Long clazzId) {
        Clazz clazz = clazzMapper.selectOne(new QueryWrapper<Clazz>().select(
                Clazz.ID, Clazz.SCHEDULE_ID
        ).eq(Clazz.ID, clazzId));
        List<GroupResp> respList = statsManager.wordGroupList(clazz.getId());
        return RespUtils.success(setScheduleInfo(clazz.getScheduleId(), respList));
    }

    public CommResp<ScheduleResp<RewardResp>> fxyReward(Long orderId) {
        Order order = orderMapper.selectOne(new QueryWrapper<Order>().select(
                Order.CLAZZ_ID, Order.ID, Order.SCHEDULE_ID
        ).eq(Order.ID, orderId));
        if (order == null || order.getClazzId() == null) {
            throw new WebBaseException(4000, "没有分班，无法统计");
        }
        List<RewardResp> respList = statsManager.fxyReward(order.getClazzId());
        respList = respList.stream().sorted(Comparator.comparingInt(RewardResp::getReward).reversed()).toList();
        return RespUtils.success(setScheduleInfo(order.getScheduleId(), respList));
    }

    public CommResp<ScheduleResp<RewardResp>> fxyRewardByClazzId(Long clazzId) {
        Clazz clazz = clazzMapper.selectOne(new QueryWrapper<Clazz>().select(
                Clazz.ID, Clazz.SCHEDULE_ID
        ).eq(Clazz.ID, clazzId));
        List<RewardResp> respList = statsManager.fxyReward(clazz.getId());
        respList = respList.stream().sorted(Comparator.comparingInt(RewardResp::getReward).reversed()).toList();
        return RespUtils.success(setScheduleInfo(clazz.getScheduleId(), respList));
    }

    public CommResp<ScheduleResp<GroupResp>> fxyGroup(Long orderId) {
        Order order = orderMapper.selectOne(new QueryWrapper<Order>().select(
                Order.CLAZZ_ID, Order.ID, Order.SCHEDULE_ID
        ).eq(Order.ID, orderId));
        if (order == null || order.getClazzId() == null) {
            throw new WebBaseException(4000, "没有分班，无法统计");
        }
        List<GroupResp> respList = statsManager.fxyGroupReward(order.getClazzId());
        return RespUtils.success(setScheduleInfo(order.getScheduleId(), respList));
    }

    public CommResp<ScheduleResp<GroupResp>> fxyGroupByClazzId(Long clazzId) {
        Clazz clazz = clazzMapper.selectOne(new QueryWrapper<Clazz>().select(
                Clazz.ID, Clazz.SCHEDULE_ID
        ).eq(Clazz.ID, clazzId));
        List<GroupResp> respList = statsManager.fxyGroupReward(clazz.getId());
        return RespUtils.success(setScheduleInfo(clazz.getScheduleId(), respList));
    }

    private <T> ScheduleResp<T> setScheduleInfo(Long scheduleId, List<T> dataList) {
        ScheduleResp<T> scheduleResp = new ScheduleResp<>();
        Schedule schedule = scheduleMapper.selectOne(new QueryWrapper<Schedule>().select(
                Schedule.ID, Schedule.START_TIME, Schedule.END_TIME
        ).eq(Schedule.ID, scheduleId));
        scheduleResp.setStartTime(schedule.getStartTime());
        if (LocalDateTime.now().isAfter(schedule.getEndTime())) {
            scheduleResp.setEndTime(schedule.getEndTime());
        } else {
            scheduleResp.setEndTime(LocalDateTime.now());
        }
        scheduleResp.setDataList(dataList);
        return scheduleResp;
    }
}
