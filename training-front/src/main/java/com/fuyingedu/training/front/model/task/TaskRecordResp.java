package com.fuyingedu.training.front.model.task;

import com.fuyingedu.training.dto.task.UploadItem;
import com.fuyingedu.training.front.model.media.MediaResp;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;

@ToString
@Getter
@Setter
public class TaskRecordResp {

    /**
     * 打卡的次数
     */
    private Integer punchNum;

    /**
     * 总次数
     */
    private Integer totalNum;
    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 任务介绍
     */
    private String taskContent;

    /**
     * 开始时间
     */
    private LocalTime startTime;

    /**
     * 截止时间
     */
    private LocalTime endTime;

    /**
     * 打卡日历
     */
    private List<Item> itemList;

    /**
     * 今日完成状态 1-未完成 2-已完成
     */
    private Byte taskStatus;
    /**
     * 提交状态 0-可提交 1-不可以提交
     */
    private Byte submitStatus;

    /**
     * 打卡案例
     */
    private List<MediaResp> punchCaseList;
    /**
     * 上传项目类型
     */
    private List<UploadItem> uploadItemList;

    @Getter
    @Setter
    public static class Item {

        private Long id;
        /**
         * 打卡日期
         */
        private LocalDate punchDate;

        /**
         * 完成状态 1-未完成 2-已完成 3-未开始
         */
        private Byte taskStatus;

        /**
         * 0 - 不是当天 1 - 是当天
         */
        private Byte today;
    }

}
