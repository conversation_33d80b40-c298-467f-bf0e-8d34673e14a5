package com.fuyingedu.training.front.manager;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.fuyingedu.training.entity.Teacher;
import com.fuyingedu.training.mapper.TeacherMapper;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.cp.api.WxCpService;
import me.chanjar.weixin.cp.bean.external.WxCpContactWayInfo;
import me.chanjar.weixin.cp.bean.external.WxCpContactWayResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Collections;

@Service
@Slf4j
public class TeacherManager {

    @Autowired
    private WxCpService wxCpService;
    @Autowired
    private TeacherMapper teacherMapper;

    @Value("${spring.profiles.active}")
    private String profiles;

    public String getWxUrl(Long teacherId, Long phoneNum, String wxUrl) {
        if (!"prod".equals(profiles)) {
            return wxUrl;
        }
        if (!StringUtils.hasLength(wxUrl)) {
            try {
                WxCpContactWayInfo wayInfo = new WxCpContactWayInfo();
                WxCpContactWayInfo.ContactWay contactWay = new WxCpContactWayInfo.ContactWay();
                contactWay.setType(WxCpContactWayInfo.TYPE.SINGLE);
                contactWay.setScene(WxCpContactWayInfo.SCENE.MINIPROGRAM);
                String userId = wxCpService.getUserService().getUserId(String.valueOf(phoneNum));
                contactWay.setUsers(Collections.singletonList(userId));
                wayInfo.setContactWay(contactWay);
                WxCpContactWayResult contactQrCode = wxCpService.getExternalContactService().addContactWay(wayInfo);
                if (contactQrCode != null) {
                    teacherMapper.update(new UpdateWrapper<Teacher>().set(Teacher.WX_URL, contactQrCode.getConfigId())
                            .eq(Teacher.ID, teacherId));
                    wxUrl = contactQrCode.getConfigId();
                }
            } catch (Exception e) {
                log.warn("获取二维码失败", e);
            }
        }
        return wxUrl;
    }


}
