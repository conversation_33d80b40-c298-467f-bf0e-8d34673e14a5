package com.fuyingedu.training.front.controller;

import com.fuyingedu.training.common.model.CommResp;
import com.fuyingedu.training.common.util.RespUtils;
import com.fuyingedu.training.front.model.word.*;
import com.fuyingedu.training.front.service.FrontHawkService;
import com.fuyingedu.training.front.service.FrontWordService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 扶小鹰-单词训练营
 */
@RestController
@RequestMapping("fxy/word")
public class FxyWordController {

    @Autowired
    private FrontWordService frontWordService;
    @Autowired
    private FrontHawkService frontHawkService;

    /**
     * 今日任务
     */
    @GetMapping("today/task")
    public CommResp<TaskResp> todayTask(Long orderId) {
        return frontWordService.todayTask(orderId);
    }

    /**
     * 任务列表
     */
    @GetMapping("list")
    public CommResp<List<WeekResp>> list(Long orderId, HttpServletRequest request) {
        return frontWordService.getTaskList(orderId, request);
    }

    /**
     * Word鹰
     */
    @GetMapping("hawk")
    public CommResp<HawkResp> hawkList(Long orderId) {
        return frontHawkService.list(orderId);
    }

    /**
     * Word鹰解锁
     */
    @PostMapping("hawk/unlock")
    public CommResp<?> hawkUnlock(@RequestBody @Valid HawkReq hawkReq) {
        frontHawkService.unlock(hawkReq);
        return RespUtils.success();
    }

    /**
     * 本期积分
     */
    @GetMapping("order/reward")
    public CommResp<RewardResp> orderReward(Long orderId) {
        return frontHawkService.orderReward(orderId);
    }

    /**
     * Word鹰积分列表
     */
    @GetMapping("hawk/reward")
    public CommResp<List<RewardItemResp>> hawkRewardList(RewardReq req) {
        return frontHawkService.hawkRewardList(req);
    }
}
