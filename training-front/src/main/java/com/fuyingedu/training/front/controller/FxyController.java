package com.fuyingedu.training.front.controller;

import com.fuyingedu.training.common.annotation.Login;
import com.fuyingedu.training.common.model.CommResp;
import com.fuyingedu.training.common.util.RespUtils;
import com.fuyingedu.training.front.model.clazz.ClassmateHomeworkListReq;
import com.fuyingedu.training.front.model.clazz.HomeworkItemResp;
import com.fuyingedu.training.front.model.clazz.HomeworkListReq;
import com.fuyingedu.training.front.model.clazz.InfoResp;
import com.fuyingedu.training.front.model.fxy.DateItemResp;
import com.fuyingedu.training.front.model.fxy.StatisticResp;
import com.fuyingedu.training.front.model.fxy.UserInfoResp;
import com.fuyingedu.training.front.model.order.MyCampResp;
import com.fuyingedu.training.front.model.task.*;
import com.fuyingedu.training.front.model.user.MedalItemResp;
import com.fuyingedu.training.front.service.*;
import jakarta.validation.Valid;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;

/**
 * 扶小鹰相关接口
 */
@RestController
@RequestMapping("fxy")
public class FxyController {

    @Autowired
    private FxyService fxyService;
    @Autowired
    private FrontOrderService frontOrderService;
    @Autowired
    private FrontTaskService frontTaskService;
    @Autowired
    private FrontClazzService frontClazzService;
    @Autowired
    private FrontUserService frontUserService;
    @Autowired
    private FrontScheduleService frontScheduleService;

    /**
     * 我的服务单(训练营)列表
     * @param openId 前端不需要传
     */
    @GetMapping("my/camp/list")
    public CommResp<List<MyCampResp>> myCampList(@Login String openId) {
        return frontOrderService.myCampList(openId);
    }

    /**
     * 作业和打卡内容点赞
     */
    @PostMapping("record/like")
    public CommResp<LikeResp> like(@Login String openId, @RequestBody @Valid LikeReq likeReq) {
        return frontTaskService.like(openId, likeReq);
    }

    /**
     * 取消点赞
     */
    @PostMapping("record/unlike")
    public CommResp<LikeResp> unlike(@Login String openId, @RequestBody @Valid LikeReq likeReq) {
        return frontTaskService.unlike(openId, likeReq);
    }

    /**
     * 训练营统计信息
     * @param orderId 服务单ID
     */
    @GetMapping("camp/statistic/info")
    public CommResp<StatisticResp> campInfo(@RequestParam("orderId") Long orderId) {
        return fxyService.campInfo(orderId);
    }

    /**
     * 今日任务列表
     * @param orderId 服务单ID
     * @param date 日期 默认为今天 2024-06-19
     */
    @GetMapping("task/list")
    public CommResp<List<TaskItemResp>> taskList(@RequestParam("orderId") Long orderId,
                                                 @RequestParam(required = false, value = "date") LocalDate date) {
        return frontTaskService.taskList(orderId, date);
    }

    /**
     * 所有任务列表
     */
    @GetMapping("all")
    public CommResp<List<TaskResp>> allTaskList(@RequestParam("orderId") Long orderId) {
        return frontTaskService.allTaskListFxy(orderId);
    }

    /**
     * 打卡记录（作业打卡）
     * @param orderId 服务单ID
     * @param taskId 任务ID
     */
    @GetMapping("punch/list")
    public CommResp<TaskRecordResp> punchList(
                                         @RequestParam("orderId") Long orderId, @RequestParam("taskId") Long taskId) {
        return frontTaskService.punchList(orderId, taskId);
    }

    /**
     * 获取打卡详情（作业打卡）
     * @param id 打卡日历列表中的ID
     */
    @GetMapping("punch/detail")
    public CommResp<DetailResp> punchDetail(@RequestParam("id") Long id,
                                            @RequestParam(value = "orderId", required = false) Long orderId) {
        return frontTaskService.punchDetail(id, orderId);
    }

    /**
     * 获取作业基础信息和状态
     */
    @GetMapping("homework")
    public CommResp<HomeworkResp> homework(@RequestParam("orderId") Long orderId, @RequestParam("taskId") Long taskId) {
        return frontTaskService.homework(orderId, taskId);
    }

    /**
     * 作业详情
     */
    @GetMapping("homework/detail")
    public CommResp<DetailResp> homeworkDetail(@RequestParam("orderId") Long orderId, @RequestParam("taskId") Long taskId) {
        return frontTaskService.homeworkDetail(orderId, taskId);
    }
    /**
     * 打卡记录（接口打卡）
     */
    @GetMapping("enrollment/list")
    public CommResp<TaskRecordResp> enrollmentList(@RequestParam("orderId") Long orderId, @RequestParam("taskId") Long taskId) {
        return frontTaskService.enrollmentList(orderId, taskId);
    }

    /**
     * 获取班级信息
     */
    @GetMapping("clazz/info")
    public CommResp<InfoResp> clazzInfo(@RequestParam("orderId") Long orderId) throws WxErrorException {
        return frontClazzService.info(orderId);
    }

    /**
     * 学习圈-作业列表
     */
    @GetMapping("homework/list")
    public CommResp<List<HomeworkItemResp>> homeworkList(@Valid HomeworkListReq homeworkListReq) {
        return frontClazzService.homeworkList(homeworkListReq);
    }

    /**
     * 获取任务的日期列表
     */
    @GetMapping("task/date/list")
    public CommResp<List<DateItemResp>> taskDateList(@RequestParam("orderId") Long orderId) {
        return fxyService.taskDateList(orderId);
    }

    /**
     * 获取用户信息-用于判断用户是否绑定训练营
     */
    @GetMapping("user/info")
    public CommResp<UserInfoResp> userInfo(@RequestParam("openId") String openId, @RequestHeader("access_token") String accessToken) {
        return fxyService.userInfo(openId, accessToken);
    }

    /**
     * 学员详情-同班同学用户基本信息
     */
    @GetMapping("classmate/info")
    public CommResp<com.fuyingedu.training.front.model.user.InfoResp> classmateInfo(@RequestParam("orderId") Long orderId) {
        return frontUserService.classmateInfo(orderId);
    }

    /**
     * 学员详情-同班同学作业列表
     */
    @GetMapping("classmate/homework/list")
    public CommResp<List<HomeworkItemResp>> classmateHomeworkList(@Valid ClassmateHomeworkListReq classmateHomeworkListReq) {
        return frontClazzService.classmateHomeworkList(classmateHomeworkListReq);
    }

    /**
     * 直播列表
     * @param orderId 服务单ID
     */
    @GetMapping("live/list")
    public CommResp<List<LiveItemResp>> liveList(@RequestParam("orderId") Long orderId) {
        return frontTaskService.liveList(orderId);
    }

    /**
     * 获取的奖牌列表
     * @param orderId 服务单ID
     */
    @GetMapping("medal/list")
    public CommResp<List<MedalItemResp>> medalList(@RequestParam("orderId") Long orderId) {
        return frontUserService.medalList(orderId);
    }

    /**
     * 首页获取排期信息
     */
    @GetMapping("schedule/info")
    public CommResp<com.fuyingedu.training.front.model.schedule.InfoResp> scheduleInfo(@RequestParam("orderId") Long orderId) {
        return frontScheduleService.info(orderId);
    }

    /**
     * 点赞的用户列表
     */
    @GetMapping("like/list")
    public CommResp<List<com.fuyingedu.training.front.model.clazz.LikeResp>> likeList(@RequestParam("recordId") Long recordId) {
        return frontClazzService.likeList(recordId);
    }

    /**
     * 任务完成
     */
    @PostMapping("task/submit")
    public CommResp<?> submitTask(@RequestBody @Valid RecordSaveReq req) {
        frontTaskService.submitTask(req);
        return RespUtils.success();
    }
}
