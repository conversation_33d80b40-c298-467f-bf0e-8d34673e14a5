package com.fuyingedu.training.front.controller;

import com.fuyingedu.training.common.annotation.Login;
import com.fuyingedu.training.common.model.CommResp;
import com.fuyingedu.training.front.model.group.HomeworkRemarkResp;
import com.fuyingedu.training.front.service.FrontGroupService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 小组管理
 */
@RequestMapping("front/group")
@RestController
public class FrontGroupController {

    @Autowired
    private FrontGroupService frontGroupService;

    /**
     * 小组长点评列表
     */
    @GetMapping("homework/list")
    public CommResp<HomeworkRemarkResp> homeworkList(@Login Long userId,
                                                     @RequestParam("orderId") Long orderId,
                                                     @RequestParam(value = "homeworkType", defaultValue = "1") Byte homeworkType) {
        return frontGroupService.homeworkList(userId, orderId, homeworkType);
    }
}
