package com.fuyingedu.training.front.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.fasterxml.jackson.databind.JsonNode;
import com.fuyingedu.training.common.enums.*;
import com.fuyingedu.training.common.exception.WebBaseException;
import com.fuyingedu.training.common.model.CommResp;
import com.fuyingedu.training.common.util.AsyncUtils;
import com.fuyingedu.training.common.util.JsonUtils;
import com.fuyingedu.training.common.util.RespUtils;
import com.fuyingedu.training.entity.*;
import com.fuyingedu.training.front.feign.FuyingCourseFeign;
import com.fuyingedu.training.front.manager.CheckAuthManager;
import com.fuyingedu.training.front.manager.OperationManager;
import com.fuyingedu.training.front.manager.TeacherManager;
import com.fuyingedu.training.front.model.schedule.ConfirmReq;
import com.fuyingedu.training.front.model.schedule.FutureResp;
import com.fuyingedu.training.front.model.schedule.InfoResp;
import com.fuyingedu.training.front.model.schedule.UpdateReq;
import com.fuyingedu.training.mapper.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class FrontScheduleService {

    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private CampMapper campMapper;
    @Autowired
    private ScheduleMapper scheduleMapper;
    @Autowired
    private ClazzMapper clazzMapper;
    @Autowired
    private TeacherMapper teacherMapper;
    @Autowired
    private UserMapper userMapper;
    @Autowired
    private OrderFxyMapper orderFxyMapper;
    @Autowired
    private GroupMapper groupMapper;
    @Autowired
    private CheckAuthManager checkAuthManager;
    @Autowired
    private TeacherManager teacherManager;
    @Autowired
    private FuyingCourseFeign fuyingCourseFeign;
    @Value("${fxy.base-url}")
    private String baseUrl;
    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private OperationManager operationManager;

    public CommResp<InfoResp> info(Long orderId) {
        Order order = orderMapper.selectOne(new QueryWrapper<Order>().select(
                Order.CAMP_ID, Order.SCHEDULE_ID, Order.TEACHER_ID, Order.CLAZZ_ID,
                Order.STUDENT_ID, Order.CONFIRM_STATUS, Order.CONFIRM_TIME
        ).eq(Order.ID, orderId).in(Order.ORDER_STATUS, OrderStatus.NORMAL.getCode(), OrderStatus.MAY_REFUND.getCode()));
        if (order == null) {
            return RespUtils.warning(RespMetaEnum.PARAM_ERROR);
        }
        if (order.getScheduleId() == null) {
            return RespUtils.warning(RespMetaEnum.NO_SCHEDULE);
        }
        InfoResp infoResp = new InfoResp();
        infoResp.setConfirmStatus(order.getConfirmStatus());
        infoResp.setConfirmTime(order.getConfirmTime());
        Camp camp = campMapper.selectOne(new QueryWrapper<Camp>().select(
                Camp.WX_PRIORITY, Camp.NEED_FXY, Camp.NEED_BINDING)
                .eq(Camp.ID, order.getCampId()));
        infoResp.setNeedFxy(camp.getNeedFxy());
        if (order.getStudentId() == null && NeedBinding.YES.getCode().equals(camp.getNeedBinding())) {
            infoResp.setNeedBinding(NeedBinding.YES.getCode());
        } else {
            infoResp.setNeedBinding(NeedBinding.NO.getCode());
        }
        Schedule schedule = scheduleMapper.selectOne(new QueryWrapper<Schedule>().select(
                Schedule.SCHEDULE_NAME, Schedule.START_TIME, Schedule.END_TIME)
                .eq(Schedule.ID, order.getScheduleId()));
        infoResp.setScheduleName(schedule.getScheduleName());
        LocalDateTime now = scheduleMapper.queryNow();
        infoResp.setScheduleStatus(ScheduleStatus.convert(schedule.getStartTime(), schedule.getEndTime(), now).getCode());
        if (order.getTeacherId() != null && WxPriority.TEACHER.getCode().equals(camp.getWxPriority())) {
            Teacher teacher = teacherMapper.selectOne(new QueryWrapper<Teacher>().select(
                    Teacher.ID, Teacher.USER_ID, Teacher.REAL_NAME, Teacher.WX_URL)
                    .eq(Teacher.ID, order.getTeacherId())
            );
            User user = userMapper.selectOne(new QueryWrapper<User>().select(
                    User.USER_ICON, User.PHONE_NUM)
                    .eq(User.ID, teacher.getUserId())
            );
            infoResp.setTeacherName(teacher.getRealName());
            infoResp.setWxUserId(teacherManager.getWxUrl(teacher.getId(), user.getPhoneNum(), teacher.getWxUrl()));
            infoResp.setWxUrl(infoResp.getWxUserId());
            infoResp.setUserIcon(user.getUserIcon());
        }
        if (order.getClazzId() != null && WxPriority.ASSISTANT.getCode().equals(camp.getWxPriority())) {
            Clazz clazz = clazzMapper.selectOne(new QueryWrapper<Clazz>().select(
                    Clazz.ID, Clazz.ASSISTANT_ID)
                    .eq(Clazz.ID, order.getClazzId())
            );
            Teacher teacher = teacherMapper.selectOne(new QueryWrapper<Teacher>().select(
                    Teacher.ID, Teacher.USER_ID, Teacher.REAL_NAME, Teacher.WX_URL).eq(Teacher.ID, clazz.getAssistantId()));
            User user = userMapper.selectOne(new QueryWrapper<User>().select(
                        User.USER_ICON,  User.PHONE_NUM)
                    .eq(User.ID, teacher.getUserId())
            );
            infoResp.setTeacherName(teacher.getRealName());
            infoResp.setWxUserId(teacherManager.getWxUrl(teacher.getId(), user.getPhoneNum(), teacher.getWxUrl()));
            infoResp.setWxUrl(infoResp.getWxUserId());
            infoResp.setUserIcon(user.getUserIcon());
        }
        if (!ScheduleStatus.UN_START.getCode().equals(infoResp.getScheduleStatus()) && order.getClazzId() == null) {
            infoResp.setScheduleStatus((byte) 5);
        }
        if (!ScheduleStatus.UN_START.getCode().equals(infoResp.getScheduleStatus()) && order.getTeacherId() == null) {
            infoResp.setScheduleStatus((byte) 4);
        }
        if (NeedBinding.YES.getCode().equals(camp.getNeedFxy())) {
            OrderFxy studentFxy = orderFxyMapper.selectOne(new QueryWrapper<OrderFxy>().select(
                    OrderFxy.ID).eq(OrderFxy.ORDER_ID, orderId));
            if (studentFxy != null) {
                infoResp.setNeedFxy(NeedBinding.NO.getCode());
            }
        }
        return RespUtils.success(infoResp);
    }

    public CommResp<List<FutureResp>> futureList(Long orderId) {
        Order order = orderMapper.selectOne(new QueryWrapper<Order>().select(
                Order.ID, Order.CAMP_ID
        ).eq(Order.ID, orderId).in(Order.ORDER_STATUS, OrderStatus.NORMAL.getCode(), OrderStatus.MAY_REFUND.getCode()));
        LocalDateTime now = scheduleMapper.queryNow();
        List<Schedule> scheduleList = scheduleMapper.selectList(new QueryWrapper<Schedule>().select(
                Schedule.ID,
                Schedule.SCHEDULE_NAME
        ).eq(Schedule.CAMP_ID, order.getCampId()).gt(Schedule.END_TIME, now));
        List<FutureResp> respList = scheduleList.stream().map(this::toFutureResp).toList();
        return RespUtils.success(respList);
    }

    private FutureResp toFutureResp(Schedule schedule) {
        FutureResp futureResp = new FutureResp();
        futureResp.setId(schedule.getId());
        futureResp.setScheduleName(schedule.getScheduleName());
        return futureResp;
    }

    public void confirm(Long userId, ConfirmReq confirmReq) {
        Order order = orderMapper.selectOne(new QueryWrapper<Order>().select(
                Order.ID, Order.USER_ID, Order.CONFIRM_STATUS)
                .eq(Order.ID, confirmReq.getOrderId())
                .in(Order.ORDER_STATUS, OrderStatus.NORMAL.getCode(), OrderStatus.MAY_REFUND.getCode())
        );
        checkAuthManager.checkOrder(userId, order.getId(), order.getUserId());
        if (!ConfirmStatus.NOT_CONFIRM.getCode().equals(order.getConfirmStatus())) {
            throw new WebBaseException(4000, "服务单已确认");
        }
        orderMapper.update(new UpdateWrapper<Order>()
                .set(Order.CONFIRM_STATUS, ConfirmStatus.MY_CONFIRM.getCode())
                .set(Order.CONFIRM_TIME, LocalDateTime.now())
                .eq(Order.ID, order.getId()).in(Order.ORDER_STATUS, OrderStatus.NORMAL.getCode(), OrderStatus.MAY_REFUND.getCode()));
    }

    @Transactional(rollbackFor = Exception.class)
    public Order updateSchedule(Long userId, UpdateReq updateReq) {
        Schedule schedule = scheduleMapper.selectOne(new QueryWrapper<Schedule>().select(
                Schedule.ID, Schedule.START_TIME, Schedule.SCHEDULE_ID, Schedule.CLAZZ_ID
        ).eq(Schedule.ID, updateReq.getScheduleId()));
        if (schedule == null) {
            throw new WebBaseException(4000, "排期不存在");
        }
        Order order = orderMapper.selectOne(new QueryWrapper<Order>().select(
                    Order.ID, Order.SCHEDULE_ID, Order.TEACHER_ID,
                    Order.CLAZZ_ID, Order.GROUP_ID, Order.CAMP_ID,
                    Order.USER_ID, Order.STUDENT_TYPE, Order.ORDER_NO
            ).eq(Order.ID, updateReq.getId())
                .in(Order.ORDER_STATUS, OrderStatus.NORMAL.getCode(), OrderStatus.MAY_REFUND.getCode())
        );
        if (updateReq.getScheduleId().equals(order.getScheduleId())) {
            return order;
        }
        updateSchedule(userId, order, schedule);
        order.setScheduleId(updateReq.getScheduleId());
        return order;
    }

    public void updateSchedule(Long userId, Order order, Schedule schedule) {
        orderMapper.update(new UpdateWrapper<Order>()
                .set(Order.SCHEDULE_ID, schedule.getId())
                .set(Order.TEACHER_ID, null).set(Order.CLAZZ_ID, null).set(Order.GROUP_ID, null)
                .set(Order.STUDENT_TYPE, StudentType.NORMAL.getCode())
                .eq(Order.ID, order.getId()));
        resetGroupMonitor(order.getStudentType(), order.getGroupId());
        operationManager.modifySchedule(userId, order, schedule.getId());
        Map<String, Object> params = new HashMap<>(Map.of("courseId", order.getCampId(), "uid", order.getUserId(),
                "courseTraineeNos", Collections.singleton(order.getOrderNo())));
        params.put("courseScheduleId", schedule.getScheduleId());
        params.put("courseScheduleClassId", schedule.getClazzId());
        String s = fuyingCourseFeign.changeSchedule(params);
        JsonNode jsonNode = JsonUtils.parseJsonToJsonNode(s);
        if (!"200".equals(jsonNode.get("code").asText())) {
            throw new WebBaseException(500, jsonNode.get("message").asText());
        }
        log.info("外部修改排期参数:{}结果:{}", JsonUtils.formatObjToJson(params), s);
        AsyncUtils.execute(() -> {
            HttpHeaders headers = new HttpHeaders();
            headers.set("Access-Key", "3447CB6560E99791");
            HttpEntity<Object> httpEntity = new HttpEntity<>(headers);
            String url = String.format("/traApi/admin/campStudy/delStudyLog?trainingOrderId=%d", order.getId());
            ResponseEntity<String> resp = restTemplate.exchange(baseUrl + url, HttpMethod.POST, httpEntity, String.class);
            log.info("单词训练营接口调用:{}", resp.getBody());
        }, "单词训练营接口调用");
    }

    private void resetGroupMonitor(Byte studentType, Long groupId) {
        if (StudentType.VOLUNTEER.getCode().equals(studentType)) {
            groupMapper.update(new UpdateWrapper<Group>().setSql("monitor_id = null").eq(Group.ID, groupId));
        }
    }
}
