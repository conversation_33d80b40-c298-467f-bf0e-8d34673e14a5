package com.fuyingedu.training.front.feign;

import com.fuyingedu.training.front.model.feign.*;
import feign.Headers;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;
import java.util.Set;

@Headers("Internal-Access: dHJhaW5pbmctYWRtaW46JEJvMlU2dSY=")
@FeignClient(name = "abm-course-service", path = "course/offline/internal/camp", url = "https://gateway-dev.fuyingy.com/api")
public interface FuyingCourseFeign {

    @GetMapping("list")
    List<CampItem> listCamp();

    @GetMapping("schedule/list")
    List<ScheduleItem> listSchedule(@RequestParam("courseIds") Set<Long> courseIds,
                                    @RequestParam("startTime") String startTime,
                                    @RequestParam("endTime") String endTime);

    @GetMapping("trainee")
    TraineeItem getTrainee(@RequestParam(value = "traineeId", required = false) Long traineeId);

    @GetMapping("courseTrainee")
    List<OrderItem> listCourseTrainee(@RequestParam(value = "scheduleId", required = false) Long scheduleId,
                                      @RequestParam(value = "serviceNo", required = false) String serviceNo);

    @PostMapping("courseTrainee/sign")
    String sign(@RequestBody Map<String, Object> params);

    @GetMapping("courseTrainee/sign/syncrecord")
    String syncRecord(@RequestParam("nos") String nos);

    @GetMapping("schedule/class/list")
    List<ClazzItem> listClassSchedule(@RequestParam("scheduleId") Long scheduleId);

    @PostMapping("schedule/change")
    String changeSchedule(@RequestBody Map<String, Object> params);
}
