package com.fuyingedu.training.front.model.clazz;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@ToString
@Getter
@Setter
public class InfoResp {

    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 1-普通学员 2-陪跑志愿者
     */
    private Byte studentType;
    /**
     * 班级ID 为空表示未分班
     */
    private Long clazzId;
    /**
     * 班级名称
     */
    private String clazzName;
    /**
     * 小组ID 为空表示未分组
     */
    private Long groupId;
    /**
     * 小组名称
     */
    private String groupName;

    /**
     * 班级群二维码
     */
    private String wxUrl;

    /**
     * 企业微信群ID
     */
    private String wxGroupId;

    /**
     * 企业微信群名称
     */
    private String wxGroupName;

    /**
     * 讲师列表
     */
    private List<Teacher> teacherList;

    /**
     * 班级成员列表
     */
    private List<Classmate> classmateList;

    @Getter
    @Setter
    public static class Teacher {

        /**
         * 讲师类型 1-辅导老师 2-助教
         */
        private Byte teacherType;
        /**
         * 姓名
         */
        private String realName;

        /**
         * 手机号
         */
        private Long phoneNum;

        /**
         * 企业微信二维码
         */
        private String wxUrl;

        /**
         * 企业微信用户ID
         */
        private String wxUserId;
        /**
         * 用户头像
         */
        private String userIcon;
    }

}
