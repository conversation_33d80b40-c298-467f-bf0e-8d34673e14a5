package com.fuyingedu.training.front.controller;

import com.fuyingedu.training.common.annotation.Login;
import com.fuyingedu.training.common.model.CommResp;
import com.fuyingedu.training.common.util.RespUtils;
import com.fuyingedu.training.front.model.schedule.ConfirmReq;
import com.fuyingedu.training.front.model.schedule.InfoResp;
import com.fuyingedu.training.front.model.schedule.NextReq;
import com.fuyingedu.training.front.service.FrontScheduleService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 排期相关接口
 */
@RestController
@RequestMapping("front/schedule")
public class FrontScheduleController {

    @Autowired
    private FrontScheduleService frontScheduleService;

    /**
     * 首页获取排期信息
     */
    @GetMapping("info")
    public CommResp<InfoResp> info(@RequestParam("orderId") Long orderId) {
        return frontScheduleService.info(orderId);
    }

    /**
     * 确认参加
     * @param userId 前端不需要传
     */
    @PostMapping("confirm")
    public CommResp<?> confirm(@Login Long userId, @RequestBody @Valid ConfirmReq confirmReq) {
        frontScheduleService.confirm(userId, confirmReq);
        return RespUtils.success();
    }
}
