package com.fuyingedu.training.front.controller;

import com.fuyingedu.training.common.annotation.Login;
import com.fuyingedu.training.common.model.CommResp;
import com.fuyingedu.training.common.util.RespUtils;
import com.fuyingedu.training.front.model.task.*;
import com.fuyingedu.training.front.model.task.remark.SaveReq;
import com.fuyingedu.training.front.service.FrontTaskService;
import com.fuyingedu.training.front.service.TaskRemarkService;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;

/**
 * 任务管理
 */
@Slf4j
@RestController
@RequestMapping("front/task")
public class FrontTaskController {

    @Autowired
    private FrontTaskService frontTaskService;
    @Autowired
    private TaskRemarkService taskRemarkService;

    /**
     * 今日任务列表
     * @param orderId 服务单ID
     * @param date 日期 默认为今天 2024-06-19
     */
    @GetMapping("list")
    public CommResp<List<TaskItemResp>> taskList(
                                                 @RequestParam("orderId") Long orderId,
                                                 @RequestParam(required = false, value = "date")LocalDate date) {
        return frontTaskService.taskList(orderId, date);
    }

    /**
     * 所有任务列表
     */
    @GetMapping("all")
    public CommResp<List<TaskResp>> allTaskList(@Login Long userId, @RequestParam("orderId") Long orderId) {
        return frontTaskService.allTaskList(userId, orderId);
    }

    /**
     * 直播列表
     * @param userId 前端不需要传
     * @param orderId 服务单ID
     */
    @GetMapping("live/list")
    public CommResp<List<LiveItemResp>> liveList(@Login Long userId, @RequestParam("orderId") Long orderId) {
        return frontTaskService.liveList(orderId);
    }

    /**
     * 签到
     * @param userId 前端不需要传
     */
    @PostMapping("enrollment/save")
    public CommResp<?> enrollmentSave(@Login Long userId, @RequestBody @Valid EnrollmentSaveReq enrollmentSaveReq) {
        log.info("用户[{}]服务单[{}]签到[{}]", userId, enrollmentSaveReq.getOrderId(), enrollmentSaveReq.getTaskId());
        frontTaskService.enrollmentSave(userId, enrollmentSaveReq);
        return RespUtils.success();
    }

    /**
     * 签到记录
     */
    @GetMapping("enrollment/list")
    public CommResp<TaskRecordResp> enrollmentList(@RequestParam("orderId") Long orderId, @RequestParam("taskId") Long taskId) {
        return frontTaskService.enrollmentList(orderId, taskId);
    }

    /**
     * 打卡记录
     * @param userId 前端不需要传
     * @param orderId 服务单ID
     * @param taskId 任务ID
     */
    @GetMapping("punch/list")
    public CommResp<TaskRecordResp> punchList(@Login Long userId,
                                              @RequestParam("orderId") Long orderId, @RequestParam("taskId") Long taskId) {
        return frontTaskService.punchList(orderId, taskId);
    }


    /**
     * 打卡
     * @param userId 前端不需要传
     */
    @PostMapping("punch/save")
    public CommResp<?> punchSave(@Login Long userId, @RequestBody @Valid RecordSaveReq recordSaveReq) {
        log.info("用户[{}]服务单[{}]打卡[{}]", userId, recordSaveReq.getOrderId(), recordSaveReq.getTaskId());
        frontTaskService.punchSave(userId, recordSaveReq);
        return RespUtils.success();
    }

    /**
     * 保存草稿
     */
    @PostMapping("draft/save")
    public CommResp<?> draftSave(@RequestBody @Valid RecordSaveReq draftSaveReq) {
        frontTaskService.draftSave(draftSaveReq);
        return RespUtils.success();
    }

    /**
     * 获取草稿详情
     */
    @GetMapping("draft/detail")
    public CommResp<RecordResp> draftDetail(@RequestParam("orderId") Long orderId, @RequestParam("taskId") Long taskId) {
        return frontTaskService.draftDetail(orderId, taskId);
    }

    /**
     * 获取打卡的记录详情
     * @param id 打卡/作业日历列表中的ID
     */
    @GetMapping("punch/detail")
    public CommResp<DetailResp> punchDetail(@RequestParam("id") Long id,
                                            @RequestParam(value = "orderId", required = false) Long orderId) {
        return frontTaskService.punchDetail(id, orderId);
    }

    /**
     * 获取作业基础信息和状态
     */
    @GetMapping("homework")
    public CommResp<HomeworkResp> homework(@RequestParam("orderId") Long orderId, @RequestParam("taskId") Long taskId) {
        return frontTaskService.homework(orderId, taskId);
    }

    /**
     * 上传作业
     * @param userId 前端不需要传
     */
    @PostMapping("homework/save")
    public CommResp<?> homeworkSave(@Login Long userId, @RequestBody @Valid RecordSaveReq recordSaveReq) {
        log.info("用户[{}]服务单[{}]作业[{}]上传", userId, recordSaveReq.getOrderId(), recordSaveReq.getTaskId());
        frontTaskService.homeworkSave(userId, recordSaveReq);
        return RespUtils.success();
    }

    /**
     * 作业上传详情
     */
    @GetMapping("homework/detail")
    public CommResp<DetailResp> homeworkDetail(@Login Long userId, @RequestParam("orderId") Long orderId, @RequestParam("taskId") Long taskId) {
        return frontTaskService.homeworkDetail(orderId, taskId);
    }

    /**
     * 点评作业
     * @param userId 前端不需要传
     */
    @PostMapping("remark/save")
    public CommResp<?> save(@Login Long userId, @Valid @RequestBody SaveReq saveReq) {
        taskRemarkService.remark(userId, saveReq);
        return RespUtils.success();
    }

    /**
     * 作业和打卡内容点赞
     */
    @PostMapping("record/like")
    public CommResp<LikeResp> like(@Login Long userId, @RequestBody @Valid LikeReq likeReq) {
        return frontTaskService.like(userId, likeReq);
    }

    /**
     * 取消点赞
     */
    @PostMapping("record/unlike")
    public CommResp<LikeResp> unlike(@Login Long userId, @RequestBody @Valid LikeReq likeReq) {
        return frontTaskService.unlike(userId, likeReq);
    }
}
