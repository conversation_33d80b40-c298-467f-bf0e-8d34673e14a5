package com.fuyingedu.training.front.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.fuyingedu.training.common.enums.TaskType;
import com.fuyingedu.training.entity.Task;
import com.fuyingedu.training.entity.TaskStatistic;
import com.fuyingedu.training.mapper.TaskMapper;
import com.fuyingedu.training.mapper.TaskStatisticMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;

@Service
public class FrontTaskStatisticService {

    @Autowired
    private TaskStatisticMapper taskStatisticMapper;
    @Autowired
    private TaskMapper taskMapper;

    /**
     * 增加当天的记录值
     */
    public void addRecord(Long clazzId, Long taskId) {
        Task task = taskMapper.selectOne(new QueryWrapper<Task>().select(
                Task.TASK_TYPE, Task.START_TIME
        ).eq(Task.ID, taskId));
        LocalDate recordDate = null;
        if (!TaskType.HOMEWORK.getCode().equals(task.getTaskType())) {
            recordDate = LocalDate.now();
        }
        // 添加每天的记录
        TaskStatistic taskStatistic = taskStatisticMapper.selectOne(new QueryWrapper<TaskStatistic>().select(
                        TaskStatistic.ID
                ).eq(recordDate != null, TaskStatistic.RECORD_DATE, recordDate)
                        .isNull(recordDate == null, TaskStatistic.RECORD_DATE)
                .eq(TaskStatistic.CLAZZ_ID, clazzId).eq(TaskStatistic.TASK_ID, taskId));
        if (taskStatistic == null) {
            taskStatistic = new TaskStatistic();
            taskStatistic.setClazzId(clazzId);
            taskStatistic.setTaskId(taskId);
            taskStatistic.setRecordDate(recordDate);
            taskStatistic.setRecordNum(1);
            taskStatisticMapper.insert(taskStatistic);
        } else {
            taskStatisticMapper.update(new UpdateWrapper<TaskStatistic>()
                    .setSql(String.format("%s = %s + 1", TaskStatistic.RECORD_NUM, TaskStatistic.RECORD_NUM))
                    .eq(TaskStatistic.ID, taskStatistic.getId())
            );
        }
    }
}
