package com.fuyingedu.training.front.service;

import com.aliyun.sts20150401.Client;
import com.aliyun.sts20150401.models.AssumeRoleRequest;
import com.aliyun.sts20150401.models.AssumeRoleResponse;
import com.aliyun.teautil.models.RuntimeOptions;
import com.fuyingedu.training.common.annotation.Login;
import com.fuyingedu.training.common.exception.WebBaseException;
import com.fuyingedu.training.common.model.CommResp;
import com.fuyingedu.training.common.util.DateUtils;
import com.fuyingedu.training.common.util.RespUtils;
import com.fuyingedu.training.front.model.file.PolicyResp;
import com.fuyingedu.training.front.model.media.MediaConvertor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDate;

@Service
@Slf4j
public class OssService {

    @Value("${oss.bucket}")
    private String bucket;
    @Value("${oss.role-arn}")
    private String roleArn;

    @Autowired
    private Client client;

    public CommResp<PolicyResp> getOssPolicy(@Login Long userId, String type) {
        String dir = DateUtils.format(LocalDate.now(), DateUtils.DATE_NONE_FORMATTER) + "/" + userId;
        AssumeRoleRequest assumeRoleRequest = new AssumeRoleRequest()
                .setDurationSeconds(900L)
                .setRoleSessionName("fuying-training")
                .setRoleArn(roleArn)
                .setPolicy("{\"Version\":\"1\",\"Statement\":[{\"Effect\":\"Allow\",\"Action\":[\"oss:PutObject\"],\"Resource\":[\"acs:oss:*:*:" + bucket + "/" + dir + "/*\"]}]}")
                ;
        RuntimeOptions runtime = new RuntimeOptions();
        try {
            AssumeRoleResponse response = client.assumeRoleWithOptions(assumeRoleRequest, runtime);
            PolicyResp policyResp = new PolicyResp();
            policyResp.setCredentials(response.body.credentials);
            policyResp.setDir(dir);
            policyResp.setType(MediaConvertor.getMediaType(type));
            return RespUtils.success(policyResp);
        } catch (Exception _error) {
            log.error("获取oss上传策略失败", _error);
            throw new WebBaseException(500, "获取oss上传策略失败");
        }
    }
}
