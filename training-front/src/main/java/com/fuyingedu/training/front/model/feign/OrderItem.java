package com.fuyingedu.training.front.model.feign;

import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
public class OrderItem {
    /**
     * 主键标识
     */
    private Long id;

    /**
     * 训练营id
     */
    private Long courseId;

    /**
     * 服务类型：0新训 1复训
     */
    private Byte serviceType;

    /**
     * 排期id
     */
    private Long scheduleId;

    private Long scheduleClassId;

    /**
     * 学员ID
     */
    private Long traineeId;

    /**
     * 用户ID
     */
    private Long uid;

    /**
     * 服务单号
     */
    private String no;

    private String orderNo;

    /**
     * 状态：0候选 1正式 2未选期 3已上课 4已过期 5已违约 6已支付违约金 7已作废
     */
    private Integer status;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}
