package com.fuyingedu.training.front.model.order;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;

@ToString
@Getter
@Setter
public class MyCampResp {

    /**
     * 服务单ID
     */
    private Long orderId;

    private Long userId;

    /**
     * 词书ID
     */
    private Long wordId;

    /**
     * 训练营
     */
    private String campName;

    /**
     * 1-普通 2-扶小鹰陪跑 3-单词训练
     */
    private Byte campType;

    /**
     * 排期
     */
    private String scheduleName;
    /**
     * 1-未开始 2-进行中 3-已结束
     */
    private Byte scheduleStatus;

    /**
     * 学员
     */
    private String studentName;

    /**
     * 证件号
     */
    private String cartNum;


    /**
     * 排期开启时间
     */
    private LocalDateTime startTime;

    /**
     * 排期截止时间
     */
    private LocalDateTime endTime;

    /**
     * 训练营图片
     */
    private String mainMediaUrl;

    /**
     * 服务单备注
     */
    private String orderRemark;
}
