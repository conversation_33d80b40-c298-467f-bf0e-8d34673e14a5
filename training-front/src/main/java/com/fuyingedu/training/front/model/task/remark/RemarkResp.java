package com.fuyingedu.training.front.model.task.remark;

import com.fuyingedu.training.front.model.media.MediaResp;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.List;

@ToString
@Getter
@Setter
public class RemarkResp {

    private Long id;

    private Long remarkUserId;
    /**
     * 点评人
     */
    private String remarkUserName;

    /**
     * 点评人头像
     */
    private String remarkUserIcon;

    /**
     * 点评内容
     */
    private String remarkContent;
    /**
     * 点评的素材
     */
    private List<MediaResp> remarkUrlList;

    /**
     * 备注时间
     */
    private LocalDateTime createdTime;
    /**
     * 点评的标签
     */
    private List<Label> labelList;

    private Integer goodLabelNum;

    private Integer badLabelNum;

    @Getter
    @Setter
    public static class Label {
        private Long id;
        /**
         * 标签名称
         */
        private String labelName;

        /**
         * 标签图
         */
        private String mediaUrl;
    }
}
