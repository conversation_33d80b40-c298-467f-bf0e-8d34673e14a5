package com.fuyingedu.training.front.manager;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fuyingedu.training.common.util.DateUtils;
import com.fuyingedu.training.entity.WordPkConfig;
import com.fuyingedu.training.front.model.pk.StatusResp;
import com.fuyingedu.training.mapper.WordPkConfigMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;

@Component
public class PkManager {

    @Autowired
    private WordPkConfigMapper wordPkConfigMapper;

    @Cacheable(value = "pk:status:${#scheduleId}")
    public StatusResp status(Long scheduleId) {
        StatusResp statusResp = new StatusResp();
        statusResp.setStatus((byte) 1);
        List<WordPkConfig> pkConfigList = wordPkConfigMapper.selectList(new QueryWrapper<WordPkConfig>()
                .eq(WordPkConfig.SCHEDULE_ID, scheduleId).orderByAsc(WordPkConfig.START_TIME));
        if (pkConfigList.isEmpty() || pkConfigList.getFirst().getStartTime().isAfter(LocalDateTime.now())) {
            return statusResp;
        }
        WordPkConfig pkConfig = null;
        LocalDateTime now = LocalDateTime.now();
        for (WordPkConfig config : pkConfigList) {
            if (now.isAfter(config.getStartTime()) && now.isBefore(config.getStopTime())) {
                pkConfig = config;
                break;
            }
            if (now.isBefore(config.getStartTime())) {
                break;
            }
            pkConfig = config;
        }
        assert pkConfig != null;
        statusResp.setPkId(pkConfig.getId());
        statusResp.setStartTime(DateUtils.toMillis(pkConfig.getStartTime()));
        statusResp.setStopTime(DateUtils.toMillis(pkConfig.getStopTime()));
        if (now.isAfter(pkConfig.getStartTime()) && now.isBefore(pkConfig.getStopTime())) {
            statusResp.setStatus((byte) 2);
        } else {
            statusResp.setStatus((byte) 3);
        }
        return statusResp;
    }

    @Cacheable(value = "pk:id:${#scheduleId}")
    public Long getPkId(Long scheduleId) {
        List<WordPkConfig> pkConfigList = wordPkConfigMapper.selectList(new QueryWrapper<WordPkConfig>()
                .eq(WordPkConfig.SCHEDULE_ID, scheduleId).orderByAsc(WordPkConfig.START_TIME));
        if (pkConfigList.isEmpty() || pkConfigList.getFirst().getStartTime().isAfter(LocalDateTime.now())) {
            return null;
        }
        LocalDateTime now = LocalDateTime.now();
        WordPkConfig pkConfig = null;
        for (WordPkConfig config : pkConfigList) {
            if (now.isAfter(config.getStartTime()) && now.isBefore(config.getStopTime())) {
                pkConfig = config;
                break;
            }
            if (now.isBefore(config.getStartTime())) {
                break;
            }
            pkConfig = config;
        }
        return pkConfig != null ? pkConfig.getId() : null;
    }
}
