package com.fuyingedu.training.front.controller;

import com.fuyingedu.training.common.model.CommResp;
import com.fuyingedu.training.common.model.PageReq;
import com.fuyingedu.training.front.model.message.*;
import com.fuyingedu.training.front.service.FrontMessageService;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 扶小鹰端-消息列表
 */
@RestController
@RequestMapping("fxy/message")
@Slf4j
public class FxyMessageController {

    @Autowired
    private FrontMessageService frontMessageService;

    /**
     * 系统消息
     */
    @GetMapping("system")
    public CommResp<List<SystemResp>> system(
            @RequestParam Long orderId,
            PageReq req
    ) {
        return frontMessageService.system(orderId, req);
    }

    /**
     * 点赞消息
     */
    @GetMapping("like")
    public CommResp<List<LikeResp>> like(
            @RequestParam Long orderId,
            PageReq req
    ) {
        return frontMessageService.like(orderId, req);
    }

    /**
     * 点评消息
     */
    @GetMapping("remark")
    public CommResp<List<RemarkResp>> remark(
            @RequestParam Long orderId,
            PageReq req
    ) {
        return frontMessageService.remark(orderId, req);
    }

    /**
     * 阅读消息
     */
    @PostMapping("read")
    public CommResp<?> read(
            @RequestBody @Valid ReadReq req
    ) {
        return frontMessageService.read(req);
    }

    /**
     * 消息状态
     */
    @GetMapping("status")
    public CommResp<StatusResp> status(
            @RequestParam Long orderId
    ) {
        return frontMessageService.status(orderId);
    }

}
