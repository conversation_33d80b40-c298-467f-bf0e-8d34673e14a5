package com.fuyingedu.training.front.model.task;

import com.fuyingedu.training.front.model.media.MediaResp;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDate;
import java.util.List;

@ToString
@Getter
@Setter
public class HomeworkResp {

    private Long id;
    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 任务介绍
     */
    private String taskContent;

    /**
     * 开始时间
     */
    private LocalDate startDate;

    /**
     * 截止时间
     */
    private LocalDate endDate;
    /**
     * 完成状态 1-未完成 2-已完成
     */
    private Byte taskStatus;

    /**
     * 提交状态 0-可提交 1-不可以提交
     */
    private Byte submitStatus;

    /**
     * 作业案例
     */
    private List<MediaResp> homeworkCaseList;
}
