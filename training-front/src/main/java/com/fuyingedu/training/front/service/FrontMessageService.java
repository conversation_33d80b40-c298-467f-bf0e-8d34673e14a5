package com.fuyingedu.training.front.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fuyingedu.training.common.model.CommResp;
import com.fuyingedu.training.common.model.PageReq;
import com.fuyingedu.training.common.util.RespUtils;
import com.fuyingedu.training.entity.*;
import com.fuyingedu.training.front.model.message.*;
import com.fuyingedu.training.mapper.*;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class FrontMessageService {

    @Autowired
    private OperationLogMapper operationLogMapper;
    @Autowired
    private TeacherMapper teacherMapper;
    @Autowired
    private OrderRewardLogMapper orderRewardLogMapper;
    @Autowired
    private OrderFxyRewardLogMapper orderFxyRewardLogMapper;
    @Autowired
    private WordRewardLogMapper wordRewardLogMapper;
    @Autowired
    private TaskSubmitRecordMapper taskSubmitRecordMapper;
    @Autowired
    private TaskRecordLikeMapper taskRecordLikeMapper;
    @Autowired
    private TaskRecordRemarkMapper taskRecordRemarkMapper;
    @Autowired
    private TaskMapper taskMapper;
    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private UserMapper userMapper;
    @Autowired
    private ScheduleTeacherMapper scheduleTeacherMapper;
    @Autowired
    private MedalMapper medalMapper;
    @Autowired
    private MedalRecordMapper medalRecordMapper;
    @Autowired
    private OrderMedalMapper orderMedalMapper;
    @Autowired
    private OrderMessageReadMapper orderMessageReadMapper;

    public CommResp<List<SystemResp>> system(Long orderId, PageReq req) {
        Long readMaxId = 0L;
        OrderMessageRead orderMessageRead = orderMessageReadMapper.selectOne(new LambdaQueryWrapper<OrderMessageRead>().select(
                OrderMessageRead::getId, OrderMessageRead::getSystemMessage
        ).eq(OrderMessageRead::getId, orderId));
        if (orderMessageRead != null) {
            readMaxId = orderMessageRead.getSystemMessage();
        }
        IPage<OperationLog> page = new Page<>(req.getPageNum(), req.getPageSize());
        page = operationLogMapper.selectPage(page, new LambdaQueryWrapper<OperationLog>().select(
                        OperationLog::getId, OperationLog::getOperationType, OperationLog::getCreatedTime,
                        OperationLog::getNextContent, OperationLog::getNextTeacher
                ).eq(OperationLog::getOrderId, orderId).eq(OperationLog::getLogStatus, 1).in(OperationLog::getOperationType, 2, 6, 8, 10, 14, 15, 16, 17, 18)
                .orderByDesc(OperationLog::getId));
        if (page.getRecords().isEmpty()) {
            return RespUtils.success(req.getPageNum(), req.getPageSize(), page.getTotal(), Collections.emptyList());
        }
        Map<Long, Teacher> teacherMap = teacherMapper.selectList(new LambdaQueryWrapper<Teacher>().select(
                        Teacher::getId, Teacher::getRealName
                ).in(Teacher::getId, page.getRecords().stream().map(OperationLog::getNextTeacher).toList()))
                .stream().collect(Collectors.toMap(Teacher::getId, teacher -> teacher));
        List<Long> logIds = page.getRecords().stream().filter(item -> item.getOperationType() == 14).map(OperationLog::getNextContent).toList();
        Map<Long, OrderRewardLog> rewardLogMap = Collections.emptyMap();
        if (!logIds.isEmpty()) {
            rewardLogMap = orderRewardLogMapper.selectList(new LambdaQueryWrapper<OrderRewardLog>().select(
                            OrderRewardLog::getId, OrderRewardLog::getTaskReward, OrderRewardLog::getLogRemark
                    ).in(OrderRewardLog::getId, logIds))
                    .stream().collect(Collectors.toMap(OrderRewardLog::getId, Function.identity()));
        }
        List<Long> fxyLogIds = page.getRecords().stream().filter(item -> item.getOperationType() == 15).map(OperationLog::getNextContent).toList();
        Map<Long, OrderFxyRewardLog> fxyRewardLogMap = Collections.emptyMap();
        if (!fxyLogIds.isEmpty()) {
            fxyRewardLogMap = orderFxyRewardLogMapper.selectList(new LambdaQueryWrapper<OrderFxyRewardLog>().select(
                    OrderFxyRewardLog::getId, OrderFxyRewardLog::getTaskReward, OrderFxyRewardLog::getLogRemark
            ).in(OrderFxyRewardLog::getId, fxyLogIds)).stream().collect(Collectors.toMap(OrderFxyRewardLog::getId, Function.identity()));
        }
        List<Long> wordLogIds = page.getRecords().stream().filter(item -> item.getOperationType() == 16).map(OperationLog::getNextContent).toList();
        Map<Long, WordRewardLog> wordRewardLogMap = Collections.emptyMap();
        if (!wordLogIds.isEmpty()) {
            wordRewardLogMap = wordRewardLogMapper.selectList(new LambdaQueryWrapper<WordRewardLog>().select(
                    WordRewardLog::getId, WordRewardLog::getRewardNum, WordRewardLog::getRemark
            ).in(WordRewardLog::getId, wordLogIds)).stream().collect(Collectors.toMap(WordRewardLog::getId, Function.identity()));
        }
        List<Long> recordIds = page.getRecords().stream().filter(item -> item.getOperationType() == 17).map(OperationLog::getNextContent).toList();
        Map<Long, String> taskNameMap = Collections.emptyMap();
        if (!recordIds.isEmpty()) {
            Map<Long, Long> recordTaskIdMap = taskSubmitRecordMapper.selectList(new LambdaQueryWrapper<TaskSubmitRecord>().select(
                    TaskSubmitRecord::getId, TaskSubmitRecord::getTaskId
            ).in(TaskSubmitRecord::getId, recordIds)).stream().collect(Collectors.toMap(TaskSubmitRecord::getId, TaskSubmitRecord::getTaskId));
            Map<Long, String> taskMap = taskMapper.selectList(new LambdaQueryWrapper<Task>().select(
                    Task::getId, Task::getTaskName
            ).in(Task::getId, recordTaskIdMap.values())).stream().collect(Collectors.toMap(Task::getId, Task::getTaskName));
            taskNameMap = new HashMap<>(recordTaskIdMap.size());
            for (Map.Entry<Long, Long> entry : recordTaskIdMap.entrySet()) {
                taskNameMap.put(entry.getKey(), taskMap.get(entry.getValue()));
            }
        }
        List<Long> medalRecordIds = page.getRecords().stream().filter(item -> item.getOperationType() == 18).map(OperationLog::getNextContent).toList();
        Map<Long, String> medalNameMap = Collections.emptyMap();
        if (!medalRecordIds.isEmpty()) {
            Map<Long, Long> medalRecordMedalIdMap = orderMedalMapper.selectList(new LambdaQueryWrapper<OrderMedal>().select(
                    OrderMedal::getId, OrderMedal::getMedalId
            ).in(OrderMedal::getId, medalRecordIds)).stream().collect(Collectors.toMap(OrderMedal::getId, OrderMedal::getMedalId));
            Map<Long, String> medalMap = medalRecordMapper.selectList(new LambdaQueryWrapper<MedalRecord>().select(
                    MedalRecord::getId, MedalRecord::getMedalName
            ).in(MedalRecord::getId, medalRecordMedalIdMap.values())).stream().collect(Collectors.toMap(MedalRecord::getId, MedalRecord::getMedalName));
            medalNameMap = new HashMap<>();
            for (Map.Entry<Long, Long> entry : medalRecordMedalIdMap.entrySet()) {
                medalNameMap.put(entry.getKey(), medalMap.get(entry.getValue()));
            }
        }
        List<SystemResp> respList = new ArrayList<>(page.getRecords().size());
        for (OperationLog operationLog : page.getRecords()) {
            SystemResp systemResp = new SystemResp();
            systemResp.setId(operationLog.getId());
            if (operationLog.getId() > readMaxId) {
                systemResp.setReadStatus(0);
            } else {
                systemResp.setReadStatus(1);
            }
            systemResp.setType(operationLog.getOperationType());
            systemResp.setCreatedTime(operationLog.getCreatedTime());
            Teacher teacher = teacherMap.get(operationLog.getNextTeacher());
            if (systemResp.getType() == 2) {
                systemResp.setContent(String.format("管理员已为你分配辅导老师%s，快去添加老师企业微信吧", teacher.getRealName()));
            } else if (systemResp.getType() == 6) {
                systemResp.setContent(String.format("你的辅导老师已更改为%s，快去添加老师企业微信吧", teacher.getRealName()));
            } else if (systemResp.getType() == 8) {
                systemResp.setContent("你的老师已为你分班，快去添加大志愿者企业微信，加入班级群吧");
            } else if (systemResp.getType() == 10) {
                systemResp.setContent("你的班级已更改，快去添加大志愿者企业微信，加入班级群吧");
            } else if (systemResp.getType() == 14) {
                OrderRewardLog rewardLog = rewardLogMap.get(operationLog.getNextContent());
                systemResp.setContent(String.format("你获得了%d个小红花，获得原因：%s", rewardLog.getTaskReward(), rewardLog.getLogRemark()));
            } else if (systemResp.getType() == 15) {
                OrderFxyRewardLog fxyRewardLog = fxyRewardLogMap.get(operationLog.getNextContent());
                systemResp.setContent(String.format("你获得了%d个小太阳，获得原因：%s", fxyRewardLog.getTaskReward(), fxyRewardLog.getLogRemark()));
            } else if (systemResp.getType() == 16) {
                WordRewardLog wordRewardLog = wordRewardLogMap.get(operationLog.getNextContent());
                systemResp.setContent(String.format("你获得了%d个Word鹰积分，获得原因：%s", wordRewardLog.getRewardNum(), wordRewardLog.getRemark()));
            } else if (systemResp.getType() == 17) {
                systemResp.setContent(String.format("你的作业%s已推荐为优秀作业。", taskNameMap.get(operationLog.getNextContent())));
            } else if (systemResp.getType() == 18) {
                systemResp.setContent(String.format("你获得了1个奖状《%s》", medalNameMap.get(operationLog.getNextContent())));
            }
            respList.add(systemResp);
        }
        return RespUtils.success(req.getPageNum(), req.getPageSize(), page.getTotal(), respList);
    }

    public CommResp<List<LikeResp>> like(Long orderId, PageReq req) {
        Long readMaxId = 0L;
        OrderMessageRead orderMessageRead = orderMessageReadMapper.selectOne(new LambdaQueryWrapper<OrderMessageRead>().select(
                OrderMessageRead::getId, OrderMessageRead::getLikeMessage
        ).eq(OrderMessageRead::getId, orderId));
        if (orderMessageRead != null) {
            readMaxId = orderMessageRead.getLikeMessage();
        }
        List<TaskSubmitRecord> taskSubmitRecords = taskSubmitRecordMapper.selectList(new LambdaQueryWrapper<TaskSubmitRecord>().select(
                TaskSubmitRecord::getId, TaskSubmitRecord::getTaskId
        ).eq(TaskSubmitRecord::getOrderId, orderId).orderByDesc(TaskSubmitRecord::getId).last("limit 100"));
        if (taskSubmitRecords.isEmpty()) {
            return RespUtils.success(req.getPageNum(), req.getPageSize(), 0, Collections.emptyList());
        }
        Map<Long, TaskSubmitRecord> recordMap = taskSubmitRecords.stream().collect(Collectors.toMap(TaskSubmitRecord::getId, Function.identity()));
        Set<Long> taskIds = taskSubmitRecords.stream().map(TaskSubmitRecord::getTaskId).collect(Collectors.toSet());
        Map<Long, Task> taskMap = taskMapper.selectList(new QueryWrapper<Task>().select(Task.ID, Task.TASK_NAME).in(Task.ID, taskIds))
                .stream().collect(Collectors.toMap(Task::getId, Function.identity()));
        List<Long> recordIds = taskSubmitRecords.stream().map(TaskSubmitRecord::getId).toList();
        IPage<TaskRecordLike> page = new Page<>(req.getPageNum(), req.getPageSize());
        page = taskRecordLikeMapper.selectPage(page, new LambdaQueryWrapper<TaskRecordLike>().select(
                TaskRecordLike::getId, TaskRecordLike::getCreatedTime, TaskRecordLike::getRecordId, TaskRecordLike::getOrderId
        ).in(TaskRecordLike::getRecordId, recordIds).orderByDesc(TaskRecordLike::getId));
        if (page.getRecords().isEmpty()) {
            return RespUtils.success(req.getPageNum(), req.getPageSize(), page.getTotal(), Collections.emptyList());
        }
        Set<Long> orderIds = page.getRecords().stream().map(TaskRecordLike::getOrderId).collect(Collectors.toSet());
        Map<Long, Order> orderMap = orderMapper.selectList(new LambdaQueryWrapper<Order>().select(Order::getId, Order::getUserId, Order::getOrderRemark)
                .in(Order::getId, orderIds)).stream().collect(Collectors.toMap(Order::getId, Function.identity()));
        Set<Long> userIds = orderMap.values().stream().map(Order::getUserId).collect(Collectors.toSet());
        Map<Long, User> userMap = userMapper.selectList(new QueryWrapper<User>().select(User.ID, User.REAL_NAME, User.USER_ICON)
                .in(User.ID, userIds)).stream().collect(Collectors.toMap(User::getId, Function.identity()));
        List<LikeResp> likeRespList = new ArrayList<>();
        for (TaskRecordLike like : page.getRecords()) {
            LikeResp likeResp = new LikeResp();
            likeResp.setId(like.getId());
            if (like.getId() > readMaxId) {
                likeResp.setReadStatus(0);
            } else {
                likeResp.setReadStatus(1);
            }
            likeResp.setCreatedTime(like.getCreatedTime());
            Order order = orderMap.get(like.getOrderId());
            User user = userMap.get(order.getUserId());
            likeResp.setUserIcon(user.getUserIcon());
            if (StringUtils.hasLength(order.getOrderRemark())) {
                likeResp.setNickName(order.getOrderRemark());
            } else {
                likeResp.setNickName(user.getRealName());
            }
            likeResp.setRecordId(like.getRecordId());
            TaskSubmitRecord record = recordMap.get(like.getRecordId());
            Task task = taskMap.get(record.getTaskId());
            likeResp.setTaskId(task.getId());
            likeResp.setTaskTitle(task.getTaskName());
            likeRespList.add(likeResp);
        }
        return RespUtils.success(req.getPageNum(), req.getPageSize(), page.getTotal(), likeRespList);
    }

    public CommResp<List<RemarkResp>> remark(Long orderId, PageReq req) {
        Long readMaxId = 0L;
        OrderMessageRead orderMessageRead = orderMessageReadMapper.selectOne(new LambdaQueryWrapper<OrderMessageRead>().select(
                OrderMessageRead::getId, OrderMessageRead::getRemarkMessage
        ).eq(OrderMessageRead::getId, orderId));
        if (orderMessageRead != null) {
            readMaxId = orderMessageRead.getRemarkMessage();
        }
        List<TaskSubmitRecord> taskSubmitRecords = taskSubmitRecordMapper.selectList(new LambdaQueryWrapper<TaskSubmitRecord>().select(
                TaskSubmitRecord::getId, TaskSubmitRecord::getTaskId
        ).eq(TaskSubmitRecord::getOrderId, orderId).orderByDesc(TaskSubmitRecord::getId).last("limit 100"));
        if (taskSubmitRecords.isEmpty()) {
            return RespUtils.success(req.getPageNum(), req.getPageSize(), 0, Collections.emptyList());
        }
        Map<Long, TaskSubmitRecord> recordMap = taskSubmitRecords.stream().collect(Collectors.toMap(TaskSubmitRecord::getId, Function.identity()));
        Set<Long> taskIds = taskSubmitRecords.stream().map(TaskSubmitRecord::getTaskId).collect(Collectors.toSet());
        Map<Long, Task> taskMap = taskMapper.selectList(new QueryWrapper<Task>().select(Task.ID, Task.TASK_NAME, Task.TASK_TYPE).in(Task.ID, taskIds))
                .stream().collect(Collectors.toMap(Task::getId, Function.identity()));
        List<Long> recordIds = taskSubmitRecords.stream().map(TaskSubmitRecord::getId).toList();
        IPage<TaskRecordRemark> page = new Page<>(req.getPageNum(), req.getPageSize());
        page = taskRecordRemarkMapper.selectPage(page, new LambdaQueryWrapper<TaskRecordRemark>().select(
                TaskRecordRemark::getId, TaskRecordRemark::getCreatedTime, TaskRecordRemark::getRecordId, TaskRecordRemark::getCreateUserId
        ).in(TaskRecordRemark::getRecordId, recordIds).orderByDesc(TaskRecordRemark::getId));
        if (page.getRecords().isEmpty()) {
            return RespUtils.success(req.getPageNum(), req.getPageSize(), page.getTotal(), Collections.emptyList());
        }
        Set<Long> userIds = page.getRecords().stream().map(TaskRecordRemark::getCreateUserId).collect(Collectors.toSet());
        Map<Long, User> userMap = userMapper.selectList(new QueryWrapper<User>().select(User.ID, User.REAL_NAME, User.USER_ICON)
                .in(User.ID, userIds)).stream().collect(Collectors.toMap(User::getId, Function.identity()));
        Order order = orderMapper.selectOne(new LambdaQueryWrapper<Order>().select(Order::getId, Order::getClazzId, Order::getScheduleId, Order::getGroupId)
                .eq(Order::getId, orderId));
        Order monitor = null;
        if (order.getClazzId() != null && order.getGroupId() != null) {
            monitor = orderMapper.selectOne(new LambdaQueryWrapper<Order>().select(Order::getId, Order::getUserId, Order::getOrderRemark)
                    .eq(Order::getClazzId, order.getClazzId()).eq(Order::getGroupId, order.getGroupId()).eq(Order::getStudentType, 2).last("limit 1"));
        }
        Map<Long, Teacher> teacherMap = teacherMapper.selectList(new QueryWrapper<Teacher>().select(Teacher.ID, Teacher.USER_ID, Teacher.REAL_NAME)
                .in(Teacher.USER_ID, userIds)).stream().collect(Collectors.toMap(Teacher::getUserId, Function.identity()));
        Map<Long, ScheduleTeacher> scheduleTeacherMap = Collections.emptyMap();
        if (!teacherMap.isEmpty()) {
            scheduleTeacherMap = scheduleTeacherMapper.selectList(new LambdaQueryWrapper<ScheduleTeacher>().select(
                                    ScheduleTeacher::getId, ScheduleTeacher::getTeacherId, ScheduleTeacher::getTeacherType
                            ).eq(ScheduleTeacher::getScheduleId, order.getScheduleId())
                            .in(ScheduleTeacher::getTeacherId, teacherMap.values().stream().map(Teacher::getId).toList()))
                    .stream().collect(Collectors.toMap(ScheduleTeacher::getTeacherId, Function.identity()));
        }
        List<RemarkResp> respList = new ArrayList<>();
        for (TaskRecordRemark like : page.getRecords()) {
            RemarkResp resp = new RemarkResp();
            resp.setId(like.getId());
            if (like.getId() > readMaxId) {
                resp.setReadStatus(0);
            } else {
                resp.setReadStatus(1);
            }
            resp.setCreatedTime(like.getCreatedTime());
            User user = userMap.get(like.getCreateUserId());
            resp.setUserIcon(user.getUserIcon());
            Teacher teacher = teacherMap.get(like.getCreateUserId());
            if (teacher != null) {
                ScheduleTeacher scheduleTeacher = scheduleTeacherMap.get(teacher.getId());
                if (scheduleTeacher != null) {
                    resp.setNickName(teacher.getRealName());
                    resp.setRemarkType(scheduleTeacher.getTeacherType());
                }
            }
            if (resp.getRemarkType() == null) {
                resp.setRemarkType((byte) 3);
                if (monitor != null && monitor.getUserId().equals(like.getCreateUserId()) && StringUtils.hasLength(monitor.getOrderRemark())) {
                    resp.setNickName(monitor.getOrderRemark());
                } else {
                    resp.setNickName(user.getRealName());
                }
            }
            resp.setRecordId(like.getRecordId());
            TaskSubmitRecord record = recordMap.get(like.getRecordId());
            Task task = taskMap.get(record.getTaskId());
            resp.setTaskId(task.getId());
            resp.setTaskTitle(task.getTaskName());
            resp.setTaskType(task.getTaskType());
            respList.add(resp);
        }
        return RespUtils.success(req.getPageNum(), req.getPageSize(), page.getTotal(), respList);
    }

    public CommResp<?> read(@Valid ReadReq req) {
        OrderMessageRead orderMessageRead = orderMessageReadMapper.selectOne(new LambdaQueryWrapper<OrderMessageRead>().select(
                OrderMessageRead::getId
        ).eq(OrderMessageRead::getId, req.getOrderId()));
        if (orderMessageRead == null) {
            orderMessageRead = new OrderMessageRead();
            orderMessageRead.setId(req.getOrderId());
            orderMessageReadMapper.insert(orderMessageRead);
        }
        if (req.getType() == 1) {
            orderMessageRead.setSystemMessage(req.getMessageId());
        } else if (req.getType() == 2) {
            orderMessageRead.setLikeMessage(req.getMessageId());
        } else if (req.getType() == 3) {
            orderMessageRead.setRemarkMessage(req.getMessageId());
        }
        orderMessageReadMapper.updateById(orderMessageRead);
        return RespUtils.success();
    }

    public CommResp<StatusResp> status(Long orderId) {
        OrderMessageRead orderMessageRead = orderMessageReadMapper.selectOne(new LambdaQueryWrapper<OrderMessageRead>().select(
                OrderMessageRead::getId, OrderMessageRead::getSystemMessage, OrderMessageRead::getLikeMessage, OrderMessageRead::getRemarkMessage
        ).eq(OrderMessageRead::getId, orderId));
        OperationLog operationLog = operationLogMapper.selectOne(new LambdaQueryWrapper<OperationLog>().select(
                        OperationLog::getId, OperationLog::getOperationType, OperationLog::getCreatedTime,
                        OperationLog::getNextContent, OperationLog::getNextTeacher
                ).eq(OperationLog::getOrderId, orderId).eq(OperationLog::getLogStatus, 1).in(OperationLog::getOperationType, 2, 6, 8, 10, 14, 15, 16, 17, 18)
                .orderByDesc(OperationLog::getId).last("limit 1"));
        StatusResp statusResp = new StatusResp();
        if (operationLog != null) {
            statusResp.setSystemMsgTime(operationLog.getCreatedTime());
            if (operationLog.getOperationType() == 2) {
                Teacher teacher = teacherMapper.selectOne(new LambdaQueryWrapper<Teacher>().select(Teacher::getId, Teacher::getRealName)
                        .eq(Teacher::getId, operationLog.getNextTeacher()));
                statusResp.setSystemContent(String.format("管理员已为你分配辅导老师%s，快去添加老师企业微信吧", teacher.getRealName()));
            } else if (operationLog.getOperationType() == 6) {
                Teacher teacher = teacherMapper.selectOne(new LambdaQueryWrapper<Teacher>().select(Teacher::getId, Teacher::getRealName)
                        .eq(Teacher::getId, operationLog.getNextTeacher()));
                statusResp.setSystemContent(String.format("你的辅导老师已更改为%s，快去添加老师企业微信吧", teacher.getRealName()));
            } else if (operationLog.getOperationType() == 8) {
                statusResp.setSystemContent("你的老师已为你分班，快去添加大志愿者企业微信，加入班级群吧");
            } else if (operationLog.getOperationType() == 10) {
                statusResp.setSystemContent("你的班级已更改，快去添加大志愿者企业微信，加入班级群吧");
            } else if (operationLog.getOperationType() == 14) {
                OrderRewardLog rewardLog = orderRewardLogMapper.selectOne(new LambdaQueryWrapper<OrderRewardLog>().select(
                        OrderRewardLog::getId, OrderRewardLog::getTaskReward, OrderRewardLog::getLogRemark
                ).eq(OrderRewardLog::getId, operationLog.getNextContent()));
                statusResp.setSystemContent(String.format("你获得了%d个小红花，获得原因：%s", rewardLog.getTaskReward(), rewardLog.getLogRemark()));
            } else if (operationLog.getOperationType() == 15) {
                OrderFxyRewardLog fxyRewardLog = orderFxyRewardLogMapper.selectOne(new LambdaQueryWrapper<OrderFxyRewardLog>().select(
                        OrderFxyRewardLog::getId, OrderFxyRewardLog::getTaskReward, OrderFxyRewardLog::getLogRemark
                ).eq(OrderFxyRewardLog::getId, operationLog.getNextContent()));
                statusResp.setSystemContent(String.format("你获得了%d个小太阳，获得原因：%s", fxyRewardLog.getTaskReward(), fxyRewardLog.getLogRemark()));
            } else if (operationLog.getOperationType() == 16) {
                WordRewardLog wordRewardLog = wordRewardLogMapper.selectOne(new LambdaQueryWrapper<WordRewardLog>().select(
                        WordRewardLog::getId, WordRewardLog::getRewardNum, WordRewardLog::getRemark
                ).eq(WordRewardLog::getId, operationLog.getNextContent()));
                statusResp.setSystemContent(String.format("你获得了%d个Word鹰积分，获得原因：%s", wordRewardLog.getRewardNum(), wordRewardLog.getRemark()));
            } else if (operationLog.getOperationType() == 17) {
                TaskSubmitRecord record = taskSubmitRecordMapper.selectOne(new LambdaQueryWrapper<TaskSubmitRecord>().select(
                        TaskSubmitRecord::getId, TaskSubmitRecord::getTaskId
                ).eq(TaskSubmitRecord::getId, operationLog.getNextContent()));
                Task task = taskMapper.selectOne(new LambdaQueryWrapper<Task>().select(
                        Task::getId, Task::getTaskName
                ).in(Task::getId, record.getTaskId()));
                statusResp.setSystemContent(String.format("你的作业%s已推荐为优秀作业。", task.getTaskName()));
            } else if (operationLog.getOperationType() == 18) {
                OrderMedal orderMedal = orderMedalMapper.selectOne(new LambdaQueryWrapper<OrderMedal>().select(
                        OrderMedal::getId, OrderMedal::getMedalId
                ).eq(OrderMedal::getId, operationLog.getNextContent()));
                MedalRecord medal = medalRecordMapper.selectOne(new LambdaQueryWrapper<MedalRecord>().select(
                        MedalRecord::getId, MedalRecord::getMedalName
                ).in(MedalRecord::getId, orderMedal.getMedalId()));
                statusResp.setSystemContent(String.format("你获得了1个奖状《%s》", medal.getMedalName()));
            }
            if (orderMessageRead == null || orderMessageRead.getSystemMessage() < operationLog.getId()) {
                statusResp.setSystemStatus(0);
            } else {
                statusResp.setSystemStatus(1);
            }
        } else {
            statusResp.setSystemStatus(1);
        }
        List<TaskSubmitRecord> taskSubmitRecords = taskSubmitRecordMapper.selectList(new LambdaQueryWrapper<TaskSubmitRecord>().select(
                TaskSubmitRecord::getId, TaskSubmitRecord::getTaskId
        ).eq(TaskSubmitRecord::getOrderId, orderId).orderByDesc(TaskSubmitRecord::getId).last("limit 100"));
        if (taskSubmitRecords.isEmpty()) {
            statusResp.setLikeStatus(1);
            statusResp.setRemarkStatus(1);
            return RespUtils.success(statusResp);
        }
        List<Long> recordIds = taskSubmitRecords.stream().map(TaskSubmitRecord::getId).collect(Collectors.toList());
        TaskRecordLike taskRecordLike = taskRecordLikeMapper.selectOne(new LambdaQueryWrapper<TaskRecordLike>().select(
                TaskRecordLike::getId, TaskRecordLike::getCreatedTime, TaskRecordLike::getOrderId, TaskRecordLike::getRecordId
        ).in(TaskRecordLike::getRecordId, recordIds).orderByDesc(TaskRecordLike::getId).last("limit 1"));
        if (taskRecordLike != null) {
            Order order = orderMapper.selectOne(new LambdaQueryWrapper<Order>().select(
                    Order::getId, Order::getOrderRemark, Order::getUserId
            ).eq(Order::getId, taskRecordLike.getOrderId()));
            User user = userMapper.selectOne(new LambdaQueryWrapper<User>().select(
                    User::getId, User::getRealName
            ).eq(User::getId, order.getUserId()));
            String username;
            if (StringUtils.hasLength(order.getOrderRemark())) {
                username = order.getOrderRemark();
            } else {
                username = user.getRealName();
            }
            TaskSubmitRecord record = taskSubmitRecordMapper.selectOne(new LambdaQueryWrapper<TaskSubmitRecord>().select(
                    TaskSubmitRecord::getId, TaskSubmitRecord::getTaskId
            ).eq(TaskSubmitRecord::getId, taskRecordLike.getRecordId()));
            Task task = taskMapper.selectOne(new LambdaQueryWrapper<Task>().select(
                    Task::getId, Task::getTaskName
            ).in(Task::getId, record.getTaskId()));
            statusResp.setLikeContent(String.format("%s 点赞了你的作业：%s", username, task.getTaskName()));
            statusResp.setLikeStatus(1);
            statusResp.setLikeMsgTime(taskRecordLike.getCreatedTime());
            if (orderMessageRead == null || orderMessageRead.getLikeMessage() < taskRecordLike.getId()) {
                statusResp.setLikeStatus(0);
            }
        } else {
            statusResp.setLikeStatus(1);
        }
        TaskRecordRemark taskRecordRemark = taskRecordRemarkMapper.selectOne(new LambdaQueryWrapper<TaskRecordRemark>().select(
                TaskRecordRemark::getId, TaskRecordRemark::getRecordId, TaskRecordRemark::getCreatedTime, TaskRecordRemark::getCreateUserId
        ).in(TaskRecordRemark::getRecordId, recordIds).orderByDesc(TaskRecordRemark::getId).last("limit 1"));
        if (taskRecordRemark != null) {
            Order order = orderMapper.selectOne(new LambdaQueryWrapper<Order>().select(
                    Order::getId, Order::getClazzId, Order::getScheduleId, Order::getGroupId
            ).eq(Order::getId, orderId));
            Teacher teacher = teacherMapper.selectOne(new QueryWrapper<Teacher>().select(Teacher.ID, Teacher.USER_ID, Teacher.REAL_NAME)
                            .eq(Teacher.USER_ID, taskRecordRemark.getCreateUserId()));
            String username = null;
            if (teacher != null) {
                ScheduleTeacher scheduleTeacher = scheduleTeacherMapper.selectOne(new LambdaQueryWrapper<ScheduleTeacher>().select(
                                ScheduleTeacher::getId
                        ).eq(ScheduleTeacher::getScheduleId, order.getScheduleId())
                        .eq(ScheduleTeacher::getTeacherId, teacher.getId()));
                if (scheduleTeacher != null) {
                    username = teacher.getRealName();
                }
            }
            if (username == null) {
                Order monitor = orderMapper.selectOne(new LambdaQueryWrapper<Order>().select(
                        Order::getId, Order::getOrderRemark, Order::getUserId
                ).eq(Order::getClazzId, order.getClazzId())
                        .eq(order.getGroupId() != null, Order::getGroupId, order.getGroupId()).eq(Order::getStudentType, 2)
                        .eq(Order::getUserId, taskRecordRemark.getCreateUserId()).last("limit 1"));
                if (order.getGroupId() != null && monitor != null && StringUtils.hasLength(monitor.getOrderRemark())) {
                    username = monitor.getOrderRemark();
                } else {
                    User user = userMapper.selectOne(new LambdaQueryWrapper<User>().select(
                            User::getId, User::getRealName
                    ).eq(User::getId, taskRecordRemark.getCreateUserId()));
                    username = user.getRealName();
                }
            }
            TaskSubmitRecord record = taskSubmitRecordMapper.selectOne(new LambdaQueryWrapper<TaskSubmitRecord>().select(
                    TaskSubmitRecord::getId, TaskSubmitRecord::getTaskId
            ).eq(TaskSubmitRecord::getId, taskRecordRemark.getRecordId()));
            Task task = taskMapper.selectOne(new LambdaQueryWrapper<Task>().select(
                    Task::getId, Task::getTaskName
            ).in(Task::getId, record.getTaskId()));
            statusResp.setRemarkContent(String.format("%s 点评了你的作业：%s", username, task.getTaskName()));
            statusResp.setRemarkStatus(1);
            statusResp.setRemarkMsgTime(taskRecordRemark.getCreatedTime());
            if (orderMessageRead == null || orderMessageRead.getRemarkMessage() < taskRecordRemark.getId()) {
                statusResp.setRemarkStatus(0);
            }
        } else {
            statusResp.setRemarkStatus(1);
        }
        return RespUtils.success(statusResp);
    }
}
