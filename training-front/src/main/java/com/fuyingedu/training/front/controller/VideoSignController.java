package com.fuyingedu.training.front.controller;

import com.fuyingedu.training.common.annotation.Login;
import com.fuyingedu.training.common.model.CommResp;
import com.fuyingedu.training.common.util.RespUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Map;

/**
 * 驰声智能语音管理
 */
@RestController
@Slf4j
@RequestMapping("front/video")
public class VideoSignController {

    private final MessageDigest md;
    private static final String CONFIG_APP_KEY = "1690164472000110"; //chivox appKey
    private static final String CONFIG_SECRET_KEY = "067cb144a1c29a2b0c8331961e0857cc"; //chivox secretKey

    public VideoSignController() throws NoSuchAlgorithmException {
        md = MessageDigest.getInstance("sha1");
    }

    /**
     * 获取签名
     */
    @GetMapping("sign")
    public CommResp<?> sign(@Login Long userId) {
        log.info("[{}]获取签名", userId);
        long timestamp = System.currentTimeMillis();
        String sig = CONFIG_APP_KEY + timestamp + CONFIG_SECRET_KEY;
        md.update(sig.getBytes());
        byte[] b = md.digest();
        int i;
        StringBuilder buf = new StringBuilder();
        for (byte value : b) {
            i = value;
            if (i < 0)
                i += 256;
            if (i < 16)
                buf.append("0");
            buf.append(Integer.toHexString(i));
        }
        return RespUtils.success(Map.of("timestamp", String.valueOf(timestamp), "sig", buf.toString(),
                "applicationId", CONFIG_APP_KEY, "alg", "sha1"));
    }
}
