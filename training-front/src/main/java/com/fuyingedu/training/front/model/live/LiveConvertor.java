package com.fuyingedu.training.front.model.live;

import com.fuyingedu.training.entity.Live;

import java.time.LocalDateTime;

public class LiveConvertor {

    public static byte getLiveStatus(Live live) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime endDate = live.getStartTime().plusMinutes(live.getLiveDuration());
        if (live.getStartTime().isAfter(now)) {
            return (byte) 1;
        } else if (endDate.isAfter(now)) {
            return (byte) 2;
        } else if (live.getRepeatStartTime() != null && live.getRepeatEndTime() != null
                && live.getRepeatStartTime().isBefore(now)
                && live.getRepeatEndTime().isAfter(now)) {
            return (byte) 3;
        } else {
            return (byte) 4;
        }
    }
}
