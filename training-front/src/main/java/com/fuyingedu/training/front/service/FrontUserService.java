package com.fuyingedu.training.front.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.fuyingedu.training.common.enums.RespMetaEnum;
import com.fuyingedu.training.common.enums.TaskType;
import com.fuyingedu.training.common.exception.WebBaseException;
import com.fuyingedu.training.common.model.CommResp;
import com.fuyingedu.training.common.util.RespUtils;
import com.fuyingedu.training.entity.*;
import com.fuyingedu.training.front.manager.CheckAuthManager;
import com.fuyingedu.training.front.model.user.*;
import com.fuyingedu.training.mapper.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@Slf4j
public class FrontUserService {

    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private OrderFxyMapper orderFxyMapper;
    @Autowired
    private FxyMapper fxyMapper;
    @Autowired
    private UserMapper userMapper;
    @Autowired
    private MedalRecordMapper medalRecordMapper;
    @Autowired
    private OrderMedalMapper orderMedalMapper;
    @Autowired
    private OrderStatisticMapper orderStatisticMapper;
    @Autowired
    private OrderAssistantMapper orderAssistantMapper;
    @Autowired
    private CheckAuthManager checkAuthManager;
    @Autowired
    private TaskSubmitRecordMapper taskSubmitRecordMapper;
    @Autowired
    private LiveSignRecordMapper liveSignRecordMapper;
    @Autowired
    private WordRewardMapper wordRewardMapper;
    @Autowired
    private TaskMapper taskMapper;
    @Autowired
    private TaskRelationMapper taskRelationMapper;
    @Autowired
    private ClazzMapper clazzMapper;

    public CommResp<InfoResp> info(Long userId, Long orderId) {
        Order order = orderMapper.selectOne(new QueryWrapper<Order>().select(
                Order.USER_ID, Order.ID, Order.ORDER_REMARK, Order.SCHEDULE_ID, Order.TEACHER_ID, Order.CLAZZ_ID
        ).eq(Order.ID, orderId));
        checkAuthManager.checkOrder(userId, orderId, order.getUserId());
        return classmateInfo(orderId);
    }

    public CommResp<InfoResp> classmateInfo(Long orderId) {
        Order order = orderMapper.selectOne(new QueryWrapper<Order>().select(
                Order.ID, Order.USER_ID, Order.SCHEDULE_ID, Order.ORDER_REMARK
        ).eq(Order.ID, orderId));
        OrderStatistic orderRecord = orderStatisticMapper.selectOne(new QueryWrapper<OrderStatistic>().select(
                OrderStatistic.ENROLLMENT_NUM, OrderStatistic.PUNCH_NUM, OrderStatistic.HOMEWORK_NUM, OrderStatistic.REMARK_NUM,
                OrderStatistic.MEDAL_NUM, OrderStatistic.TASK_REWARD, OrderStatistic.REMARKED_NUM, OrderStatistic.GOOD_HOMEWORK_NUM,
                OrderStatistic.FXY_REWARD
        ).eq(OrderStatistic.ORDER_ID, orderId));
        User user = userMapper.selectOne(new QueryWrapper<User>().select(
                User.ID, User.REAL_NAME, User.USER_ICON
        ).eq(User.ID, order.getUserId()));
        OrderMedal orderMedal = orderMedalMapper.selectOne(new QueryWrapper<OrderMedal>().select(
                OrderMedal.MEDAL_ID
        ).eq(OrderMedal.ORDER_ID, orderId).orderByDesc(OrderMedal.ID).last("limit 1"));
        MedalRecord medalRecord = null;
        if (orderMedal != null) {
            medalRecord = medalRecordMapper.selectOne(new QueryWrapper<MedalRecord>().select(
                    MedalRecord.MEDAL_NAME, MedalRecord.MEDAL_ICON, MedalRecord.MEDAL_CONTENT,
                    MedalRecord.CREATED_TIME
            ).eq(MedalRecord.ID, orderMedal.getMedalId()));
        }
        InfoResp myInfoResp = toInfoResp(user, orderRecord, medalRecord);
        setTaskNum(order, myInfoResp);
        return RespUtils.success(myInfoResp);
    }

    private void setTaskNum(Order order, InfoResp infoResp) {
        if (order.getOrderRemark() != null) {
            infoResp.setRealName(order.getOrderRemark());
        }
        WordReward wordReward = wordRewardMapper.selectOne(new QueryWrapper<WordReward>().select(
                WordReward.USER_ID, WordReward.REWARD_NUM
        ).eq(WordReward.ORDER_ID, order.getId()));
        if (wordReward != null) {
            infoResp.setWordReward(wordReward.getRewardNum());
        } else {
            infoResp.setWordReward(0);
        }
        Long assistantId = null;
        if (order.getClazzId() != null) {
            Clazz clazz = clazzMapper.selectOne(new QueryWrapper<Clazz>().select(
                    Clazz.ID, Clazz.ASSISTANT_ID
            ).eq(Clazz.ID, order.getClazzId()));
            assistantId = clazz.getAssistantId();
        }
        List<Long> taskIds = taskRelationMapper.selectList(new LambdaQueryWrapper<TaskRelation>().select(
                                TaskRelation::getTaskId
                        ).eq(TaskRelation::getScheduleId, order.getScheduleId()).in(TaskRelation::getAssistantId, -1, assistantId)
                        .in(assistantId != null, TaskRelation::getAssistantId, assistantId, -1)
                        .in(order.getClazzId() != null, TaskRelation::getClazzId, order.getClazzId(), -1)
        ).stream().map(TaskRelation::getTaskId).toList();
        Set<Byte> taskTypeSet = Collections.emptySet();
        if (!taskIds.isEmpty()) {
            taskTypeSet = taskMapper.selectList(new QueryWrapper<Task>().select(Task.TASK_TYPE).in(Task.ID, taskIds))
                    .stream().map(Task::getTaskType).collect(Collectors.toSet());
        }
        if (!taskTypeSet.contains(TaskType.HOMEWORK.getCode())) {
            infoResp.setHomeworkNum(-1);
        } else {
            List<TaskSubmitRecord> taskSubmitRecords = taskSubmitRecordMapper.selectList(new QueryWrapper<TaskSubmitRecord>().select(
                    TaskSubmitRecord.ORDER_ID, TaskSubmitRecord.RECORD_TYPE
            ).eq(TaskSubmitRecord.ORDER_ID, order.getId()).eq(TaskSubmitRecord.TASK_TYPE, TaskType.HOMEWORK.getCode()));
            infoResp.setHomeworkNum(taskSubmitRecords.size());
            infoResp.setGoodHomeworkNum((int) taskSubmitRecords.stream().filter(taskSubmitRecord ->
                    Byte.valueOf((byte) 2).equals(taskSubmitRecord.getRecordType())).count());
        }
        if (!taskTypeSet.contains(TaskType.ENROLLMENT.getCode())) {
            infoResp.setEnrollmentNum(-1);
        }
        if (!taskTypeSet.contains(TaskType.PUNCH.getCode())) {
            infoResp.setPunchNum(-1);
        }
        Long count = liveSignRecordMapper.selectCount(new QueryWrapper<LiveSignRecord>().eq(LiveSignRecord.ORDER_ID, order.getId()));
        infoResp.setLiveNum(count);
    }

    private InfoResp toInfoResp(User user, OrderStatistic orderRecord, MedalRecord medalRecord) {
        InfoResp myInfoResp = new InfoResp();
        myInfoResp.setUserId(user.getId());
        myInfoResp.setUserIcon(user.getUserIcon());
        myInfoResp.setRealName(user.getRealName());

        myInfoResp.setEnrollmentNum(orderRecord.getEnrollmentNum());
        myInfoResp.setPunchNum(orderRecord.getPunchNum());
        myInfoResp.setTaskReward(orderRecord.getTaskReward());
        myInfoResp.setMedalNum(orderRecord.getMedalNum());
        myInfoResp.setFxyReward(orderRecord.getFxyReward());

        if (medalRecord != null) {
            myInfoResp.setMedalName(medalRecord.getMedalName());
            myInfoResp.setMedalIcon(medalRecord.getMedalIcon());
            myInfoResp.setTemplateName(medalRecord.getTemplateName());
            myInfoResp.setMediaContent(medalRecord.getMedalContent());
            myInfoResp.setMedalTime(medalRecord.getCreatedTime());
        }
        return myInfoResp;

    }

    public CommResp<List<MedalItemResp>> medalList(Long orderId) {
        Order order = orderMapper.selectOne(new QueryWrapper<Order>().select(
                Order.USER_ID, Order.ORDER_REMARK
        ).eq(Order.ID, orderId));
        List<Long> medalIds = orderMedalMapper.selectList(new QueryWrapper<OrderMedal>().select(
                OrderMedal.MEDAL_ID
        ).eq(OrderMedal.ORDER_ID, orderId)).stream().map(OrderMedal::getMedalId).toList();
        if (medalIds.isEmpty()) {
            return RespUtils.success(Collections.emptyList());
        }
        List<MedalRecord> medalRecords = medalRecordMapper.selectList(new QueryWrapper<MedalRecord>().select(
                MedalRecord.MEDAL_NAME, MedalRecord.MEDAL_ICON, MedalRecord.MEDAL_CONTENT,
                MedalRecord.CREATED_TIME
        ).in(MedalRecord.ID, medalIds));
        User user = userMapper.selectOne(new QueryWrapper<User>().select(
                User.ID, User.REAL_NAME, User.USER_ICON
        ).eq(User.ID, order.getUserId()));
        return RespUtils.success(medalRecords.stream().map(medalRecord -> toMedalItemResp(medalRecord, user, order)).toList());
    }

    private MedalItemResp toMedalItemResp(MedalRecord medalRecord, User user, Order order) {
        MedalItemResp medalItemResp = new MedalItemResp();
        medalItemResp.setMedalName(medalRecord.getMedalName());
        medalItemResp.setTemplateName(medalItemResp.getTemplateName());
        medalItemResp.setMedalIcon(medalRecord.getMedalIcon());
        medalItemResp.setMedalContent(medalRecord.getMedalContent());
        medalItemResp.setMedalTime(medalRecord.getCreatedTime());
        medalItemResp.setRealName(user.getRealName());
        if (StringUtils.hasLength(order.getOrderRemark())) {
            medalItemResp.setRealName(order.getOrderRemark());
        }
        return medalItemResp;
    }


    public CommResp<AccountInfoResp> accountInfo(Long userId, Long orderId) {
        Order order = orderMapper.selectOne(new QueryWrapper<Order>().select(
                Order.USER_ID, Order.STUDENT_ID
        ).eq(Order.ID, orderId));
        if (order == null) {
            throw new WebBaseException(RespMetaEnum.PARAM_ERROR);
        }
        AccountInfoResp accountInfoResp = new AccountInfoResp();
        accountInfoResp.setCurrUserId(userId);
        if (order.getUserId().equals(userId)) {
            accountInfoResp.setCurrAccountType((byte) 1);
        } else {
            accountInfoResp.setCurrAccountType((byte) 2);
        }
        OrderFxy orderFxy = orderFxyMapper.selectOne(new QueryWrapper<OrderFxy>().select(
                OrderFxy.FXY_ID
        ).eq(OrderFxy.ORDER_ID, orderId));
        if (orderFxy != null) {
            Fxy fxy = fxyMapper.selectOne(new QueryWrapper<Fxy>().select(
                    Fxy.OPEN_ID, Fxy.NICK_NAME, Fxy.ACCOUNT_INFO, Fxy.ICON_URL
            ).eq(Fxy.ID, orderFxy.getFxyId()));
            accountInfoResp.setOpenId(fxy.getOpenId());
            accountInfoResp.setAccountInfo(fxy.getAccountInfo());
            accountInfoResp.setIconUrl(fxy.getIconUrl());
            accountInfoResp.setNickname(fxy.getNickName());
        }
        Set<Long> userIds = orderAssistantMapper.selectList(new QueryWrapper<OrderAssistant>().select(
                OrderAssistant.USER_ID
        ).eq(OrderAssistant.ORDER_ID, orderId)).stream().map(OrderAssistant::getUserId).collect(Collectors.toSet());
        userIds.add(order.getUserId());
        if (!userIds.contains(userId)) {
            throw new WebBaseException(4000, "您没有绑定该训练营");
        }
        List<User> users = userMapper.selectList(new QueryWrapper<User>().select(
                User.ID, User.REAL_NAME, User.PHONE_NUM, User.USER_ICON
        ).in(User.ID, userIds));
        accountInfoResp.setUserInfoList(users.stream().map(user -> {
            AccountInfoResp.UserInfo userInfo = new AccountInfoResp.UserInfo();
            userInfo.setUserId(user.getId());
            userInfo.setRealName(user.getRealName());
            userInfo.setPhoneNum(user.getPhoneNum());
            userInfo.setUserIcon(user.getUserIcon());
            if (user.getId().equals(order.getUserId())) {
                userInfo.setAccountType((byte) 1);
            } else {
                userInfo.setAccountType((byte) 2);
            }
            return userInfo;
        }).toList());
        return RespUtils.success(accountInfoResp);
    }

    public void deleteUser(Long userId, DeleteUserReq deleteUserReq) {
        if (!userId.equals(deleteUserReq.getUserId())) {
            Order order = orderMapper.selectOne(new QueryWrapper<Order>().select(
                    Order.USER_ID).eq(Order.ID, deleteUserReq.getOrderId()));
            if (order == null || !order.getUserId().equals(userId)) {
                throw new WebBaseException(4000, "您不是该服务单的主账号，无法解绑");
            }
        }
        orderAssistantMapper.delete(new QueryWrapper<OrderAssistant>()
                .eq(OrderAssistant.ORDER_ID, deleteUserReq.getOrderId())
                .eq(OrderAssistant.USER_ID, deleteUserReq.getUserId())
        );
    }

    public CommResp<MessageNumResp> messageNum(Long userId) {
        User user = userMapper.selectOne(new QueryWrapper<User>().select(
                User.ID, User.LIVE_MESSAGE_NUM, User.REMARK_MESSAGE_NUM, User.TASK_MESSAGE_NUM
        ).eq(User.ID, userId));
        MessageNumResp messageNumResp = new MessageNumResp();
        messageNumResp.setLiveMessageStatus((byte) 1).setTaskMessageStatus((byte) 1).setTaskMessageStatus((byte) 1);
        if (user == null) {
            log.error("[{}]不存在", userId);
            return RespUtils.success(messageNumResp);
        }
        messageNumResp.setLiveMessageStatus(user.getLiveMessageNum() < 10 ? (byte) 1 : (byte) 2);
        messageNumResp.setRemarkMessageStatus(user.getRemarkMessageNum() < 10 ? (byte) 1 : (byte) 2);
        messageNumResp.setTaskMessageStatus(user.getTaskMessageNum() < 10 ? (byte) 1 : (byte) 2);
        return RespUtils.success(messageNumResp);
    }

    public void reportMessage(Long userId, ReportMessageReq reportMessageReq) {
        StringBuilder sql = new StringBuilder();
        for (Byte messageType : reportMessageReq.getMessageTypeList()) {
            if (Byte.valueOf((byte) 1).equals(messageType)) {
                sql.append("live_message_num = live_message_num + 1,");
            } else if (Byte.valueOf((byte) 2).equals(messageType)) {
                sql.append("remark_message_num = remark_message_num + 1,");
            } else if (Byte.valueOf((byte) 3).equals(messageType)) {
                sql.append("task_message_num = task_message_num + 1,");
            } else {
                throw new WebBaseException(4000, "消息类型错误");
            }
        }
        if (!sql.isEmpty()) {
            userMapper.update(new UpdateWrapper<User>()
                    .setSql(sql.substring(0, sql.length() - 1))
                    .eq(User.ID, userId)
            );
        }
    }

    public void update(Long userId, UpdateReq updateReq) {
        if (StringUtils.hasLength(updateReq.getNickName()) || StringUtils.hasLength(updateReq.getUserIcon())) {
            userMapper.update(new UpdateWrapper<User>()
                    .set(StringUtils.hasLength(updateReq.getNickName()), User.REAL_NAME, updateReq.getNickName())
                    .set(StringUtils.hasLength(updateReq.getUserIcon()), User.USER_ICON, updateReq.getUserIcon())
                    .eq(User.ID, userId)
            );
        }
    }
}
