package com.fuyingedu.training.front.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.fuyingedu.training.common.enums.CardType;
import com.fuyingedu.training.common.enums.OrderStatus;
import com.fuyingedu.training.common.enums.RespMetaEnum;
import com.fuyingedu.training.common.enums.ScheduleStatus;
import com.fuyingedu.training.common.exception.WebBaseException;
import com.fuyingedu.training.common.model.CommResp;
import com.fuyingedu.training.common.util.RespUtils;
import com.fuyingedu.training.common.util.StrUtils;
import com.fuyingedu.training.common.util.TransactionUtils;
import com.fuyingedu.training.dto.fuying.StudentRet;
import com.fuyingedu.training.entity.*;
import com.fuyingedu.training.front.feign.FuyingCourseFeign;
import com.fuyingedu.training.front.manager.CheckAuthManager;
import com.fuyingedu.training.front.model.feign.TraineeItem;
import com.fuyingedu.training.front.model.media.MediaConvertor;
import com.fuyingedu.training.front.model.order.*;
import com.fuyingedu.training.mapper.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class FrontOrderService {

    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private StudentMapper studentMapper;
    @Autowired
    private ClazzMapper clazzMapper;
    @Autowired
    private TeacherMapper teacherMapper;
    @Autowired
    private GroupMapper groupMapper;
    @Autowired
    private CampMapper campMapper;
    @Autowired
    private ScheduleMapper scheduleMapper;
    @Autowired
    private OrderAssistantMapper orderAssistantMapper;
    @Autowired
    private FuyingCourseFeign fuyingCourseFeign;
    @Autowired
    private FxyMapper fxyMapper;
    @Autowired
    private OrderFxyMapper orderFxyMapper;
    @Autowired
    private TaskRelationMapper taskRelationMapper;
    @Autowired
    private CheckAuthManager checkAuthManager;
    @Autowired
    private WordScheduleMapper wordScheduleMapper;
    @Autowired
    private UserMapper userMapper;

    public CommResp<List<AssistantItemResp>> listAssistantOrder(Long userId, Byte cartType, String cartNum) {
        Student student = studentMapper.selectOne(new QueryWrapper<Student>().select(
                Student.ID
        ).eq(Student.CARD_NUM, cartNum).eq(Student.CARD_TYPE, cartType));
        if (student == null) {
            return RespUtils.warning(4000, "该学员不存在");
        }
        Set<Long> orderIds = orderAssistantMapper.selectList(new QueryWrapper<OrderAssistant>().select(OrderAssistant.ORDER_ID)
                .eq(OrderAssistant.USER_ID, userId)).stream().map(OrderAssistant::getOrderId).collect(Collectors.toSet());
        List<Order> orderList = orderMapper.selectList(new QueryWrapper<Order>().select(
                        Order.ID,
                        Order.CAMP_ID,
                        Order.SCHEDULE_ID,
                        Order.TEACHER_ID,
                        Order.CLAZZ_ID,
                        Order.GROUP_ID
                ).eq(Order.STUDENT_ID, student.getId())
                .notIn(!orderIds.isEmpty(), Order.ID, orderIds)
                .in(Order.ORDER_STATUS, OrderStatus.NORMAL.getCode(), OrderStatus.MAY_REFUND.getCode()).orderByDesc(Order.ID));
        if (orderList.isEmpty()) {
            return RespUtils.success(Collections.emptyList());
        }
        List<Long> campIds = orderList.stream().map(Order::getCampId).distinct().toList();
        List<Long> scheduleIds = orderList.stream().map(Order::getScheduleId).filter(Objects::nonNull).distinct().toList();
        List<Long> clazzIds = orderList.stream().map(Order::getClazzId).filter(Objects::nonNull).distinct().toList();
        List<Long> groupIds = orderList.stream().map(Order::getGroupId).filter(Objects::nonNull).distinct().toList();
        Map<Long, Camp> campMap = campMapper.selectList(new QueryWrapper<Camp>().select(
                Camp.ID, Camp.CAMP_NAME, Camp.MAIN_MEDIA_URL
        ).in(Camp.ID, campIds)).stream().collect(Collectors.toMap(Camp::getId, Function.identity()));
        Map<Long, Schedule> scheduleMap = Collections.emptyMap();
        if (!scheduleIds.isEmpty()) {
            scheduleMap = scheduleMapper.selectList(new QueryWrapper<Schedule>().select(
                    Schedule.ID,
                    Schedule.SCHEDULE_NAME,
                    Schedule.START_TIME,
                    Schedule.END_TIME
            ).in(Schedule.ID, scheduleIds)).stream().collect(Collectors.toMap(Schedule::getId, Function.identity()));
        }
        Map<Long, Clazz> clazzMap = Collections.emptyMap();
        if (!clazzIds.isEmpty()) {
            clazzMap = clazzMapper.selectList(new QueryWrapper<Clazz>().select(
                    Clazz.ID,
                    Clazz.CLASS_NAME,
                    Clazz.ASSISTANT_ID
            ).in(Clazz.ID, clazzIds)).stream().collect(Collectors.toMap(Clazz::getId, Function.identity()));
        }
        Map<Long, Teacher> teacherMap = Collections.emptyMap();
        Set<Long> teacherIds = orderList.stream().map(Order::getTeacherId).filter(Objects::nonNull).collect(Collectors.toSet());
        teacherIds.addAll(clazzMap.values().stream().map(Clazz::getAssistantId).filter(Objects::nonNull).collect(Collectors.toSet()));
        if (!teacherIds.isEmpty()) {
            teacherMap = teacherMapper.selectList(new QueryWrapper<Teacher>().select(
                    Teacher.ID,
                    Teacher.REAL_NAME
            ).in(Teacher.ID, teacherIds)).stream().collect(Collectors.toMap(Teacher::getId, Function.identity()));
        }
        Map<Long, Group> groupMap = Collections.emptyMap();
        if (!groupIds.isEmpty()) {
            groupMap = groupMapper.selectList(new QueryWrapper<Group>().select(
                    Group.ID,
                    Group.GROUP_NAME
            ).in(Group.ID, groupIds)).stream().collect(Collectors.toMap(Group::getId, Function.identity()));
        }
        LocalDateTime now = scheduleMapper.queryNow();
        List<AssistantItemResp> respList = new ArrayList<>(orderList.size());
        Map<Long, List<OrderAssistant>> orderAssistantMap = orderAssistantMapper.selectList(new QueryWrapper<OrderAssistant>().select(
                        OrderAssistant.USER_ID, OrderAssistant.ORDER_ID
                ).in(OrderAssistant.ORDER_ID, orderList.stream().map(Order::getId).toList()))
                .stream().collect(Collectors.groupingBy(OrderAssistant::getOrderId));
        for (Order order : orderList) {
            AssistantItemResp resp = new AssistantItemResp();
            resp.setOrderId(order.getId());
            Camp camp = campMap.get(order.getCampId());
            resp.setCampName(camp.getCampName());
            resp.setMainMediaUrl(MediaConvertor.getMediaUrl(camp.getMainMediaUrl()));
            Schedule schedule = scheduleMap.get(order.getScheduleId());
            if (schedule != null) {
                resp.setScheduleName(schedule.getScheduleName());
                resp.setScheduleStatus(ScheduleStatus.convert(schedule.getStartTime(), schedule.getEndTime(), now).getCode());
            }
            if (order.getTeacherId() != null) {
                resp.setTeacherName(teacherMap.get(order.getTeacherId()).getRealName());
            }
            Clazz clazz = clazzMap.get(order.getClazzId());
            if (clazz != null) {
                resp.setClazzName(clazz.getClassName());
                resp.setAssistantName(teacherMap.get(clazz.getAssistantId()).getRealName());
            }
            Group group = groupMap.get(order.getGroupId());
            if (group != null) {
                resp.setGroupName(group.getGroupName());
            }
            resp.setAssistantNum(orderAssistantMap.getOrDefault(order.getId(), Collections.emptyList()).size());
            respList.add(resp);
        }
        return RespUtils.success(respList);
    }

    @Transactional(rollbackFor = Exception.class)
    public void saveAssistant(Long userId, AssistantSaveReq assistantSaveReq) {
        Set<Long> userIds = orderAssistantMapper.selectList(new QueryWrapper<OrderAssistant>().select(
                OrderAssistant.USER_ID
        ).eq(OrderAssistant.ORDER_ID, assistantSaveReq.getOrderId())).stream().map(OrderAssistant::getUserId).collect(Collectors.toSet());
        if (userIds.contains(userId)) {
            throw new WebBaseException(4000, "您已经绑定，不能重复绑定");
        }
        if (userIds.size() >= 2) {
            throw new WebBaseException(4000, "已有2位助手，不能再绑定");
        }
        OrderAssistant orderAssistant = new OrderAssistant();
        orderAssistant.setOrderId(assistantSaveReq.getOrderId());
        orderAssistant.setUserId(userId);
        orderAssistantMapper.insert(orderAssistant);
    }

    public CommResp<List<MyCampResp>> myCampList(String openId) {
        Fxy fxy = fxyMapper.selectOne(new QueryWrapper<Fxy>().select(
                Fxy.ID
        ).eq(Fxy.OPEN_ID, openId));
        List<Long> orderIds = orderFxyMapper.selectList(new QueryWrapper<OrderFxy>().select(
                OrderFxy.ORDER_ID
        ).eq(OrderFxy.FXY_ID, fxy.getId())).stream().map(OrderFxy::getOrderId).toList();
        if (orderIds.isEmpty()) {
            return RespUtils.success(Collections.emptyList());
        }
        List<Order> orderList = orderMapper.selectList(new QueryWrapper<Order>().select(
                Order.ID, Order.CAMP_ID, Order.SCHEDULE_ID, Order.STUDENT_ID, Order.ORDER_REMARK, Order.USER_ID
        ).in(Order.ORDER_STATUS, OrderStatus.NORMAL.getCode(), OrderStatus.MAY_REFUND.getCode()).in(Order.ID, orderIds).orderByDesc(Order.ID));
        return myCampList(orderList);
    }

    public CommResp<List<MyCampResp>> myCampList(Long userId) {
        List<Long> orderIds = orderAssistantMapper.selectList(new QueryWrapper<OrderAssistant>().select(
                OrderAssistant.ORDER_ID
        ).eq(OrderAssistant.USER_ID, userId)).stream().map(OrderAssistant::getOrderId).toList();
        List<Order> orderList = orderMapper.selectList(new QueryWrapper<Order>().select(
                        Order.ID, Order.CAMP_ID, Order.SCHEDULE_ID, Order.STUDENT_ID, Order.ORDER_REMARK, Order.USER_ID
                ).in(Order.ORDER_STATUS, OrderStatus.NORMAL.getCode(), OrderStatus.MAY_REFUND.getCode())
                .and(e -> e.eq(Order.USER_ID, userId).or(!orderIds.isEmpty()).in(!orderIds.isEmpty(), Order.ID, orderIds)).orderByDesc(Order.ID));
        if (orderList.isEmpty()) {
            return RespUtils.success(Collections.emptyList());
        }
        return myCampList(orderList);
    }

    public CommResp<List<MyCampResp>> myCampList(List<Order> orderList) {
        List<Long> campIds = orderList.stream().map(Order::getCampId).distinct().toList();
        List<Long> scheduleIds = orderList.stream().map(Order::getScheduleId).filter(Objects::nonNull).distinct().toList();
        Map<Long, Camp> campMap = campMapper.selectList(new QueryWrapper<Camp>().select(
                Camp.ID, Camp.CAMP_NAME, Camp.CAMP_TYPE, Camp.MAIN_MEDIA_URL
        ).in(Camp.ID, campIds)).stream().collect(Collectors.toMap(Camp::getId, Function.identity()));
        Map<Long, Schedule> scheduleMap = Collections.emptyMap();
        Map<Long, Long> wordMap = Collections.emptyMap();
        if (!scheduleIds.isEmpty()) {
            scheduleMap = scheduleMapper.selectList(new QueryWrapper<Schedule>().select(
                    Schedule.ID,
                    Schedule.SCHEDULE_NAME,
                    Schedule.START_TIME,
                    Schedule.END_TIME
            ).in(Schedule.ID, scheduleIds)).stream().collect(Collectors.toMap(Schedule::getId, Function.identity()));
            wordMap = wordScheduleMapper.selectList(new QueryWrapper<WordSchedule>().select(WordSchedule.SCHEDULE_ID, WordSchedule.WORD_ID).in(
                    WordSchedule.SCHEDULE_ID, scheduleIds
            )).stream().collect(Collectors.toMap(WordSchedule::getScheduleId, WordSchedule::getWordId));
        }
        Set<Long> userIds = orderList.stream().map(Order::getUserId).collect(Collectors.toSet());
        Map<Long, User> userMap = userMapper.selectList(new QueryWrapper<User>().select(
                User.ID, User.REAL_NAME
        ).in(User.ID, userIds)).stream().collect(Collectors.toMap(User::getId, Function.identity()));
        List<Long> studentIds = orderList.stream().map(Order::getStudentId).distinct().toList();
        Map<Long, Student> studentMap = Collections.emptyMap();
        if (!studentIds.isEmpty()) {
            studentMap = studentMapper.selectList(new QueryWrapper<Student>().select(
                    Student.ID,
                    Student.REAL_NAME
            ).in(Student.ID, studentIds)).stream().collect(Collectors.toMap(Student::getId, Function.identity()));
        }
        List<MyCampResp> respList = new ArrayList<>(orderList.size());
        LocalDateTime now = scheduleMapper.queryNow();
        for (Order order : orderList) {
            MyCampResp resp = new MyCampResp();
            resp.setOrderId(order.getId());
            resp.setUserId(order.getUserId());
            Camp camp = campMap.get(order.getCampId());
            if (camp == null) {
                continue;
            }
            resp.setCampType(camp.getCampType());
            resp.setCampName(camp.getCampName());
            resp.setMainMediaUrl(MediaConvertor.getMediaUrl(camp.getMainMediaUrl()));
            if (order.getScheduleId() != null) {
                Schedule schedule = scheduleMap.get(order.getScheduleId());
                if (schedule == null) {
                    continue;
                }
                resp.setScheduleName(schedule.getScheduleName());
                resp.setScheduleStatus(ScheduleStatus.convert(schedule.getStartTime(), schedule.getEndTime(), now).getCode());
                resp.setStartTime(schedule.getStartTime());
                resp.setEndTime(schedule.getEndTime());
                resp.setWordId(wordMap.get(order.getScheduleId()));
            }
            Student student = studentMap.get(order.getStudentId());
            if (student != null) {
                resp.setStudentName(student.getRealName());
                resp.setCartNum(StrUtils.codeCartNum(student.getCardNum()));
            }
            User user = userMap.get(order.getUserId());
            if (StringUtils.hasLength(order.getOrderRemark())) {
                resp.setOrderRemark(order.getOrderRemark());
            } else {
                resp.setOrderRemark(user.getRealName());
            }
            respList.add(resp);
        }
        return RespUtils.success(respList);
    }

    public CommResp<List<StudentRet>> studentList(Long orderId) {
        Order order = orderMapper.selectOne(new QueryWrapper<Order>().select(
                Order.USER_ID
        ).eq(Order.ID, orderId));
        if (order == null) {
            return RespUtils.warning(RespMetaEnum.PARAM_ERROR);
        }
        Set<Long> studentIds = orderMapper.selectList(new QueryWrapper<Order>().select(
                Order.STUDENT_ID
        ).eq(Order.USER_ID, order.getUserId())).stream().map(Order::getStudentId).filter(Objects::nonNull).collect(Collectors.toSet());
        List<StudentRet> respList = new ArrayList<>(studentIds.size());
        for (Long studentId : studentIds) {
            TraineeItem traineeItem = fuyingCourseFeign.getTrainee(studentId);
            StudentRet studentRet = new StudentRet();
            studentRet.setId(traineeItem.getId());
            studentRet.setStudentName(traineeItem.getName());
            studentRet.setCartNum(StrUtils.codeCartNum(traineeItem.getIdCard()));
            respList.add(studentRet);
        }
        return RespUtils.success(respList);
    }

    public void saveStudent(Long userId, SaveStudentReq saveStudentReq) {
        Order order = orderMapper.selectOne(new QueryWrapper<Order>().select(
                Order.ID,
                Order.STUDENT_ID
        ).eq(Order.ID, saveStudentReq.getOrderId()));
        if (order == null || order.getStudentId() != null) {
            throw new WebBaseException(4000, "已绑定学员");
        }
        TraineeItem traineeItem = fuyingCourseFeign.getTrainee(saveStudentReq.getId());
        if (traineeItem == null) {
            throw new WebBaseException(4000, "学员不存在");
        }
        Student student = toStudent(traineeItem);
        Student oldStudent = studentMapper.selectOne(new QueryWrapper<Student>().select(
                Student.ID
        ).eq(Student.CARD_NUM, student.getCardNum()).eq(Student.CARD_TYPE, CardType.ID_CARD.getCode()));
        TransactionStatus transaction = TransactionUtils.open();
        try {
            if (oldStudent != null) {
                student.setId(oldStudent.getId());
            } else {
                studentMapper.insert(student);
            }
            orderMapper.update(new UpdateWrapper<Order>().set(Order.STUDENT_ID, student.getId()).eq(Order.ID, order.getId()));
            TransactionUtils.commit(transaction);
        } catch (Exception e) {
            TransactionUtils.rollback(transaction);
            throw new WebBaseException(RespMetaEnum.TIMEOUT);
        }
    }

    public void validateOrder(Long userId, Order order) {
        if (order == null) {
            throw new WebBaseException(500, "服务状态异常");
        }
        if (!userId.equals(order.getUserId())) {
            Set<Long> userIds = orderAssistantMapper.selectList(new QueryWrapper<OrderAssistant>().select(
                            OrderAssistant.ID, OrderAssistant.USER_ID)
                    .eq(OrderAssistant.ORDER_ID, order.getId())
            ).stream().map(OrderAssistant::getUserId).collect(Collectors.toSet());
            if (!userIds.contains(userId)) {
                throw new WebBaseException(RespMetaEnum.NO_AUTH);
            }
        }
    }

    public Set<Long> getOrderTaskIds(Order order) {
        List<Long> teacherIds = new ArrayList<>(2);
        teacherIds.add(-1L);
        if (order.getClazzId() != null) {
            teacherIds.add(order.getTeacherId());
        }
        List<Long> clazzIds = new ArrayList<>(2);
        clazzIds.add(-1L);
        if (order.getClazzId() != null) {
            clazzIds.add(order.getClazzId());
        }
        return taskRelationMapper.selectList(new QueryWrapper<TaskRelation>().select(
                                TaskRelation.TASK_ID
                        ).eq(TaskRelation.SCHEDULE_ID, order.getScheduleId()).in(TaskRelation.TEACHER_ID, teacherIds)
                        .in(TaskRelation.CLAZZ_ID, clazzIds)
        ).stream().map(TaskRelation::getTaskId).collect(Collectors.toSet());
    }

    private Student toStudent(TraineeItem traineeItem) {
        Student student = new Student();
        student.setRealName(traineeItem.getName());
        student.setNewOuterId(traineeItem.getId());
        student.setCardType(CardType.ID_CARD.getCode());
        student.setCardNum(traineeItem.getIdCard());
        return student;

    }

    public void update(Long userId, UpdateReq updateReq) {
        checkAuthManager.checkOrder(userId, updateReq.getOrderId());
        if (StringUtils.hasLength(updateReq.getNickname())) {
            orderMapper.update(new UpdateWrapper<Order>()
                    .set(StringUtils.hasLength(updateReq.getNickname()), Order.ORDER_REMARK, updateReq.getNickname())
                    .eq(Order.ID, updateReq.getOrderId())
            );
        }
    }
}
