package com.fuyingedu.training.front.model.user;

import lombok.*;

import java.util.List;

@ToString
@Getter
@Setter
public class AccountInfoResp {

    /**
     * 当前登录用户ID
     */
    private Long currUserId;

    /**
     * 扶小鹰学员昵称
     */
    private String nickname;

    /**
     * 扶小鹰open_id
     */
    private String openId;

    /**
     * 扶小鹰账号
     */
    private String accountInfo;

    /**
     * 扶小鹰头像
     */
    private String iconUrl;

    /**
     * 1 - 主账号 2 - 辅助账号
     */
    private Byte currAccountType;

    /**
     * 家长列表
     */
    private List<UserInfo> userInfoList;

    @Getter
    @Setter
    @AllArgsConstructor
    @NoArgsConstructor
    public static class UserInfo {

        /**
         * 用户ID
         */
        private Long userId;

        /**
         * 用户头像
         */
        private String userIcon;

        /**
         * 姓名
         */
        private String realName;

        /**
         * 手机号
         */
        private Long phoneNum;

        /**
         * 1 - 主账号 2 - 辅助账号
         */
        private Byte accountType;
    }
}
