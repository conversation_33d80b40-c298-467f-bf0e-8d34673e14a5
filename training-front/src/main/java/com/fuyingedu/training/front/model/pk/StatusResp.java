package com.fuyingedu.training.front.model.pk;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class StatusResp {

    private Long pkId;

    /**
     * 状态：1-未开始过PK 2-进行中 3-已结束
     */
    private Byte status;

    /**
     * 开始时间数
     */
    private Long startTime;

    /**
     * 结束时间数
     */
    private Long stopTime;

    /**
     * 协管用户ID列表
     */
    private List<Long> userIds;
}
