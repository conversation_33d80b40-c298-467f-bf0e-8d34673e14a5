package com.fuyingedu.training.front.model.word;

import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.List;

@Getter
@Setter
public class HawkResp {

    /**
     * 个人积分数
     */
    private Integer rewardNum;

    /**
     * 本期积分数
     */
    private Integer orderRewardNum;

    private List<Hawk> hawkList;

    @Getter
    @Setter
    public static class Hawk {

        private Long hawkId;

        /**
         * 鹰的名字
         */
        private String hawkName;
        /**
         * Word鹰介绍
         */
        private String hawkContent;

        /**
         * Word鹰的图片地址
         */
        private String hawkUrl;

        /**
         * 剪影图
         */
        private String coverUrl;

        /**
         * 未解锁图
         */
        private String lockUrl;

        /**
         * 已解锁图
         */
        private String unlockUrl;

        /**
         * 1 已解锁 2 未解锁-可解锁 3 未解锁-不能解锁
         */
        private Byte lockedStatus;

        /**
         * 解锁时间
         */
        private LocalDateTime unlockTime;
        /**
         * 解锁需要的积分
         */
        private Integer rewardNum;
    }
}
