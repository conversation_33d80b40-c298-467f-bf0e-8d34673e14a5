package com.fuyingedu.training.front.service;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.fuyingedu.training.common.enums.LoginType;
import com.fuyingedu.training.common.exception.WebBaseException;
import com.fuyingedu.training.common.model.CommResp;
import com.fuyingedu.training.common.util.*;
import com.fuyingedu.training.entity.User;
import com.fuyingedu.training.front.feign.FuyingUserFeign;
import com.fuyingedu.training.front.manager.FxyWordManager;
import com.fuyingedu.training.front.model.feign.UnionItem;
import com.fuyingedu.training.front.model.login.CodeDto;
import com.fuyingedu.training.front.model.login.CodeReq;
import com.fuyingedu.training.front.model.login.LoginReq;
import com.fuyingedu.training.front.model.login.LoginResp;
import com.fuyingedu.training.mapper.UserMapper;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class FrontLoginService {

    @Autowired
    private WxMaService wxMaService;
    @Autowired
    private UserMapper userMapper;
    @Autowired
    private RedisTemplate<String, String> redisTemplate;
    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private FuyingUserFeign fuyingUserFeign;

    @Value("${spring.profiles.active}")
    private String profiles;
    @Autowired
    private FxyWordManager fxyWordManager;

    public CommResp<LoginResp> login(LoginReq loginReq) {
        User user = userMapper.selectOne(new QueryWrapper<User>().select(
                User.ID, User.NICK_NAME, User.REAL_NAME, User.USER_ICON, User.UNION_ID, User.OPEN_ID
        ).eq(User.PHONE_NUM, loginReq.getPhoneNum()));
        if (user == null) {
            throw new WebBaseException(4000, "该手机号未开通训练营服务，无法登录");
        }
        WxMaJscode2SessionResult sessionInfo = null;
        if (loginReq.getSessionCode() != null) {
            String sessionKey = redisTemplate.opsForValue().get(RedisKey.PREFIX + loginReq.getSessionCode());
            if (StringUtils.hasLength(sessionKey)) {
                sessionInfo = JsonUtils.parseJsonToObj(sessionKey, WxMaJscode2SessionResult.class);
                redisTemplate.delete(RedisKey.PREFIX + loginReq.getSessionCode());
            }
        }
        if (user.getOpenId() == null && sessionInfo != null) {
            userMapper.update(new UpdateWrapper<User>().set(User.OPEN_ID, sessionInfo.getOpenid()).eq(User.ID, user.getId()));
        }
        if (user.getUnionId() == null) {
            try {
                UnionItem unionItem = fuyingUserFeign.getUnionId(user.getId());
                if (unionItem != null) {
                    userMapper.update(new UpdateWrapper<User>().set(User.UNION_ID, unionItem.getThirdAuthId()).eq(User.ID, user.getId()));
                } else if (sessionInfo != null) {
                    userMapper.update(new UpdateWrapper<User>().set(User.UNION_ID, sessionInfo.getUnionid()).eq(User.ID, user.getId()));
                }
            } catch (Exception e) {
                log.info("获取用户信息失败[{}]", e.getMessage());
            }
        }
        if (LoginType.PHONE.getType().equals(loginReq.getType())) {
            String code = redisTemplate.opsForValue().get(String.format(RedisKey.PHONE, loginReq.getPhoneNum()));
            if (loginReq.getCode() == null || !loginReq.getCode().equals(code)) {
                throw new WebBaseException(500, "验证码错误");
            }
        }
        LoginResp resp = new LoginResp();
        setLoginResp(user, resp);
        return RespUtils.success(resp);
    }

    private void setLoginResp(User user, LoginResp resp) {
        resp.setId(user.getId());
        resp.setRealName(user.getRealName());
        resp.setUserIcon(user.getUserIcon());
        String token = UUID.randomUUID().toString().replace("-", "");
        resp.setToken(token);
        Map<String, Object> params = Map.of("userId", user.getId(), "loginTime", System.currentTimeMillis());
        redisTemplate.opsForValue().set(String.format(RedisKey.FRONT_LOGIN, token), JsonUtils.formatObjToJson(params), 30, TimeUnit.DAYS);
        fxyWordManager.login(user.getId(), token);
    }


    public void sendCode(CodeReq codeReq) {
        User user = userMapper.selectOne(new QueryWrapper<User>().select(User.ID, User.UNION_ID, User.OPEN_ID)
                .eq(User.PHONE_NUM, codeReq.getPhoneNum()));
        if (user == null) {
            throw new WebBaseException(4000, "该手机号未开通训练营服务，无法登录");
        }
        String codeKey = String.format(RedisKey.PHONE, codeReq.getPhoneNum());
        String key = String.format(RedisKey.PHONE_NUM, codeReq.getPhoneNum());
        LocalDateTime end = LocalDate.now().plusDays(1).atStartOfDay();
        long millis = DateUtils.toMillis(end) - System.currentTimeMillis();
        Long count = Objects.requireNonNull(redisTemplate.opsForValue().increment(key));
        if (Long.valueOf(1L).equals(count)) {
            redisTemplate.expire(key, millis, TimeUnit.MILLISECONDS);
        }
        if (count > 20L) {
            throw new WebBaseException(500, "今日发送短信次数过多，请联系管理员");
        }
        int code = ThreadLocalRandom.current().nextInt(100000, 999999);
        CodeDto codeDto = new CodeDto();
        codeDto.setTemplateCode("DXYZM0002");
        codeDto.setTemplateParam("{\"code\":\"" + code + "\",\"minutes\":\"10\"}");
        codeDto.setServiceCode("TRAINING_CAMP");
        codeDto.setTargetUser("86" + codeReq.getPhoneNum());
        codeDto.setNo(UUID.randomUUID().toString().replace("-", ""));
        if ("prod".equals(profiles) && !"18969908983".equals(codeReq.getPhoneNum().toString())) {
            AsyncUtils.execute(() -> {
                String s = restTemplate.postForObject("http://sms.internal.fuyingy.com/message/real/send", codeDto, String.class);
                log.info("短信发送结果:" + s);
            }, "短信发送");
        } else {
            code = 123456;
        }
        redisTemplate.opsForValue().set(codeKey, String.valueOf(code), 10, TimeUnit.MINUTES);
    }

    public CommResp<LoginResp> getSessionKey(String code) throws WxErrorException {
        WxMaJscode2SessionResult sessionInfo = wxMaService.getUserService().getSessionInfo(code);
        redisTemplate.opsForValue().set(RedisKey.PREFIX + sessionInfo.getSessionKey(), JsonUtils.formatObjToJson(sessionInfo), 1, TimeUnit.HOURS);
        LoginResp loginResp = new LoginResp();
        loginResp.setSessionKey(sessionInfo.getSessionKey());
        User user = userMapper.selectOne(new QueryWrapper<User>().select(
                User.ID,
                User.NICK_NAME,
                User.REAL_NAME,
                User.USER_ICON, User.OPEN_ID, User.UNION_ID
        ).eq(User.UNION_ID, sessionInfo.getUnionid()));
        if (user == null) {
            user = userMapper.selectOne(new QueryWrapper<User>().select(
                    User.ID,
                    User.NICK_NAME,
                    User.REAL_NAME,
                    User.USER_ICON, User.OPEN_ID, User.UNION_ID
            ).eq(User.OPEN_ID, sessionInfo.getOpenid()).last("limit 1"));
        }
        if (user != null) {
            setLoginResp(user, loginResp);
        }
        return RespUtils.success(loginResp);
    }
}
