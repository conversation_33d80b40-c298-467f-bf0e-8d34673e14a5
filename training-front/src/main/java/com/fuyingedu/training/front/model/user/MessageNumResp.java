package com.fuyingedu.training.front.model.user;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
public class MessageNumResp {
    /**
     * 直播消息 1 需要 2 不需要
     */
    private Byte liveMessageStatus;

    /**
     * 作业和打卡消息 1 需要 2 不需要
     */
    private Byte taskMessageStatus;

    /**
     * 点评消息 1 需要 2 不需要
     */
    private Byte remarkMessageStatus;
}
