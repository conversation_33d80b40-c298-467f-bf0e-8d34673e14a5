package com.fuyingedu.training.front.model.message;

import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
public class RemarkResp {

    private Long id;
    /**
     * 0-未读 1-已读
     */
    private Integer readStatus;
    /**
     * 用户头像
     */
    private String userIcon;

    /**
     * 昵称
     */
    private String nickName;

    private LocalDateTime createdTime;

    /**
     * 作业记录id
     */
    private Long recordId;

    /**
     * 作业id
     */
    private Long taskId;
    /**
     * 作业标题
     */
    private String taskTitle;

    /**
     * 1 - 辅导老师 2 - 大志愿者 3-陪跑志愿者
     */
    private Byte remarkType;

    /**
     * 任务类型 1-签到 2-打卡 3-作业5-接口打卡
     */
    private Byte taskType;
}
