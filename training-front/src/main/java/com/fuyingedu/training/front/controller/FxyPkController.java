package com.fuyingedu.training.front.controller;

import com.fuyingedu.training.common.model.CommResp;
import com.fuyingedu.training.front.model.pk.ItemResp;
import com.fuyingedu.training.front.model.pk.StatusResp;
import com.fuyingedu.training.front.model.stats.GroupResp;
import com.fuyingedu.training.front.model.stats.RewardResp;
import com.fuyingedu.training.front.service.FrontPkService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 扶小鹰-PK排行榜
 */
@RestController
@RequestMapping("fxy/pk")
public class FxyPkController {

    @Autowired
    private FrontPkService frontPkService;

    /**
     * 判断PK开启状态
     */
    @GetMapping("status")
    public CommResp<StatusResp> status(Long orderId) {
        return frontPkService.status(orderId);
    }

    /**
     * PK历史列表
     */
    @GetMapping("list")
    public CommResp<List<ItemResp>> list(Long orderId) {
        return frontPkService.list(orderId);
    }

    /**
     * 个人榜
     */
    @GetMapping("reward")
    public CommResp<List<RewardResp>> reward(@RequestParam Long orderId,
                                             @RequestParam(required = false) Long pkId) {
        return frontPkService.reward(orderId, pkId);
    }

    /**
     * 小组榜
     */
    @GetMapping("group")
    public CommResp<List<GroupResp>> group(@RequestParam Long orderId,
                                           @RequestParam(required = false) Long pkId) {
        return frontPkService.group(orderId, pkId);
    }
}
