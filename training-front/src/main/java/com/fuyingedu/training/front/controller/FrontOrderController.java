package com.fuyingedu.training.front.controller;

import com.fuyingedu.training.common.annotation.Login;
import com.fuyingedu.training.common.model.CommResp;
import com.fuyingedu.training.common.util.RespUtils;
import com.fuyingedu.training.dto.fuying.FxyItemRet;
import com.fuyingedu.training.dto.fuying.StudentRet;
import com.fuyingedu.training.front.model.order.*;
import com.fuyingedu.training.front.service.FrontOrderService;
import com.fuyingedu.training.front.service.FxyService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 服务单相关接口
 */
@RestController
@RequestMapping("front/order")
public class FrontOrderController {

    @Autowired
    private FrontOrderService frontOrderService;
    @Autowired
    private FxyService fxyService;

    /**
     * 根据证件号查询该学员下的服务单
     * @param cartType 证件类型 1-身份证 2-护照 3-港澳通行证
     */
    @GetMapping("assistant/list")
    public CommResp<List<AssistantItemResp>> listAssistantOrder(@Login Long userId, @RequestParam("cartType") Byte cartType,
                                                                @RequestParam("cartNum") String cartNum) {
        return frontOrderService.listAssistantOrder(userId, cartType, cartNum);
    }

    /**
     * 保存协管关系
     * @param userId 前端不需要传
     */
    @PostMapping("assistant/save")
    public CommResp<?> saveAssistant(@Login Long userId, @RequestBody AssistantSaveReq assistantSaveReq) {
        frontOrderService.saveAssistant(userId, assistantSaveReq);
        return RespUtils.success();
    }

    /**
     * 我的服务单(训练营)列表
     */
    @GetMapping("my/camp/list")
    public CommResp<List<MyCampResp>> myCampList(@Login Long userId) {
        return frontOrderService.myCampList(userId);
    }

    /**
     * 根据服务单查询学员列表
     */
    @GetMapping("student/list")
    public CommResp<List<StudentRet>> studentList(@RequestParam("orderId") Long orderId) {
        return frontOrderService.studentList(orderId);
    }

    /**
     * 绑定学员
     */
    @PostMapping("student/save")
    public CommResp<?> saveStudent(@Login Long userId, @RequestBody @Valid SaveStudentReq saveStudentReq) {
        frontOrderService.saveStudent(userId, saveStudentReq);
        return RespUtils.success();
    }

    /**
     * 查询扶小鹰列表
     * @param userId 不传
     */
    @GetMapping("fxy/list")
    public CommResp<List<FxyItemRet>> fxyList(@Login Long userId, FxyListReq fxyListReq) {
        return fxyService.fxyList(userId, fxyListReq);
    }

    /**
     * 绑定扶小鹰
     */
    @PostMapping("fxy/save")
    public CommResp<?> saveFxy(@Login Long userId, @RequestBody @Valid FxySaveReq fxySaveReq) {
        fxyService.saveFxy(userId, fxySaveReq);
        return RespUtils.success();
    }

    /**
     * 更新服务单的备注名（昵称）
     */
    @PostMapping("update")
    public CommResp<?> update(@Login Long userId, @RequestBody @Valid UpdateReq updateReq) {
        frontOrderService.update(userId, updateReq);
        return RespUtils.success();
    }

    /**
     * 解除扶小鹰的绑定
     */
    @PostMapping("fxy/unbind")
    public CommResp<?> unbindFxy(@Login Long userId, @RequestParam("orderId") Long orderId) {
        return fxyService.unbindFxy(userId, orderId);
    }
}
