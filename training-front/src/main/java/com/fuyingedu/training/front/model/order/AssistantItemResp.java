package com.fuyingedu.training.front.model.order;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@ToString
@Getter
@Setter
public class AssistantItemResp {

    /**
     * 服务单ID
     */
    private Long orderId;

    /**
     * 训练营
     */
    private String campName;
    /**
     * 训练营图片
     */
    private String mainMediaUrl;

    /**
     * 排期
     */
    private String scheduleName;
    /**
     * 1-未开始 2-进行中 3-已结束
     */
    private Byte scheduleStatus;

    /**
     * 助教老师
     */
    private String assistantName;

    /**
     * 辅导老师
     */
    private String teacherName;

    /**
     * 班级
     */
    private String clazzName;

    /**
     * 分组
     */
    private String groupName;

    /**
     * 协管用户数
     */
    private Integer assistantNum;
}
