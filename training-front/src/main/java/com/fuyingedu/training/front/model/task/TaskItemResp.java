package com.fuyingedu.training.front.model.task;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDate;
import java.time.LocalDateTime;

@ToString
@Getter
@Setter
public class TaskItemResp {

    private Long id;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 任务开始时间
     */
    private LocalDateTime startDate;
    /**
     * 任务截止时间
     */
    private LocalDateTime endDate;

    /**
     * 任务日期
     */
    private LocalDate taskDate;

    /**
     * 1 已开始 2 当天 3 未开始
     */
    private Byte dateType;
    /**
     * 任务积分
     */
    private Integer taskReward;

    /**
     * 扶小鹰小太阳
     */
    private Integer fxyReward;
    /**
     * Word鹰积分值
     */
    private Integer wordReward;

    /**
     * 任务类型 1-签到 2-打卡 3-作业 4-直播 5-接口打卡
     */
    private Byte taskType;

    /**
     * 今日完成状态 1-未完成 2-已完成 (直播 1-未开始 2-已开始 3-已结束（可回看） 4-直播结束（不可回看））
     */
    private Byte taskStatus;

    /**
     * 距离开始的秒数
     */
    private Long distance;
    /**
     * 直播间信息
     */
    private String liveInfo;

    /**
     * 直播房间号
     */
    private String liveRoom;

    /**
     * 直播密码
     */
    private String livePassword;

    /**
     * 回放链接
     */
    private String repeatUrl;

    /**
     * 直播方式 1-公司直播 2-导师直播
     * 字典Key：LIVE_TYPE
     */
    private Byte liveType;

    /**
     * 直播端口 1-腾讯会议 2-保利威 3-百家云
     * 字典Key：LIVE_PORT
     */
    private Byte livePort;
}
