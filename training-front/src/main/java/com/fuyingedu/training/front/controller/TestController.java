package com.fuyingedu.training.front.controller;

import com.fuyingedu.training.common.model.CommResp;
import com.fuyingedu.training.common.util.RespUtils;
import com.fuyingedu.training.front.feign.FuyingCourseFeign;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("front")
public class TestController {

    @Autowired
    private FuyingCourseFeign fuyingCourseFeign;
    @GetMapping("test")
    public CommResp<?> test() {
        return RespUtils.success(fuyingCourseFeign.listCamp());
    }
}
