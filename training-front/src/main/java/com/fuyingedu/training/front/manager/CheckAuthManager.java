package com.fuyingedu.training.front.manager;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fuyingedu.training.common.enums.RespMetaEnum;
import com.fuyingedu.training.common.enums.Status;
import com.fuyingedu.training.common.enums.TeacherType;
import com.fuyingedu.training.common.exception.WebBaseException;
import com.fuyingedu.training.entity.*;
import com.fuyingedu.training.mapper.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Component
public class CheckAuthManager {

    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private OrderAssistantMapper orderAssistantMapper;
    @Autowired
    private FxyMapper fxyMapper;
    @Autowired
    private OrderFxyMapper orderFxyMapper;

    @Autowired
    private TeacherMapper teacherMapper;
    @Autowired
    private ScheduleTeacherMapper scheduleTeacherMapper;

    public void checkTeacher(Long userId, Long scheduleId) {
        Teacher teacher = teacherMapper.selectOne(new QueryWrapper<Teacher>().select(
                Teacher.ID
        ).eq(Teacher.USER_ID, userId).eq(Teacher.TEACHER_STATUS, Status.NORMAL.getCode()));
        checkTeacher(teacher, scheduleId);
    }

    public void checkTeacher(Teacher teacher, Long scheduleId) {
        if (teacher == null) {
            throw new WebBaseException(4000, "你不是辅导老师或者状态已失效，没有该权限");
        }
        ScheduleTeacher scheduleTeacher = scheduleTeacherMapper.selectOne(new QueryWrapper<ScheduleTeacher>().select(
                        ScheduleTeacher.ID
                ).eq(ScheduleTeacher.SCHEDULE_ID, scheduleId).eq(ScheduleTeacher.TEACHER_TYPE, TeacherType.TEACHER.getCode())
                .eq(ScheduleTeacher.TEACHER_ID, teacher.getId()));
        if (scheduleTeacher == null) {
            throw new WebBaseException(4000, "你不是该训练营的辅导老师，没有该权限");
        }
    }

    public Order checkOrder(String openId, Long orderId) {
        Fxy fxy = fxyMapper.selectOne(new QueryWrapper<Fxy>().select(
                Fxy.ID
        ).eq(Fxy.OPEN_ID, openId));
        List<Long> orderIds = orderFxyMapper.selectList(new QueryWrapper<OrderFxy>().select(
                OrderFxy.ORDER_ID
        ).eq(OrderFxy.FXY_ID, fxy.getId())).stream().map(OrderFxy::getOrderId).toList();
        if (orderIds.isEmpty() || !orderIds.contains(orderId)) {
            throw new WebBaseException(RespMetaEnum.PARAM_ERROR);
        }
        return orderMapper.selectOne(new QueryWrapper<Order>().select(
                Order.CLAZZ_ID, Order.ID
        ).eq(Order.ID, orderId));
    }


    public void checkOrder(Long loginUserId, Long orderId) {
        Order order = orderMapper.selectOne(new QueryWrapper<Order>().select(
                Order.USER_ID, Order.ID
        ).eq(Order.ID, orderId));
        order.setId(orderId);
        checkOrder(loginUserId, order.getId(), order.getUserId());
    }

    public void checkOrder(Long loginUserId, Long orderId, Long orderUserId) {
        if (orderUserId.equals(loginUserId)) {
            return;
        }
        Set<Long> userIds = orderAssistantMapper.selectList(new QueryWrapper<OrderAssistant>().select(
                OrderAssistant.USER_ID
        ).eq(OrderAssistant.ORDER_ID, orderId)).stream().map(OrderAssistant::getUserId).collect(Collectors.toSet());
        if (!userIds.contains(loginUserId)) {
            throw new WebBaseException(RespMetaEnum.NO_AUTH);
        }
    }
}
