package com.fuyingedu.training.front.model.clazz;

import com.fuyingedu.training.front.model.media.MediaResp;
import com.fuyingedu.training.front.model.task.remark.RemarkResp;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.List;

@ToString
@Getter
@Setter
public class HomeworkItemResp {

    private Long id;
    /**
     * 姓名
     */
    private String realName;
    /**
     * 用户头像
     */
    private String userIcon;
    /**
     * 0-不是自己 1-自己
     */
    private Byte myself;

    private Long groupId;

    private String groupName;

    /**
     * 打卡时间
     */
    private LocalDateTime createdTime;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 作业的文本信息
     */
    private String content;

    /**
     * 点赞数
     */
    private Integer likeNum;
    /**
     * 点赞状态 0-未点赞 1-已点赞
     */
    private Byte like;

    /**
     * 1-普通作业 2-优秀作业
     */
    private Byte homeworkType;

    /**
     * 任务类型 1-签到 2-打卡 3-作业 5-接口打卡
     */
    private Byte taskType;

    /**
     * 作业的素材
     */
    private List<MediaResp> mediaUrlList;


    /**
     * 点评列表
     */
    private List<RemarkResp> remarkList;

}
