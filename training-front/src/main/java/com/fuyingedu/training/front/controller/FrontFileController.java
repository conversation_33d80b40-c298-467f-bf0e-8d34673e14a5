package com.fuyingedu.training.front.controller;

import com.aliyun.oss.OSS;
import com.aliyun.oss.model.MatchMode;
import com.aliyun.oss.model.PolicyConditions;
import com.fuyingedu.training.common.annotation.Login;
import com.fuyingedu.training.common.model.CommResp;
import com.fuyingedu.training.common.util.DateUtils;
import com.fuyingedu.training.common.util.RespUtils;
import com.fuyingedu.training.front.model.media.MediaConvertor;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Base64;

/**
 * 文件上传管理
 */
@RestController
@RequestMapping("front/file")
@Slf4j
public class FrontFileController {

    @Autowired
    private OSS ossClient;
    @Value("${oss.persistent-key}")
    private String persistentKey;

    /**
     * 获取oss上传策略
     * @param suffix 文件后缀名 例如 jpg、png
     * @param type 文件类型 image/png
     */
    @GetMapping("policy")
    public CommResp<PolicyResp> getOssPolicy(@Login Long userId,
                                             @RequestParam("suffix") String suffix,
                                             @RequestParam("type") String type) {
        String dir = DateUtils.format(LocalDate.now(), DateUtils.DATE_NONE_FORMATTER) + "/" + userId;
        String fullPath = dir + "/" + System.currentTimeMillis() + "." + suffix;
        PolicyConditions conditions = new PolicyConditions();
        conditions.addConditionItem(PolicyConditions.COND_CONTENT_LENGTH_RANGE, 0, 1048576000);
        conditions.addConditionItem(MatchMode.Exact, PolicyConditions.COND_KEY, fullPath);
        LocalDateTime expiredTime = LocalDateTime.now().plusMinutes(10);
        String policy = ossClient.generatePostPolicy(DateUtils.asDate(expiredTime), conditions);
        String signature = ossClient.calculatePostSignature(policy);
        PolicyResp policyResp = new PolicyResp();
        policyResp.setAccessKey(persistentKey);
        policyResp.setPolicy(new String(Base64.getEncoder().encode(policy.getBytes())));
        policyResp.setSignature(signature);
        policyResp.setKey(fullPath);
        policyResp.setType(MediaConvertor.getMediaType(type));
        return RespUtils.success(policyResp);
    }

    @Getter
    @Setter
    public static class PolicyResp {
        /**
         * OSS的AccessKey
         */
        private String accessKey;

        /**
         * OSS上传策略
         */
        private String policy;

        /**
         * OSS上传策略的签名
         */
        private String signature;

        /**
         * OSS上传的Key
         */
        private String key;

        /**
         * 素材类型 0-未知 1-文本 2-图片 3-视频 4-音频
         */
        private Byte type;
    }
}
