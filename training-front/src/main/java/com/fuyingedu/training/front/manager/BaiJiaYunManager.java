package com.fuyingedu.training.front.manager;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fasterxml.jackson.databind.JsonNode;
import com.fuyingedu.training.common.exception.WebBaseException;
import com.fuyingedu.training.common.util.JsonUtils;
import com.fuyingedu.training.entity.*;
import com.fuyingedu.training.mapper.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.util.DigestUtils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 百家云相关接口管理
 */
@Slf4j
@Component
public class BaiJiaYunManager {

    private static final String BASE_URL = "https://e49679728.at.baijiacloud.com/openapi/";

    private static final String PARTNER_KEY = "eMhtQbAwiPATOs6xUo1iZ2BA4EnkEbJyGn6LViyLXOfEbzy2/PbxvJTwLCsdyPU55FPEgLVrvPRD+UYc1DcDYZO5mK/v";

    private static final Integer PARTNER_ID = 49679728;

    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private ClazzRoomMapper clazzRoomMapper;
    @Autowired
    private ScheduleMapper scheduleMapper;
    @Autowired
    private ClazzMapper clazzMapper;
    @Autowired
    private ScheduleRoomMapper scheduleRoomMapper;
    @Autowired
    private UserMapper userMapper;

    public JsonNode getRoomList() {
        TreeMap<String, Object> params = new TreeMap<>();
        params.put("page", 1);
        params.put("limit", 1000);
        params.put("product_type", 1);
        String s = restTemplate.postForObject(BASE_URL + "room/list", httpEntity(params(params)), String.class);
        return checkResult(s);
    }

    public void createGroup() {
        List<ClazzRoom> clazzRooms = clazzRoomMapper.selectList(new QueryWrapper<ClazzRoom>().select(
                ClazzRoom.ID, ClazzRoom.CLAZZ_ID, ClazzRoom.SCHEDULE_ID
        ).isNull(ClazzRoom.ROOM_ID));
        Map<Long, List<ClazzRoom>> group = clazzRooms.stream().collect(Collectors.groupingBy(ClazzRoom::getScheduleId));
        Map<Long, Long> roomIds = new HashMap<>();
        for (Map.Entry<Long, List<ClazzRoom>> entry : group.entrySet()) {
            Schedule schedule = scheduleMapper.selectOne(new QueryWrapper<Schedule>().select(
                    Schedule.ID, Schedule.LIVE_ROOM
            ).eq(Schedule.ID, entry.getKey()));
            if (schedule.getLiveRoom() == null || schedule.getLiveRoom() <= 0) {
                log.info("排期[{}]没有直播房间号", schedule.getId());
                continue;
            }
            Map<Long, ClazzRoom> clazzRoomMap = entry.getValue().stream().collect(Collectors.toMap(ClazzRoom::getClazzId, clazzRoom -> clazzRoom));
            List<Clazz> clazzList = clazzMapper.selectList(new QueryWrapper<Clazz>().select(
                            Clazz.ID, Clazz.CLASS_NAME
                    ).in(Clazz.ID, clazzRoomMap.keySet()));
            List<String> groupNames = clazzList.stream().map(Clazz::getClassName).collect(Collectors.toList());
            JsonNode jsonNode = createGroup(schedule.getLiveRoom(), groupNames);
            Map<Integer, JsonNode> adminMap = new HashMap<>();
            for (JsonNode node : jsonNode.get("admin")) {
                JsonNode groupId = node.get("group_id");
                if (groupId != null) {
                    adminMap.put(groupId.asInt(), node);
                }
            }
            Map<Integer, JsonNode> teacherMap = new HashMap<>();
            for (JsonNode node : jsonNode.get("teacher")) {
                JsonNode groupId = node.get("group_id");
                if (groupId != null) {
                    teacherMap.put(groupId.asInt(), node);
                }
            }
            JsonNode studentList = jsonNode.get("student");
            for (int i = 0; i < studentList.size(); i++) {
                JsonNode node = studentList.get(i);
                Clazz clazz = clazzList.get(i);
                JsonNode groupId = node.get("group_id");
                if (groupId == null) {
                    continue;
                }
                JsonNode teacherNode = teacherMap.get(groupId.asInt());
                JsonNode adminNode = adminMap.get(groupId.asInt());
                ClazzRoom clazzRoom = clazzRoomMap.get(clazz.getId());
                clazzRoom.setRoomId(node.get("room_id").asLong());
                clazzRoom.setGroupId(groupId.asInt());
                clazzRoom.setRoomName(clazz.getClassName());
                clazzRoom.setTeacherCode(teacherNode.get("code").asText());
                clazzRoom.setAdminCode(adminNode.get("code").asText());
                clazzRoom.setStudentCode(node.get("code").asText());
            }
            roomIds.put(schedule.getLiveRoom(), entry.getKey());
        }
        if (!clazzRooms.isEmpty()) {
            clazzRoomMapper.updateById(clazzRooms);
        }
        List<ScheduleRoom> updateList = new ArrayList<>();
        for (Map.Entry<Long, Long> entry : roomIds.entrySet()) {
            ScheduleRoom scheduleRoom = scheduleRoomMapper.selectOne(new QueryWrapper<ScheduleRoom>().select(
                    ScheduleRoom.ID
            ).eq(ScheduleRoom.ROOM_ID, entry.getKey()));
            scheduleRoom.setRoomNum(clazzRoomMapper.selectCount(new QueryWrapper<ClazzRoom>()
                    .eq(ClazzRoom.SCHEDULE_ID, entry.getValue())).intValue());
            updateList.add(scheduleRoom);
        }
        if (!updateList.isEmpty()) {
            scheduleRoomMapper.updateById(updateList);
        }
        deleteGroup(group.keySet());
    }

    public void deleteGroup(Set<Long> scheduleIds) {
        if (scheduleIds == null || scheduleIds.isEmpty()) {
            return;
        }
        List<Schedule> scheduleList = scheduleMapper.selectList(new QueryWrapper<Schedule>().select(
                Schedule.ID, Schedule.LIVE_ROOM
        ).in(Schedule.ID, scheduleIds).isNotNull(Schedule.LIVE_ROOM));
        for (Schedule schedule : scheduleList) {
            Long scheduleId = schedule.getId();
            Set<String> studentCodes = clazzRoomMapper.selectList(new QueryWrapper<ClazzRoom>().select(
                    ClazzRoom.STUDENT_CODE
            ).eq(ClazzRoom.SCHEDULE_ID, scheduleId).isNotNull(ClazzRoom.STUDENT_CODE))
                    .stream().map(ClazzRoom::getStudentCode).collect(Collectors.toSet());
            JsonNode groupList = getGroupList(schedule.getLiveRoom());
            JsonNode jsonNode = groupList.get("student");
            for (JsonNode student : jsonNode) {
                String studentCode = student.get("code").asText();
                if (studentCodes.contains(studentCode)) {
                    continue;
                }
                String url = student.get("url").asText();
                // 解析url中的参数
                String[] params = url.split("\\?");
                if (params.length > 1) {
                    String[] paramPairs = params[1].split("&");
                    for (int i = 0; i < paramPairs.length; i++) {
                        String[] pairs = paramPairs[i].split("=");
                        if (pairs.length == 2 && pairs[0].equals("room_id")) {
                            deleteRoom(Long.valueOf(pairs[1]));
                        }
                    }
                }
            }
        }
    }

    public JsonNode createGroup(Long roomId, List<String> groupNames) {
        TreeMap<String, Object> params = new TreeMap<>();
        params.put("room_id", roomId);
        params.put("individual_group_name", JsonUtils.formatObjToJson(groupNames));
        params.put("number", groupNames.size());
        String s = restTemplate.postForObject(BASE_URL + "room/createGroupLiveCodes", httpEntity(params(params)), String.class);
        return checkResult(s);
    }

    public JsonNode updateGroupName(Long roomId, Integer groupId, String groupName) {
        TreeMap<String, Object> params = new TreeMap<>();
        params.put("room_id", roomId);
        params.put("group_id", groupId);
        params.put("group_name", groupName);
        String s = restTemplate.postForObject(BASE_URL + "room/updateGroupName", httpEntity(params(params)), String.class);
        return checkResult(s);
    }

    public JsonNode getGroupList(Long roomId) {
        TreeMap<String, Object> params = new TreeMap<>();
        params.put("room_id", roomId);
        String s = restTemplate.postForObject(BASE_URL + "room/getGroupLiveCodes", httpEntity(params(params)), String.class);
        return checkResult(s);
    }

    public JsonNode playbackList(Long roomId) {
        TreeMap<String, Object> params = new TreeMap<>();
        params.put("room_id", roomId);
        params.put("product_type", 1);
        params.put("page", 1);
        params.put("page_size", 1000);
        String s = restTemplate.postForObject(BASE_URL + "playback/getList", httpEntity(params(params)), String.class);
        return checkResult(s);
    }

    public JsonNode questionList(Long roomId, Long quizId) {
        TreeMap<String, Object> params = new TreeMap<>();
        params.put("room_id", roomId);
        params.put("quiz_id", quizId);
        String s = restTemplate.postForObject(BASE_URL + "room_data/getQuizUserAnswer", httpEntity(params(params)), String.class);
        return checkResult(s);
    }

    public JsonNode checkinInfo(Long roomId) {
        TreeMap<String, Object> params = new TreeMap<>();
        params.put("room_id", roomId);
        String s = restTemplate.postForObject(BASE_URL + "room_data/getUserCheckinInfo", httpEntity(params(params)), String.class);
        return checkResult(s);
    }

    public JsonNode deleteRoom(Long roomId) {
        TreeMap<String, Object> params = new TreeMap<>();
        params.put("room_id", roomId);
        String s = restTemplate.postForObject(BASE_URL + "room/delete", httpEntity(params(params)), String.class);
        return checkResult(s);
    }

    public JsonNode roomQuiz(Long roomId) {
        TreeMap<String, Object> params = new TreeMap<>();
        params.put("room_id", roomId);
        String s = restTemplate.postForObject(BASE_URL + "room_data/getRoomQuiz", httpEntity(params(params)), String.class);
        return checkResult(s);
    }

    public JsonNode roomAnswer(Long roomId, LocalDate localDate) {
        TreeMap<String, Object> params = new TreeMap<>();
        params.put("room_id", roomId);
        params.put("date", localDate.toString());
        String s = restTemplate.postForObject(BASE_URL + "room_data/exportAnswerStat", httpEntity(params(params)), String.class);
        return checkResult(s);
    }

    public String setCallback(String callbackUrl) {
        TreeMap<String, Object> params = new TreeMap<>();
        params.put("url", callbackUrl);
        return restTemplate.postForObject(BASE_URL + "live_account/setClassCallbackUrl", httpEntity(params(params)), String.class);
    }

    public boolean isBaiJiaYun(Schedule schedule) {
        return Byte.valueOf((byte) 3).equals(schedule.getLivePort()) && Byte.valueOf((byte) 1).equals(schedule.getLiveType());
    }

    public String getLiveInfo(Schedule schedule, Order order) {
        User user;
        Long roomId = null;
        if (isBaiJiaYun(schedule)) {
            user = userMapper.selectOne(new QueryWrapper<User>().select(
                    User.ID, User.REAL_NAME, User.USER_ICON
            ).eq(User.ID, order.getUserId()));
            if (order.getClazzId() != null) {
                ClazzRoom clazzRoom = clazzRoomMapper.selectOne(new QueryWrapper<ClazzRoom>().select(
                        ClazzRoom.ROOM_ID
                ).eq(ClazzRoom.CLAZZ_ID, order.getClazzId()));
                roomId = schedule.getLiveRoom();
                if (clazzRoom != null) {
                    roomId = clazzRoom.getRoomId();
                }
            } else {
                roomId = schedule.getLiveRoom();
            }
        } else {
            user = null;
        }
        if (user != null) {
            String realName = StringUtils.hasLength(order.getOrderRemark()) ? order.getOrderRemark() : user.getRealName();
            return getRoomUrl(roomId, order.getId(), 0, realName, user.getUserIcon());
        }
        return null;
    }

    public String getRoomUrl(Long roomId, Long userNumber, int role, String userName, String userAvatar) {
        String params = String.format("room_id=%d&user_avatar=%s&user_name=%s&user_number=%d&user_role=%d&partner_key=%s",
                roomId, userAvatar, userName, userNumber, role, PARTNER_KEY);
        String url = String.format("room_id=%d&user_avatar=%s&user_name=%s&user_number=%d&user_role=%d",
                roomId, URLEncoder.encode(userAvatar, StandardCharsets.UTF_8),
                URLEncoder.encode(userName, StandardCharsets.UTF_8),
                userNumber, role);
        String sign = DigestUtils.md5DigestAsHex(params.getBytes(StandardCharsets.UTF_8));
        return "https://e49679728.at.baijiacloud.com/web/room/enter?" + url + "&sign=" + sign;
    }

    private HttpEntity<Object> httpEntity(MultiValueMap<String, Object> params) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);
        return new HttpEntity<>(params, headers);
    }

    private MultiValueMap<String, Object> params(TreeMap<String, Object> params) {
        params.put("partner_id", PARTNER_ID);
        params.put("timestamp", System.currentTimeMillis() / 1000);
        MultiValueMap<String, Object> map = new LinkedMultiValueMap<>();
        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, Object> entry : params.entrySet()) {
            sb.append(entry.getKey()).append("=").append(entry.getValue()).append("&");
            map.add(entry.getKey(), entry.getValue());
        }
        sb.append("partner_key=").append(PARTNER_KEY);
        String sign = DigestUtils.md5DigestAsHex(sb.toString().getBytes(StandardCharsets.UTF_8));
        map.add("sign", sign);
        return map;
    }

    private JsonNode checkResult(String s) {
        JsonNode jsonNode = JsonUtils.parseJsonToJsonNode(s);
        if (jsonNode.get("code").asInt() != 0) {
            log.info("百家云接口报错[{}]", s);
            throw new WebBaseException(4000, "百家云接口报错");
        }
        return jsonNode.get("data");
    }
}
