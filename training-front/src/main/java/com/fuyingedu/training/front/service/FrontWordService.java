package com.fuyingedu.training.front.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fasterxml.jackson.databind.JsonNode;
import com.fuyingedu.training.common.enums.TaskType;
import com.fuyingedu.training.common.model.CommResp;
import com.fuyingedu.training.common.util.DateUtils;
import com.fuyingedu.training.common.util.JsonUtils;
import com.fuyingedu.training.common.util.RespUtils;
import com.fuyingedu.training.entity.*;
import com.fuyingedu.training.front.manager.BaiJiaYunManager;
import com.fuyingedu.training.front.model.live.LiveConvertor;
import com.fuyingedu.training.front.model.word.TaskItem;
import com.fuyingedu.training.front.model.word.TaskResp;
import com.fuyingedu.training.front.model.word.WeekResp;
import com.fuyingedu.training.mapper.*;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class FrontWordService {

    @Autowired
    private ScheduleMapper scheduleMapper;
    @Autowired
    private WordScheduleMapper wordScheduleMapper;
    @Autowired
    private WordDelayMapper wordDelayMapper;
    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private LiveMapper liveMapper;
    @Autowired
    private TaskMapper taskMapper;
    @Autowired
    private TaskSubmitRecordMapper taskSubmitRecordMapper;
    @Autowired
    private BaiJiaYunManager baiJiaYunManager;
    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private LiveSignRecordMapper liveSignRecordMapper;
    @Value("${fxy.pad-url}")
    private String padUrl;

    public CommResp<TaskResp> todayTask(Long orderId) {
        Order order = orderMapper.selectOne(new QueryWrapper<Order>().select(
                Order.ID, Order.SCHEDULE_ID, Order.CLAZZ_ID, Order.USER_ID, Order.ORDER_REMARK
        ).eq(Order.ID, orderId));
        Schedule schedule = scheduleMapper.selectOne(new QueryWrapper<Schedule>().select(
                Schedule.ID, Schedule.LIVE_ROOM, Schedule.LIVE_TIME, Schedule.LIVE_PORT, Schedule.LIVE_TYPE
        ).eq(Schedule.ID, order.getScheduleId()));
        List<TaskItem> itemList = taskList(order.getScheduleId());
        int totalDays = itemList.size();
        LocalDate now = LocalDate.now();
        itemList = itemList.stream().filter(t -> t.getRealDate().equals(now)).toList();
        if (itemList.isEmpty()) {
            return RespUtils.success(null);
        }
        TaskItem taskItem = itemList.getFirst();
        Task task = taskMapper.selectOne(new QueryWrapper<Task>().select(
                Task.ID, Task.TASK_REWARD, Task.TASK_NAME, Task.WORD_REWARD
            ).eq(Task.SCHEDULE_ID, order.getScheduleId()).eq(Task.TASK_TYPE, TaskType.WORD.getCode()).last("limit 1"));
        TaskResp resp = new TaskResp();
        resp.setWordId(taskItem.getWordId());
        resp.setTaskType(taskItem.getTaskType());
        resp.setDays(taskItem.getDays());
        resp.setRealDate(taskItem.getRealDate());
        resp.setTotalDays(totalDays);
        resp.setTaskReward(task.getTaskReward());
        resp.setWordReward(task.getWordReward());
        resp.setTaskName(task.getTaskName());
        if (Byte.valueOf((byte) 3).equals(taskItem.getTaskType())) {
            Live live = liveMapper.selectOne(new QueryWrapper<Live>().select(
                Live.ID, Live.START_TIME, Live.LIVE_NAME, Live.LIVE_INFO, Live.LIVE_DURATION,
                Live.LIVE_ROOM, Live.LIVE_PASSWORD, Live.REPEAT_URL, Live.REPEAT_START_TIME, Live.REPEAT_END_TIME
            ).eq(Live.SCHEDULE_ID, order.getScheduleId()).ge(Live.START_TIME, now.atStartOfDay())
            .lt(Live.START_TIME, now.plusDays(1).atStartOfDay()));
            setLiveInfo(resp, live);
            resp.setLiveType(schedule.getLiveType());
            resp.setLivePort(schedule.getLivePort());
        } else {
            TaskSubmitRecord record = taskSubmitRecordMapper.selectOne(new QueryWrapper<TaskSubmitRecord>().select(TaskSubmitRecord.ID)
                    .eq(TaskSubmitRecord.TASK_ID, task.getId()).eq(TaskSubmitRecord.ORDER_ID, orderId).eq(TaskSubmitRecord.SUBMIT_DATE, LocalDate.now()));
            if (record == null) {
                resp.setTaskStatus((byte) 2);
            } else {
                resp.setTaskStatus((byte) 3);
            }
        }
        resp.setTaskState((byte) 2);
        String liveInfo = baiJiaYunManager.getLiveInfo(schedule, order);
        if (liveInfo != null) {
            resp.setLiveInfo(liveInfo);
        }
        return RespUtils.success(resp);
    }

    public void setLiveInfo(TaskResp resp, Live live) {
        resp.setLiveName(live.getLiveName());
        resp.setLiveInfo(live.getLiveInfo());
        resp.setLiveRoom(live.getLiveRoom());
        resp.setLivePassword(live.getLivePassword());
        resp.setRepeatUrl(live.getRepeatUrl());
        resp.setStartTime(live.getStartTime());
        resp.setDistance(DateUtils.seconds(LocalDateTime.now(), live.getStartTime()));
        resp.setLiveType(live.getLiveType());
        resp.setTaskStatus(LiveConvertor.getLiveStatus(live));
    }

    public CommResp<List<WeekResp>> getTaskList(Long orderId, HttpServletRequest request) {
        Order order = orderMapper.selectOne(new QueryWrapper<Order>().select(
                Order.ID, Order.SCHEDULE_ID, Order.CLAZZ_ID, Order.USER_ID, Order.ORDER_REMARK
        ).eq(Order.ID, orderId));
        Schedule schedule = scheduleMapper.selectOne(new QueryWrapper<Schedule>().select(
                Schedule.ID, Schedule.LIVE_ROOM, Schedule.LIVE_TIME, Schedule.LIVE_PORT,
                Schedule.LIVE_TYPE, Schedule.START_TIME
        ).eq(Schedule.ID, order.getScheduleId()));
        List<TaskItem> itemList = taskList(schedule);
        if (itemList.isEmpty()) {
            return RespUtils.success(Collections.emptyList());
        }
        Task task = taskMapper.selectOne(new QueryWrapper<Task>().select(
                Task.ID, Task.WORD_REWARD, Task.FXY_REWARD, Task.TASK_REWARD
            ).eq(Task.SCHEDULE_ID, order.getScheduleId()).eq(Task.TASK_TYPE, TaskType.WORD.getCode()).last("limit 1"));
        Map<LocalDate, TaskSubmitRecord> recordMap = taskSubmitRecordMapper.selectList(new QueryWrapper<TaskSubmitRecord>().select(
                TaskSubmitRecord.ID, TaskSubmitRecord.SUBMIT_DATE, TaskSubmitRecord.CREATED_TIME
                ).eq(TaskSubmitRecord.TASK_ID, task.getId()).eq(TaskSubmitRecord.ORDER_ID, orderId))
                .stream().collect(Collectors.toMap(TaskSubmitRecord::getSubmitDate, record -> record));
        Map<LocalDate, Live> liveMap = liveMapper.selectList(new QueryWrapper<Live>().select(
                Live.ID, Live.START_TIME, Live.LIVE_NAME, Live.LIVE_INFO, Live.LIVE_DURATION,
                Live.LIVE_ROOM, Live.LIVE_PASSWORD, Live.REPEAT_URL, Live.REPEAT_START_TIME, Live.REPEAT_END_TIME
        ).eq(Live.SCHEDULE_ID, order.getScheduleId()))
                .stream().collect(Collectors.toMap(v -> v.getStartTime().toLocalDate(), live -> live));
        Set<Long> liveIds = liveSignRecordMapper.selectList(new QueryWrapper<LiveSignRecord>().select(LiveSignRecord.LIVE_ID).eq(LiveSignRecord.ORDER_ID, orderId))
                .stream().map(LiveSignRecord::getLiveId).collect(Collectors.toSet());
        String liveInfo = baiJiaYunManager.getLiveInfo(schedule, order);

        TaskItem first = itemList.getFirst();
        HttpHeaders headers = new HttpHeaders();
        headers.set("accessToken",  request.getAttribute("token").toString());
        HttpEntity<Object> httpEntity = new HttpEntity<>(headers);
        String url = String.format("/traApi/pad/campStudy/week/List?trainingCampPlanId=%s&trainingOrderId=%s",
                first.getWordId(), orderId);
        ResponseEntity<String> remoteResp = restTemplate.exchange(padUrl + url, HttpMethod.GET, httpEntity, String.class);
        JsonNode jsonNode = JsonUtils.parseJsonToJsonNode(remoteResp.getBody());
        if (jsonNode.get("code").asInt() != 200) {
            log.info("获取远程数据失败[{}]", jsonNode.get("message").asText());
            return RespUtils.warning(jsonNode.get("code").asInt(), jsonNode.get("message").asText());
        }
        Map<Integer, WeekResp> weekMap = new HashMap<>();
        Map<Integer, JsonNode> dayMap = new HashMap<>();
        for (JsonNode node : jsonNode.get("data")) {
            WeekResp weekResp = new WeekResp();
            Integer week = node.get("week").asInt();
            weekResp.setWeek(week);
            weekResp.setWordCnt(node.get("wordCnt").asInt());
            weekMap.put(week, weekResp);
            JsonNode days = node.get("days");
            if (days != null) {
                for (JsonNode day : days) {
                    dayMap.put(day.get("day").asInt(), day);
                }
            }
        }
        int weeks = 1;
        LocalDate now = LocalDate.now();
        WeekResp weekResp = null;
        List<WeekResp> respList = new ArrayList<>();
        for (TaskItem item : itemList) {
            if (item.getDays() % 7 == 1 && item.getDays() != 1) {
                weekResp = null;
            }
            if (weekResp == null) {
                weekResp = weekMap.get(weeks);
                if (weekResp == null) {
                    weekResp = new WeekResp();
                    weekResp.setWeek(weeks);
                    weekResp.setWordCnt(0);
                }
                weekResp.setDayList(new ArrayList<>());
                respList.add(weekResp);
                weeks++;
            }
            List<TaskResp> taskRespList = weekResp.getDayList();
            TaskSubmitRecord record = recordMap.get(item.getRealDate());
            Live live = liveMap.get(item.getRealDate());
            TaskResp resp = new TaskResp();
            resp.setWordId(item.getWordId());
            resp.setTaskType(item.getTaskType());
            resp.setDays(item.getDays());
            resp.setRealDate(item.getRealDate());
            resp.setTotalDays(itemList.size());
            resp.setTaskReward(task.getTaskReward());
            resp.setWordReward(task.getWordReward());
            if (live != null) {
                setLiveInfo(resp, live);
                if (liveInfo != null) {
                    resp.setLiveInfo(liveInfo);
                }
                resp.setLiveType(schedule.getLiveType());
                resp.setLivePort(schedule.getLivePort());
                resp.setFinishStep(liveIds.contains(live.getId()) ? 1 : 0);
            } else {
                if (item.getRealDate().isBefore(now)) {
                    if (record != null) {
                        setTaskDoneStatus(resp, record);
                    } else {
                        resp.setTaskStatus((byte) 4);
                    }
                } else if (item.getRealDate().equals(now)) {
                    if (record != null) {
                        setTaskDoneStatus(resp, record);
                    } else {
                        resp.setTaskStatus((byte) 2);
                    }
                } else {
                    resp.setTaskStatus((byte) 1);
                }
            }
            if (item.getRealDate().isBefore(now)) {
                resp.setTaskState((byte) 1);
            } else if (item.getRealDate().equals(now)) {
                resp.setTaskState((byte) 2);
            } else {
                resp.setTaskState((byte) 3);
            }
            JsonNode day = dayMap.get(item.getDays());
            if (day != null) {
                resp.setFinishStep(day.get("finishStep").asInt());
            }
            taskRespList.add(resp);
        }
        return RespUtils.success(respList);
    }

    private void setTaskDoneStatus(TaskResp resp, TaskSubmitRecord record) {
        if (record.getSubmitDate().isEqual(record.getCreatedTime().toLocalDate())) {
            resp.setTaskStatus((byte) 3);
        } else {
            resp.setTaskStatus((byte) 5);
        }
    }

    public List<TaskItem> taskList(Long scheduleId) {
        Schedule schedule = scheduleMapper.selectOne(new QueryWrapper<Schedule>().select(
                Schedule.ID, Schedule.START_TIME
        ).eq(Schedule.ID, scheduleId));
        return taskList(schedule);
    }

    public List<TaskItem> taskList(Schedule schedule) {
        WordSchedule scheduleWord = wordScheduleMapper.selectOne(new QueryWrapper<WordSchedule>().select(
                WordSchedule.WORD_DAYS, WordSchedule.LIVE_DAYS, WordSchedule.WORD_ID
        ).eq(WordSchedule.SCHEDULE_ID, schedule.getId()));
        Map<Integer, Integer> delayMap = wordDelayMapper.selectList(new QueryWrapper<WordDelay>().select(
                WordDelay.REAL_DAYS, WordDelay.DELAY_DAYS
        ).eq(WordDelay.SCHEDULE_ID, schedule.getId())).stream().collect(Collectors.groupingBy(
                WordDelay::getRealDays, Collectors.summingInt(WordDelay::getDelayDays)
        ));
        LocalDate startDate = schedule.getStartTime().toLocalDate();
        List<TaskItem> itemList = new ArrayList<>(scheduleWord.getWordDays());
        List<Integer> liveDays = JsonUtils.parseJsonToList(scheduleWord.getLiveDays(), Integer.class);
        for (int i = 0, realDay = 0; i <= scheduleWord.getWordDays(); i++, realDay++) {
            TaskItem item = new TaskItem();
            item.setWordId(scheduleWord.getWordId());
            item.setStartDate(startDate);
            int delayDay = delayMap.getOrDefault(i, 0);
            realDay += delayDay;
            item.setRealDate(startDate.plusDays(realDay));
            item.setDays(i);
            if (i == 0 || liveDays.contains(i)) {
                item.setTaskType((byte) 3);
            } else {
                item.setTaskType((byte) 1);
            }
            itemList.add(item);
        }
        return itemList;
    }
}
