package com.fuyingedu.training.front.manager;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaSubscribeMessage;
import cn.binarywang.wx.miniapp.bean.WxMaSubscribeMessage.MsgData;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.fuyingedu.training.common.enums.*;
import com.fuyingedu.training.common.util.DateUtils;
import com.fuyingedu.training.entity.*;
import com.fuyingedu.training.mapper.*;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Component
public class WxMaManager {

    @Autowired
    private WxMaService wxMaService;
    @Autowired
    private TaskSubmitRecordMapper taskSubmitRecordMapper;
    @Autowired
    private ClazzMapper clazzMapper;
    @Autowired
    private ScheduleMapper scheduleMapper;
    @Autowired
    private CampMapper campMapper;
    @Autowired
    private TaskMapper taskMapper;
    @Autowired
    private UserMapper userMapper;
    @Autowired
    private TaskRecordRemarkMapper taskRecordRemarkMapper;
    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private TaskRelationMapper taskRelationMapper;
    @Autowired
    private TeacherMapper teacherMapper;

    public void remarkMessage(Long recordId, Long remarkUserId) {
        TaskSubmitRecord record = taskSubmitRecordMapper.selectOne(new QueryWrapper<TaskSubmitRecord>().select(
                TaskSubmitRecord.USER_ID, TaskSubmitRecord.TASK_ID, TaskSubmitRecord.CLAZZ_ID, TaskSubmitRecord.TASK_TYPE,
                TaskSubmitRecord.CREATED_TIME, TaskSubmitRecord.ORDER_ID
        ).eq(TaskSubmitRecord.ID, recordId));
        if (record == null || record.getUserId() == null) {
            log.error("[{}]提交记录异常", record);
            return;
        }
        if (!TaskType.HOMEWORK.getCode().equals(record.getTaskType()) && !TaskType.PUNCH.getCode().equals(record.getTaskType())) {
            return;
        }
        User user = userMapper.selectOne(new QueryWrapper<User>().select(
                User.ID, User.OPEN_ID, User.REMARK_MESSAGE_NUM
        ).eq(User.ID, record.getUserId()));
        if (user.getOpenId() == null || user.getRemarkMessageNum() <= 0) {
            log.error("[{}-{}-{}]无法发送消息", record.getUserId(), user.getOpenId(), user.getRemarkMessageNum());
            return;
        }
        List<TaskRecordRemark> recordRemarkList = taskRecordRemarkMapper.selectList(new QueryWrapper<TaskRecordRemark>().select(TaskRecordRemark.ID)
                .eq(TaskRecordRemark.RECORD_ID, recordId).last("limit 2"));
        if (recordRemarkList.size() > 1) {
            log.info("[{}]不是第一次备注", recordId);
            return;
        }
        Clazz clazz = clazzMapper.selectOne(new QueryWrapper<Clazz>().select(
                Clazz.CLASS_NAME, Clazz.TEACHER_ID, Clazz.SCHEDULE_ID
        ).eq(Clazz.ID, record.getClazzId()));
        String clazzName = getClazzName(clazz);
        Task task = taskMapper.selectOne(new QueryWrapper<Task>().select(
                Task.TASK_NAME, Task.ID, Task.START_DATE
        ).eq(Task.ID, record.getTaskId()));
        String title = task.getTaskName();
        if (TaskType.PUNCH.getCode().equals(record.getTaskType())) {
            title += "第" + DateUtils.days(task.getStartDate(), LocalDate.now()) + "天作业";
        }
        User teacher = userMapper.selectOne(new QueryWrapper<User>().select(
                User.REAL_NAME, User.ID
        ).eq(User.ID, remarkUserId));
        String type = getTaskType(record.getTaskType());
        String page = String.format("/pagesIndex/signInDetail?orderId=%d&taskId=%d&type=3&status=2&updateId=%d",
                record.getOrderId(), task.getId(), task.getId());
        sendTaskRemarkMessage(user.getId(), user.getOpenId(), page, clazzName, type, title,
                teacher.getRealName(), DateUtils.format(LocalDateTime.now()));
    }


    public void homeworkMessage(Long scheduleId, ScheduleTeacher teacher, Task task, List<Clazz> clazzList) {
        log.info("[{}-{}]发送作业消息", scheduleId, task.getId());
        if (!TaskType.HOMEWORK.getCode().equals(task.getTaskType())) {
            return;
        }
        if (TaskClazzType.ALL.getCode().equals(task.getClazzType())) {
            if (TeacherType.ASSISTANT.getCode().equals(teacher.getTeacherType())) {
                clazzList = clazzMapper.selectList(new QueryWrapper<Clazz>().select(
                        Clazz.ID, Clazz.CLASS_NAME
                ).eq(Clazz.SCHEDULE_ID, scheduleId).eq(Clazz.ASSISTANT_ID, teacher.getTeacherId()));
            } else {
                clazzList = clazzMapper.selectList(new QueryWrapper<Clazz>().select(
                        Clazz.ID, Clazz.CLASS_NAME
                ).eq(Clazz.SCHEDULE_ID, scheduleId).eq(Clazz.TEACHER_ID, teacher.getTeacherId()));
            }
        }
        taskMessage(scheduleId, task, clazzList);
    }

    public void homeworkMessage(Long scheduleId, Task task) {
        log.info("[{}-{}]发送作业消息", scheduleId, task.getId());
        if (!TaskType.HOMEWORK.getCode().equals(task.getTaskType())) {
            return;
        }
        taskMessage(scheduleId, task);
    }

    public void punchMessage(Task task) {
        Long scheduleId = task.getScheduleId();
        if (TaskLevel.SCHEDULE.getCode().equals(task.getTaskLevel())) {
            taskMessage(scheduleId, task);
        } else {
            List<TaskRelation> relationList = taskRelationMapper.selectList(new QueryWrapper<TaskRelation>().select(
                    TaskRelation.CLAZZ_ID, TaskRelation.TEACHER_ID, TaskRelation.ASSISTANT_ID
                    ).eq(TaskRelation.TASK_ID, task.getId()));
            if (relationList.isEmpty()) {
                return;
            }
            List<Clazz> clazzList = Collections.emptyList();
            if (TaskLevel.CLAZZ.getCode().equals(task.getTaskLevel())) {
                List<Long> clazzIds = relationList.stream().map(TaskRelation::getClazzId).distinct().toList();
                clazzList = clazzMapper.selectList(new QueryWrapper<Clazz>().select(
                        Clazz.ID, Clazz.CLASS_NAME
                ).in(Clazz.ID, clazzIds));
            } else {
                clazzList = clazzMapper.selectList(new QueryWrapper<Clazz>().select(
                        Clazz.ID, Clazz.CLASS_NAME
                ).in(Clazz.TEACHER_ID, relationList.stream().map(TaskRelation::getTeacherId).toList()));
            }
            taskMessage(scheduleId, task, clazzList);
        }
    }

    public void taskMessage(Long scheduleId, Task task) {
        List<Order> orderList = orderMapper.selectList(new QueryWrapper<Order>().select(
                        Order.ID, Order.CLAZZ_ID, Order.USER_ID).eq(Order.SCHEDULE_ID, scheduleId)
                .in(Order.ORDER_STATUS, OrderStatus.NORMAL.getCode(), OrderStatus.MAY_REFUND.getCode()).isNotNull(Order.CLAZZ_ID));
        if (orderList.isEmpty()) {
            return;
        }
        Set<Long> clazzIds = orderList.stream().map(Order::getClazzId).collect(Collectors.toSet());
        Map<Long, Clazz> clazzMap = clazzMapper.selectList(new QueryWrapper<Clazz>().select(
                        Clazz.ID, Clazz.CLASS_NAME, Clazz.TEACHER_ID, Clazz.SCHEDULE_ID
                ).in(Clazz.ID, clazzIds))
                .stream().collect(Collectors.toMap(Clazz::getId, Function.identity()));
        taskMessage(scheduleId, task, orderList, clazzMap);
    }

    public void taskMessage(Long scheduleId, Task task, List<Clazz> clazzList) {
        if (clazzList.isEmpty()) {
            return;
        }
        Map<Long, Clazz> clazzMap = clazzList.stream().collect(Collectors.toMap(Clazz::getId, Function.identity()));
        List<Order> orderList = orderMapper.selectList(new QueryWrapper<Order>().select(
                        Order.ID, Order.CLAZZ_ID, Order.USER_ID).in(Order.CLAZZ_ID, clazzMap.keySet())
                .in(Order.ORDER_STATUS, OrderStatus.NORMAL.getCode(), OrderStatus.MAY_REFUND.getCode()));
        if (orderList.isEmpty()) {
            return;
        }
        taskMessage(scheduleId, task, orderList, clazzMap);
    }

    public void taskMessage(Long scheduleId, Task task, List<Order> orderList, Map<Long, Clazz> clazzMap) {
        Set<Long> userIds = orderList.stream().map(Order::getUserId).collect(Collectors.toSet());
        Map<Long, User> userList = userMapper.selectList(new QueryWrapper<User>().select(
                User.ID, User.OPEN_ID, User.TASK_MESSAGE_NUM
        ).in(User.ID, userIds)).stream().collect(Collectors.toMap(User::getId, Function.identity()));
        String title = task.getTaskName();
        if (TaskType.PUNCH.getCode().equals(task.getTaskType())) {
            title += "第" + DateUtils.days(task.getStartDate(), LocalDate.now()) + "天作业";
        }
        Schedule schedule = scheduleMapper.selectOne(new QueryWrapper<Schedule>().select(
                Schedule.CAMP_ID
        ).eq(Schedule.ID, scheduleId));
        Camp camp = campMapper.selectOne(new QueryWrapper<Camp>().select(
                Camp.CAMP_NAME
        ).eq(Camp.ID, schedule.getCampId()));
        String type = getTaskType(task.getTaskType());
        String endTime;
        if (TaskType.HOMEWORK.getCode().equals(task.getTaskType())) {
            endTime = DateUtils.format(task.getEndDate().atTime(LocalTime.MAX));
        } else {
            endTime = DateUtils.format(task.getEndTime().atDate(LocalDate.now()));
        }
        for (Order order : orderList) {
            User user = userList.get(order.getUserId());
            if (user.getOpenId() == null || user.getTaskMessageNum() <= 0) {
                log.error("任务[{}-{}-{}]无法发送消息", user.getId(), user.getOpenId(), user.getTaskMessageNum());
                continue;
            }
            Clazz clazz = clazzMap.get(order.getClazzId());
            String clazzName = String.format("%s%s", camp.getCampName(), clazz.getClassName());
            sendTaskMessage(order.getId(), user.getId(), user.getOpenId(), clazzName, type, title, endTime);
        }
    }

    /**
     * 直播消息
     */
    public void liveMessage(Live live) {
        List<Order> orderList = orderMapper.selectList(new QueryWrapper<Order>().select(
                        Order.ID, Order.TEACHER_ID, Order.USER_ID)
                        .eq(Order.SCHEDULE_ID, live.getScheduleId()).isNotNull(Order.TEACHER_ID)
                .in(Order.ORDER_STATUS, OrderStatus.NORMAL.getCode(), OrderStatus.MAY_REFUND.getCode()));
        if (orderList.isEmpty()) {
            return;
        }
        Set<Long> userIds = orderList.stream().map(Order::getUserId).collect(Collectors.toSet());
        Set<Long> teacherIds = orderList.stream().map(Order::getTeacherId).collect(Collectors.toSet());
        Map<Long, User> userMap = userMapper.selectList(new QueryWrapper<User>().select(
                User.ID, User.OPEN_ID, User.LIVE_MESSAGE_NUM
        ).in(User.ID, userIds)).stream().collect(Collectors.toMap(User::getId, Function.identity()));
        Map<Long, String> teacherMap = teacherMapper.selectList(new QueryWrapper<Teacher>().select(
                Teacher.ID, Teacher.REAL_NAME
        ).in(Teacher.ID, teacherIds)).stream().collect(Collectors.toMap(Teacher::getId, Teacher::getRealName));
        for (Order order : orderList) {
            User user = userMap.get(order.getUserId());
            if (user.getOpenId() == null || user.getLiveMessageNum() <= 0) {
                log.error("直播[{}-{}-{}]无法发送消息", user.getId(), user.getOpenId(), user.getLiveMessageNum());
                continue;
            }
            sendLiveMessage(order.getId(), user.getId(), user.getOpenId(), live.getLiveName(), DateUtils.format(live.getStartTime()),
                    DateUtils.format(live.getStartTime().plusMinutes(live.getLiveDuration())), teacherMap.get(order.getTeacherId()));
        }
    }

    private String getClazzName(Clazz clazz) {
        Schedule schedule = scheduleMapper.selectOne(new QueryWrapper<Schedule>().select(
                Schedule.CAMP_ID
        ).eq(Schedule.ID, clazz.getScheduleId()));
        Camp camp = campMapper.selectOne(new QueryWrapper<Camp>().select(
                Camp.CAMP_NAME
        ).eq(Camp.ID, schedule.getCampId()));
        return String.format("%s%s", camp.getCampName(), clazz.getClassName());
    }

    private String getTaskType(Byte taskType) {
        String type = "单次作业";
        if (TaskType.PUNCH.getCode().equals(taskType)) {
            type = "打卡作业";
        }
        return type;
    }

    public void sendLiveMessage(Long orderId, Long userId, String openId, String title, String startTime, String endTime, String teacher) {
        try {
            userMapper.update(new UpdateWrapper<User>().setSql(String.format("%s = %s - 1", User.LIVE_MESSAGE_NUM, User.LIVE_MESSAGE_NUM))
                    .eq(User.ID, userId).gt(User.LIVE_MESSAGE_NUM, 0));
            List<MsgData> messageList = List.of(
                    new MsgData("thing1", title),
                    new MsgData("time2", startTime),
                    new MsgData("time6", endTime),
                    new MsgData("thing5", teacher),
                    new MsgData("thing4", "直播即将开始，请及时进入直播间参与课程")
            );
            sendMessage(orderId, openId, "IS93B-TZC0qDy6geU7TN4ZoiJopOMnboSSEmgk3bl7w", messageList);
        } catch (WxErrorException e) {
            log.error("[{}]发送消息异常", openId, e);
        }
    }

    public void sendTaskMessage(Long orderId, Long userId, String openId, String clazzName, String type, String title, String endTime) {
        try {
            userMapper.update(new UpdateWrapper<User>().setSql(String.format("%s = %s - 1", User.TASK_MESSAGE_NUM, User.TASK_MESSAGE_NUM))
                    .eq(User.ID, userId).gt(User.TASK_MESSAGE_NUM, 0));
            List<MsgData> messageList = List.of(
                    new MsgData("thing12", clazzName),
                    new MsgData("thing9", title),
                    new MsgData("thing19", type),
                    new MsgData("date4", endTime),
                    new MsgData("thing7", "请尽快完成作业，如作业已提交请忽略")
            );
            sendMessage(orderId, openId, "ornnC8IaY0W8d8VKxHmATb8R9-SBqJSDwyzGjnwAe-k", messageList);
        } catch (WxErrorException e) {
            log.error("[{}]发送消息异常", openId, e);
        }
    }

    public void sendTaskRemarkMessage(Long userId, String openId, String page, String clazzName, String type, String title, String teacher, String time) {
        try {
            userMapper.update(new UpdateWrapper<User>().setSql(String.format("%s = %s - 1", User.REMARK_MESSAGE_NUM, User.REMARK_MESSAGE_NUM))
                    .eq(User.ID, userId).gt(User.REMARK_MESSAGE_NUM, 0));
            List<MsgData> messageList = List.of(
                    new MsgData("thing4", clazzName),
                    new MsgData("thing9", title),
                    new MsgData("thing5", type),
                    new MsgData("thing13", teacher),
                    new MsgData("time14", time)
            );
            sendMessage(openId, "Zh1y8HsybQvwAzAuTmSFMdC3jjNlzhrrxtH7HdJ35tM", page, messageList);
        } catch (WxErrorException e) {
            log.error("[{}]发送消息异常", openId, e);
        }
    }

    public void sendMessage(Long orderId, String openId, String templateId, List<MsgData> messageList) throws WxErrorException {
        sendMessage(openId, templateId, String.format("pages/index/index?orderId=%d", orderId), messageList);
    }

    public void sendMessage(String openId, String templateId, String page, List<MsgData> messageList) throws WxErrorException {
        WxMaSubscribeMessage message = WxMaSubscribeMessage.builder()
                        .toUser(openId).templateId(templateId).page(page).data(messageList).build();
        wxMaService.getMsgService().sendSubscribeMsg(message);
        log.info("[{}]微信消息发送成功", openId);
    }
}
