package com.fuyingedu.training.front.feign;

import com.fuyingedu.training.front.model.feign.UnionItem;
import com.fuyingedu.training.front.model.feign.UserItem;
import com.fuyingedu.training.front.model.feign.UserReq;
import feign.Headers;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Map;

@Headers("Internal-Access: dHJhaW5pbmctYWRtaW46JEJvMlU2dSY=")
@FeignClient(name = "abm-user-service", path = "user", url = "https://gateway.fuyingy.com/api")
public interface FuyingUserFeign {

    @GetMapping("get/by/uid")
    UserItem getUserByUid(@RequestParam("uid") Long uid);

    @PostMapping("register")
    UserItem getUserByPhone(@RequestBody UserReq userReq);

    @GetMapping("third/auth/wechat/unionId")
    UnionItem getUnionId(@RequestParam("uid") Long uid);

    @PostMapping("third/party/get/list")
    String getOpenIdList(@RequestBody Map<String, Object> params);
}
