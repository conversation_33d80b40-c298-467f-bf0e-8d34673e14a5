package com.fuyingedu.training.front.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fuyingedu.training.common.model.CommResp;
import com.fuyingedu.training.common.util.RespUtils;
import com.fuyingedu.training.entity.Clazz;
import com.fuyingedu.training.entity.Order;
import com.fuyingedu.training.entity.OrderAssistant;
import com.fuyingedu.training.entity.WordPkConfig;
import com.fuyingedu.training.front.manager.PkManager;
import com.fuyingedu.training.front.manager.StatsManager;
import com.fuyingedu.training.front.model.pk.ItemResp;
import com.fuyingedu.training.front.model.pk.StatusResp;
import com.fuyingedu.training.front.model.stats.GroupResp;
import com.fuyingedu.training.front.model.stats.RewardResp;
import com.fuyingedu.training.mapper.ClazzMapper;
import com.fuyingedu.training.mapper.OrderAssistantMapper;
import com.fuyingedu.training.mapper.OrderMapper;
import com.fuyingedu.training.mapper.WordPkConfigMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Service
@Slf4j
public class FrontPkService {

    @Autowired
    private WordPkConfigMapper wordPkConfigMapper;
    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private StatsManager statsManager;
    @Autowired
    private OrderAssistantMapper orderAssistantMapper;
    @Autowired
    private PkManager pkManager;
    @Autowired
    private ClazzMapper clazzMapper;

    public CommResp<StatusResp> status(Long orderId) {
        Order order = orderMapper.selectOne(new QueryWrapper<Order>().select(
                Order.SCHEDULE_ID, Order.ID, Order.USER_ID
        ).eq(Order.ID, orderId));
        if (order == null || order.getScheduleId() == null) {
            StatusResp statusResp = new StatusResp();
            statusResp.setStatus((byte) 1);
            return RespUtils.success(statusResp);
        }
        StatusResp statusResp = pkManager.status(order.getScheduleId());
        List<Long> userIds = new ArrayList<>(orderAssistantMapper.selectList(new QueryWrapper<OrderAssistant>().select(
                OrderAssistant.USER_ID
        ).eq(OrderAssistant.ORDER_ID, orderId)).stream().map(OrderAssistant::getUserId).toList());
        userIds.add(order.getUserId());
        statusResp.setUserIds(userIds);
        return RespUtils.success(statusResp);
    }

    public CommResp<List<ItemResp>> list(Long orderId) {
        Order order = orderMapper.selectOne(new QueryWrapper<Order>().select(
                Order.SCHEDULE_ID, Order.ID
        ).eq(Order.ID, orderId));
        if (order == null || order.getScheduleId() == null) {
            return RespUtils.success(Collections.emptyList());
        }
        List<WordPkConfig> configList = wordPkConfigMapper.selectList(new QueryWrapper<WordPkConfig>().select(
                WordPkConfig.START_TIME, WordPkConfig.ID
        ).eq(WordPkConfig.SCHEDULE_ID, order.getScheduleId()).le(WordPkConfig.STOP_TIME, LocalDateTime.now()).orderByDesc(WordPkConfig.START_TIME));
        return RespUtils.success(configList.stream().map(
                item -> {
                    ItemResp itemResp = new ItemResp();
                    itemResp.setPkId(item.getId());
                    itemResp.setStartTime(item.getStartTime());
                    return itemResp;
                }
        ).toList());
    }

    public CommResp<List<RewardResp>> reward(Long orderId, Long pkId) {
        Order order = orderMapper.selectOne(new QueryWrapper<Order>().select(
                Order.SCHEDULE_ID, Order.ID, Order.CLAZZ_ID
        ).eq(Order.ID, orderId));
        if (order.getClazzId() == null) {
            return RespUtils.success(Collections.emptyList());
        }
        pkId = getPkId(order, pkId);
        if (pkId == null) {
            return RespUtils.success(Collections.emptyList());
        }
        return RespUtils.success(statsManager.pkRewardList(pkId, order.getClazzId()));
    }

    public CommResp<List<RewardResp>> rewardByPkId(Long clazzId, Long pkId) {
        pkId = getPkIdByClazz(clazzId, pkId);
        if (pkId == null) {
            return RespUtils.success(Collections.emptyList());
        }
        return RespUtils.success(statsManager.pkRewardList(pkId, clazzId));
    }

    public CommResp<List<GroupResp>> group(Long orderId, Long pkId) {
        Order order = orderMapper.selectOne(new QueryWrapper<Order>().select(
                Order.SCHEDULE_ID, Order.ID, Order.CLAZZ_ID
        ).eq(Order.ID, orderId));
        if (order.getClazzId() == null) {
            return RespUtils.success(Collections.emptyList());
        }
        pkId = getPkId(order, pkId);
        if (pkId == null) {
            return RespUtils.success(Collections.emptyList());
        }
        return RespUtils.success(statsManager.pkGroupList(pkId, order.getClazzId()));
    }

    public CommResp<List<GroupResp>> groupByPkId(Long clazzId, Long pkId) {
        pkId = getPkIdByClazz(clazzId, pkId);
        if (pkId == null) {
            return RespUtils.success(Collections.emptyList());
        }
        return RespUtils.success(statsManager.pkGroupList(pkId, clazzId));
    }

    private Long getPkIdByClazz(Long clazzId, Long pkId) {
        if (pkId != null) {
            return pkId;
        }
        Clazz clazz = clazzMapper.selectOne(new QueryWrapper<Clazz>().select(Clazz.SCHEDULE_ID).eq(Clazz.ID, clazzId));
        if (clazz == null || clazz.getScheduleId() == null) {
            return null;
        }
        return pkManager.getPkId(clazz.getScheduleId());
    }

    private Long getPkId(Order order, Long pkId) {
        if (pkId != null) {
            return pkId;
        }
        if (order == null || order.getScheduleId() == null) {
            return null;
        }
        return pkManager.getPkId(order.getScheduleId());
    }
}
