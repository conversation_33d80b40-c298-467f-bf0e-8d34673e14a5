package com.fuyingedu.training.front.controller;

import com.fuyingedu.training.common.annotation.Login;
import com.fuyingedu.training.common.model.CommResp;
import com.fuyingedu.training.front.model.stats.GroupResp;
import com.fuyingedu.training.front.model.stats.RewardResp;
import com.fuyingedu.training.front.model.stats.ScheduleResp;
import com.fuyingedu.training.front.service.FrontStatsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 扶小鹰-Word鹰排行榜
 */
@RestController
@RequestMapping("fxy/stat")
public class FxyStatsController {

    @Autowired
    private FrontStatsService frontStatsService;

    /**
     * 个人榜
     */
    @GetMapping("reward")
    public CommResp<ScheduleResp<RewardResp>> reward(Long orderId) {
        return frontStatsService.reward(orderId);
    }

    /**
     * 小组榜
     */
    @GetMapping("group")
    public CommResp<ScheduleResp<GroupResp>> group(Long orderId) {
        return frontStatsService.group(orderId);
    }

    /**
     * 小太阳个人榜
     */
    @GetMapping("fxy/reward")
    public CommResp<ScheduleResp<RewardResp>> fxyReward(@Login Long userId, Long orderId) {
        return frontStatsService.fxyReward(orderId);
    }

    /**
     * 小太阳小组榜
     */
    @GetMapping("fxy/group")
    public CommResp<ScheduleResp<GroupResp>> fxyGroup(@Login Long userId, Long orderId) {
        return frontStatsService.fxyGroup(orderId);
    }
}
