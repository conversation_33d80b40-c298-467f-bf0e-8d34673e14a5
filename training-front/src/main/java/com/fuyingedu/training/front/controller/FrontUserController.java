package com.fuyingedu.training.front.controller;

import com.fuyingedu.training.common.annotation.Login;
import com.fuyingedu.training.common.model.CommResp;
import com.fuyingedu.training.common.util.RespUtils;
import com.fuyingedu.training.front.model.user.*;
import com.fuyingedu.training.front.service.FrontUserService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 学员端-我的
 */
@RestController
@RequestMapping("front/user")
public class FrontUserController {

    @Autowired
    private FrontUserService frontUserService;

    /**
     * 用户基础信息
     * @param userId 不传
     * @param orderId 服务单ID
     */
    @GetMapping("info")
    public CommResp<InfoResp> info(@Login Long userId, @RequestParam("orderId") Long orderId) {
        return frontUserService.info(userId, orderId);
    }

    /**
     * 学员详情-同班同学用户基本信息
     */
    @GetMapping("classmate/info")
    public CommResp<InfoResp> classmateInfo(@Login Long userId, @RequestParam("orderId") Long orderId) {
        return frontUserService.classmateInfo(orderId);
    }

    /**
     * 获取的奖牌列表
     * @param userId 不传
     * @param orderId 服务单ID
     */
    @GetMapping("medal/list")
    public CommResp<List<MedalItemResp>> medalList(@Login Long userId, @RequestParam("orderId") Long orderId) {
        return frontUserService.medalList(orderId);
    }

    /**
     * 家长和扶小鹰信息
     * @param userId 不传
     * @param orderId 服务单ID
     */
    @GetMapping("account/info")
    public CommResp<AccountInfoResp> accountInfo(@Login Long userId, @RequestParam("orderId") Long orderId) {
        return frontUserService.accountInfo(userId, orderId);
    }

    /**
     * 解除绑定
     */
    @PostMapping("delete/user")
    public CommResp<?> deleteUser(@Login Long userId, @RequestBody @Valid DeleteUserReq deleteUserReq) {
        frontUserService.deleteUser(userId, deleteUserReq);
        return RespUtils.success();
    }

    /**
     * 判断是否需要订阅消息授权
     */
    @GetMapping("message/num")
    public CommResp<MessageNumResp> messageNum(@Login Long userId) {
        return frontUserService.messageNum(userId);
    }

    /**
     * 上报订阅消息
     */
    @PostMapping("report/message")
    public CommResp<?> reportMessage(@Login Long userId, @RequestBody @Valid ReportMessageReq reportMessageReq) {
        frontUserService.reportMessage(userId, reportMessageReq);
        return RespUtils.success();
    }

    /**
     * 更新用户昵称和头像
     */
    @PostMapping("update")
    public CommResp<?> update(@Login Long userId, @RequestBody @Valid UpdateReq updateReq) {
        frontUserService.update(userId, updateReq);
        return RespUtils.success();
    }
}
