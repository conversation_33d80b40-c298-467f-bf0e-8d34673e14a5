package com.fuyingedu.training.front.model.task;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;

@ToString
@Getter
@Setter
public class LiveItemResp {
    /**
     * 任务名称
     */
    private String liveName;

    /**
     * 直播开始时间
     */
    private LocalDateTime startTime;

    /**
     * 直播间信息
     */
    private String liveInfo;

    /**
     * 直播状态 1-未开始 2-今日开播 3-直播中 4-直播结束（可回看） 5-直播结束（不可回看）
     */
    private Byte liveStatus;

    /**
     * 距离开始的秒数
     */
    private Long distance;
    /**
     * 直播房间号
     */
    private String liveRoom;

    /**
     * 直播密码
     */
    private String livePassword;

    /**
     * 回放链接
     */
    private String repeatUrl;
    /**
     * 直播方式 1-公司直播 2-导师直播
     * 字典Key：LIVE_TYPE
     */
    private Byte liveType;

    /**
     * 直播端口 1-腾讯会议 2-保利威 3-百家云
     * 字典Key：LIVE_PORT
     */
    private Byte livePort;

    private Integer wordReward;
}
