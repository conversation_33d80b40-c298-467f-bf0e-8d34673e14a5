package com.fuyingedu.training.front.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fuyingedu.training.common.enums.Status;
import com.fuyingedu.training.common.model.CommResp;
import com.fuyingedu.training.common.util.RespUtils;
import com.fuyingedu.training.entity.Label;
import com.fuyingedu.training.front.model.label.ItemResp;
import com.fuyingedu.training.front.model.media.MediaConvertor;
import com.fuyingedu.training.mapper.LabelMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class FrontLabelService {

    @Autowired
    private LabelMapper labelMapper;

    public CommResp<List<ItemResp>> list() {
        List<Label> labelList = labelMapper.selectList(new QueryWrapper<Label>().select(
                Label.ID, Label.LABEL_NAME, Label.MEDIA_URL, Label.LABEL_TYPE
        ).eq(Label.LABEL_STATUS, Status.NORMAL.getCode()).orderByDesc(Label.ID));
        List<ItemResp> itemRespList = labelList.stream().map(label -> {
            ItemResp itemResp = new ItemResp();
            itemResp.setId(label.getId());
            itemResp.setLabelName(label.getLabelName());
            itemResp.setLabelType(label.getLabelType());
            itemResp.setMediaUrl(MediaConvertor.getMediaUrl(label.getMediaUrl()));
            return itemResp;
        }).toList();
        return RespUtils.success(itemRespList);
    }
}
