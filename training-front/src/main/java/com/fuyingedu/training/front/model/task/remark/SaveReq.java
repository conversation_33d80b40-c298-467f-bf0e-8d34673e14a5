package com.fuyingedu.training.front.model.task.remark;

import com.fuyingedu.training.front.model.media.MediaReq;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@ToString
@Getter
@Setter
public class SaveReq {

    /**
     * 点评ID 不为空时表示修改
     */
    private Long remarkId;

    /**
     * 点评的记录ID
     */
    @NotNull
    private Long recordId;

    /**
     * 服务单ID
     */
    private Long orderId;

    /**
     * 点评内容
     */
    private String remark;

    /**
     * 图片列表
     */
    private List<MediaReq> mediaUrlList;

    /**
     * 标签ID列表
     */
    private List<Long> labelIdList;

    /**
     * 优秀推荐 2-优秀作业
     */
    private Byte recordType;
}
