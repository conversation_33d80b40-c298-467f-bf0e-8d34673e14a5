package com.fuyingedu.training.front.manager;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;
import org.springframework.util.DigestUtils;
import org.springframework.web.client.RestTemplate;

import java.nio.charset.StandardCharsets;

@Component
@Slf4j
public class FxyWordManager {

    @Autowired
    private RestTemplate restTemplate;

    @Value("${fxy.pad-url}")
    private String padUrl;

    public void login(Long uid, String token) {
        HttpHeaders headers = new HttpHeaders();
        long currTimestamp = System.currentTimeMillis() / 1000;
        headers.set("Request-Time", currTimestamp + "");
        headers.set("Sign", DigestUtils.md5DigestAsHex(String.format("requestTime=%d&token=%s&key=46AB61D2CB4E190A", currTimestamp, token).getBytes(StandardCharsets.UTF_8)).toUpperCase());
        HttpEntity<Object> httpEntity = new HttpEntity<>(headers);
        String s = restTemplate.postForObject(padUrl + String.format("api/pad/trainingCamp/login?uid=%d&token=%s&tokenCheckFlag=false",
                uid, token), httpEntity, String.class);
        log.info("word-{}:{}", uid, s);
    }

    public void login(String token) {
        HttpHeaders headers = new HttpHeaders();
        long currTimestamp = System.currentTimeMillis() / 1000;
        headers.set("Request-Time", currTimestamp + "");
        headers.set("Sign", DigestUtils.md5DigestAsHex(String.format("requestTime=%d&token=%s&key=46AB61D2CB4E190A", currTimestamp, token).getBytes(StandardCharsets.UTF_8)).toUpperCase());
        HttpEntity<Object> httpEntity = new HttpEntity<>(headers);
        String s = restTemplate.postForObject(padUrl + String.format("api/pad/trainingCamp/login?token=%s&tokenCheckFlag=true",
                token), httpEntity, String.class);
        log.info("word:{}", s);
    }
}
