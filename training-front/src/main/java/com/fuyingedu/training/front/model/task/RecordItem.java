package com.fuyingedu.training.front.model.task;

import com.fuyingedu.training.front.model.media.MediaReq;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@ToString
@Getter
@Setter
public class RecordItem {
    /**
     * 服务单ID
     */
    @NotNull
    private Long orderId;

    /**
     * 任务ID
     */
    @NotNull
    private Long taskId;

    /**
     * 打卡内容
     */
    private String punchContent;

    /**
     * 打卡上传的素材列表
     */
    private List<MediaReq> punchUrls;
}
