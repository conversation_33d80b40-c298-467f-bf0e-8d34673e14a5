package com.fuyingedu.training.front.manager;

import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.Producer;
import com.aliyun.openservices.ons.api.SendResult;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.fuyingedu.training.common.enums.RewardLogType;
import com.fuyingedu.training.common.util.AsyncUtils;
import com.fuyingedu.training.common.util.JsonUtils;
import com.fuyingedu.training.entity.*;
import com.fuyingedu.training.front.model.fxy.RewardDto;
import com.fuyingedu.training.mapper.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.nio.charset.StandardCharsets;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Component
public class RewardManager {

    @Autowired
    private WordRewardMapper wordRewardMapper;
    @Autowired
    private OrderRewardLogMapper orderRewardLogMapper;
    @Autowired
    private OrderFxyMapper orderFxyMapper;
    @Autowired
    private OrderFxyRewardLogMapper orderFxyRewardLogMapper;
    @Autowired
    private WordRewardLogMapper wordRewardLogMapper;
    @Autowired
    private FxyMapper fxyMapper;
    @Autowired
    private Producer rocketProducer;
    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private OrderStatisticMapper orderStatisticMapper;

    @Transactional(rollbackFor = Exception.class)
    public Long saveFxyReward(Long orderId, Long userId, Long orderUserId, Integer fxyReward, String remark) {
        return sendFxyMessage(orderId, userId, fxyReward, remark);
    }

    @Transactional(rollbackFor = Exception.class)
    public Long saveReward(Long orderId, Long userId, Integer orderReward, String remark) {
        orderStatisticMapper.update(new UpdateWrapper<OrderStatistic>()
                .setSql(String.format("%s = %s + %d", OrderStatistic.TASK_REWARD, OrderStatistic.TASK_REWARD, orderReward))
                .eq(OrderStatistic.ORDER_ID,orderId));
        OrderRewardLog orderRewardLog = new OrderRewardLog();
        orderRewardLog.setOrderId(orderId);
        orderRewardLog.setCreatedUserId(userId);
        orderRewardLog.setLogType(RewardLogType.PLUS.getCode());
        orderRewardLog.setTaskReward(orderReward);
        orderRewardLog.setLogRemark(remark);
        orderRewardLogMapper.insert(orderRewardLog);
        return orderRewardLog.getId();
    }

    @Transactional(rollbackFor = Exception.class)
    public Long saveWordReward(Long orderId, Long userId, Long orderUserId, Integer wordReward, String remark) {
        WordReward reward = wordRewardMapper.selectOne(new QueryWrapper<WordReward>().select(
                WordReward.ID, WordReward.REWARD_NUM
        ).eq(WordReward.ORDER_ID, orderId));
        if (reward == null) {
            reward = new WordReward();
            reward.setUserId(orderUserId);
            reward.setOrderId(orderId);
            reward.setRewardNum(wordReward);
            wordRewardMapper.insert(reward);
        } else {
            wordRewardMapper.update(new UpdateWrapper<WordReward>()
                    .setSql(String.format("%s = %s + %d", WordReward.REWARD_NUM, WordReward.REWARD_NUM, wordReward))
                    .eq(WordReward.ID, reward.getId()));
        }
        WordRewardLog wordRewardLog = new WordRewardLog();
        wordRewardLog.setUserId(orderUserId);
        wordRewardLog.setOrderId(orderId);
        wordRewardLog.setRewardNum(wordReward);
        wordRewardLog.setRemark(remark);
        wordRewardLogMapper.insert(wordRewardLog);
        sendWordMessage(orderId, wordRewardLog);
        return wordRewardLog.getId();
    }

    @Transactional(rollbackFor = Exception.class)
    public void saveReward(Long orderId, Long userId, Long orderUserId,
                           Integer orderReward, Integer fxyReward, Integer wordReward, String remark) {
        if (orderReward != null && orderReward > 0) {
            saveReward(orderId, userId, orderReward, remark);
        }
        if (wordReward != null && wordReward > 0) {
            saveWordReward(orderId, userId, orderUserId, wordReward, remark);
        }
        if (fxyReward != null && fxyReward > 0) {
            sendFxyMessage(orderId, userId, fxyReward, remark);
        }
    }

    private Long sendFxyMessage(Long orderId, Long userId, Integer fxyReward, String remark) {
        OrderFxy orderFxy = orderFxyMapper.selectOne(new QueryWrapper<OrderFxy>().select(
                OrderFxy.FXY_ID
        ).eq(OrderFxy.ORDER_ID, orderId));
        if (orderFxy == null) {
            log.error("服务单[{}]没有绑定扶小鹰", orderId);
            return null;
        }
        Fxy fxy = fxyMapper.selectOne(new QueryWrapper<Fxy>().select(
                Fxy.OPEN_ID
        ).eq(Fxy.ID, orderFxy.getFxyId()));
        if (fxy == null) {
            log.error("没有找到对应扶小鹰[{}]", orderFxy.getFxyId());
            return null;
        }
        orderStatisticMapper.update(new UpdateWrapper<OrderStatistic>()
                        .setSql(String.format("%s = %s + %d", OrderStatistic.FXY_REWARD, OrderStatistic.FXY_REWARD, fxyReward))
                        .eq(OrderStatistic.ORDER_ID, orderId));
        OrderFxyRewardLog orderFxyRewardLog = new OrderFxyRewardLog();
        orderFxyRewardLog.setOrderId(orderId);
        orderFxyRewardLog.setCreatedUserId(userId);
        orderFxyRewardLog.setLogType(RewardLogType.PLUS.getCode());
        orderFxyRewardLog.setTaskReward(fxyReward);
        orderFxyRewardLog.setLogRemark(remark);
        orderFxyRewardLogMapper.insert(orderFxyRewardLog);
        sendFxyMessage(fxy.getOpenId(), orderFxyRewardLog);
        return orderFxyRewardLog.getId();
    }

    public void sendFxyMessage(String openId, OrderFxyRewardLog rewardLog) {
        AsyncUtils.execute(() -> {
            RewardDto rewardDto = new RewardDto();
            rewardDto.setStudentOpenId(openId);
            rewardDto.setNum(rewardLog.getTaskReward());
            rewardDto.setDesc(rewardLog.getLogRemark());
            rewardDto.setSn(rewardLog.getId());
            rewardDto.setTokenType(11);
            Message message = new Message("sprint_app", "spring_app_update_token", JsonUtils.formatObjToJson(rewardDto).getBytes(StandardCharsets.UTF_8));
            SendResult result = rocketProducer.send(message);
            if (StringUtils.hasLength(result.getMessageId())) {
                orderFxyRewardLogMapper.update(new UpdateWrapper<OrderFxyRewardLog>().set(OrderFxyRewardLog.LOG_STATUS, 2)
                        .eq(OrderFxyRewardLog.ID, rewardLog.getId()));
            }
            log.info("增加太阳币消息发送成功,resp=[{}]", JsonUtils.formatObjToJson(result));
        }, "增加太阳币消息发送");
    }

    public void sendWordMessage(Long orderId, WordRewardLog rewardLog) {
        AsyncUtils.execute(() -> {
            OrderFxy orderFxy = orderFxyMapper.selectOne(new QueryWrapper<OrderFxy>().select(
                    OrderFxy.FXY_ID
            ).eq(OrderFxy.ORDER_ID, orderId));
            if (orderFxy == null) {
                log.warn("orderId=[{}]没有绑定扶小鹰", orderId);
                return;
            }
            Fxy fxy = fxyMapper.selectOne(new QueryWrapper<Fxy>().select(
                    Fxy.OPEN_ID
            ).eq(Fxy.ID, orderFxy.getFxyId()));
            Set<Long> orderIds = orderFxyMapper.selectList(new QueryWrapper<OrderFxy>().select(
                    OrderFxy.ORDER_ID
            ).eq(OrderFxy.FXY_ID, orderFxy.getFxyId())).stream().map(OrderFxy::getOrderId).collect(Collectors.toSet());
            Set<Long> userIds = orderMapper.selectList(new QueryWrapper<Order>().select(
                    Order.USER_ID
            ).in(Order.ID, orderIds)).stream().map(Order::getUserId).collect(Collectors.toSet());
            Integer rewardNum = wordRewardMapper.getRewardByUserIds(userIds);
            if (rewardNum == null) {
                rewardNum = 0;
            }
            RewardDto rewardDto = new RewardDto();
            rewardDto.setStudentOpenId(fxy.getOpenId());
            rewardDto.setNum(rewardNum);
            rewardDto.setDesc(rewardLog.getRemark());
            rewardDto.setSn(rewardLog.getId());
            rewardDto.setTokenType(12);
            Message message = new Message("sprint_app", "spring_app_update_token", JsonUtils.formatObjToJson(rewardDto).getBytes(StandardCharsets.UTF_8));
            SendResult result = rocketProducer.send(message);
            if (StringUtils.hasLength(result.getMessageId())) {
                wordRewardLogMapper.update(new UpdateWrapper<WordRewardLog>().set(WordRewardLog.LOG_STATUS, 2)
                        .eq(WordRewardLog.ID, rewardLog.getId()));
            }
            log.info("增加单词训练营积分消息发送成功,resp=[{}]", JsonUtils.formatObjToJson(result));
        }, "增加单词训练营积分消息发送");
    }
}
