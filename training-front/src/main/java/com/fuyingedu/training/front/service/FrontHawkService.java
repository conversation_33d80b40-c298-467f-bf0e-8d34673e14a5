package com.fuyingedu.training.front.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fuyingedu.training.common.exception.WebBaseException;
import com.fuyingedu.training.common.model.CommResp;
import com.fuyingedu.training.common.util.RespUtils;
import com.fuyingedu.training.entity.*;
import com.fuyingedu.training.front.model.media.MediaConvertor;
import com.fuyingedu.training.front.model.word.*;
import com.fuyingedu.training.mapper.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class FrontHawkService {

    @Autowired
    private WordHawkMapper wordHawkMapper;
    @Autowired
    private WordHawkRecordMapper wordHawkRecordMapper;
    @Autowired
    private WordRewardMapper wordRewardMapper;
    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private WordRewardLogMapper wordRewardLogMapper;

    public CommResp<HawkResp> list(Long orderId) {
        Order order = orderMapper.selectOne(new QueryWrapper<Order>().select(Order.USER_ID).eq(Order.ID, orderId));
        Integer rewardNum = wordRewardMapper.getRewardByUserIds(Collections.singleton(order.getUserId()));
        Integer orderRewardNum = wordRewardMapper.getRewardByOrderIds(Collections.singleton(orderId));
        int finalRewardNum = rewardNum == null ? 0 : rewardNum;
        HawkResp hawkResp = new HawkResp();
        hawkResp.setRewardNum(rewardNum);
        hawkResp.setOrderRewardNum(orderRewardNum == null ? 0 : orderRewardNum);
        List<WordHawk> hawkList = wordHawkMapper.selectList(new LambdaQueryWrapper<>());
        if (hawkList.isEmpty()) {
            return RespUtils.success(hawkResp);
        }
        Map<Long, WordHawkRecord> hawkIds = wordHawkRecordMapper.selectList(new QueryWrapper<WordHawkRecord>().select(
                WordHawkRecord.HAWK_ID, WordHawkRecord.CREATED_TIME
        ).eq(WordHawkRecord.USER_ID, order.getUserId())).stream().collect(Collectors.toMap(WordHawkRecord::getHawkId, Function.identity()));
        hawkResp.setHawkList(hawkList.stream().map(hawk -> {
            HawkResp.Hawk resp = new HawkResp.Hawk();
            resp.setHawkId(hawk.getId());
            resp.setHawkName(hawk.getHawkName());
            resp.setHawkContent(hawk.getHawkContent());
            resp.setHawkUrl(MediaConvertor.getMediaUrl(hawk.getHawkUrl()));
            resp.setCoverUrl(MediaConvertor.getMediaUrl(hawk.getCoverUrl()));
            resp.setLockUrl(MediaConvertor.getMediaUrl(hawk.getLockUrl()));
            resp.setUnlockUrl(MediaConvertor.getMediaUrl(hawk.getUnlockUrl()));
            WordHawkRecord record = hawkIds.get(hawk.getId());
            if (record != null) {
                resp.setUnlockTime(record.getCreatedTime());
                resp.setLockedStatus((byte) 1);
            } else {
                resp.setLockedStatus(hawk.getRewardNum() <= finalRewardNum ? (byte) 2 : (byte) 3);
            }
            resp.setRewardNum(hawk.getRewardNum());
            return resp;
        }).collect(Collectors.toList()));
        return RespUtils.success(hawkResp);
    }

    public void unlock(HawkReq hawkReq) {
        Order order = orderMapper.selectOne(new QueryWrapper<Order>().select(Order.USER_ID).eq(Order.ID, hawkReq.getOrderId()));
        Long userId = order.getUserId();
        Long hawkId = hawkReq.getHawkId();
        WordHawkRecord record = wordHawkRecordMapper.selectOne(new QueryWrapper<WordHawkRecord>().select(WordHawkRecord.ID)
                .eq(WordHawkRecord.USER_ID, userId).eq(WordHawkRecord.HAWK_ID, hawkId));
        if (record != null) {
            return;
        }
        Integer rewardNum = wordRewardMapper.getRewardByUserIds(Collections.singleton(order.getUserId()));
        rewardNum = rewardNum == null ? 0 : rewardNum;
        WordHawk hawk = wordHawkMapper.selectOne(new QueryWrapper<WordHawk>().select(WordHawk.REWARD_NUM).eq(WordHawk.ID, hawkId));
        if (hawk == null || hawk.getRewardNum() > rewardNum) {
            throw new WebBaseException(500, "当前积分不足，无法解锁Word鹰");
        }
        record = new WordHawkRecord();
        record.setUserId(userId);
        record.setHawkId(hawkId);
        wordHawkRecordMapper.insert(record);
    }

    public CommResp<List<RewardItemResp>> hawkRewardList(RewardReq req) {
        IPage<WordRewardLog> page = new Page<>(req.getPageNum(), req.getPageSize());
        page = wordRewardLogMapper.selectPage(page, new QueryWrapper<WordRewardLog>()
                .select(WordRewardLog.CREATED_TIME, WordRewardLog.REWARD_NUM, WordRewardLog.REMARK)
                .eq(WordRewardLog.ORDER_ID, req.getOrderId()).orderByDesc(WordReward.ID));
        List<RewardItemResp> itemList = page.getRecords().stream().map(rewardLog -> {
            RewardItemResp item = new RewardItemResp();
            item.setCreatedTime(rewardLog.getCreatedTime());
            item.setRewardNum(rewardLog.getRewardNum());
            item.setMessage(rewardLog.getRemark());
            return item;
        }).collect(Collectors.toList());
        return RespUtils.success(page.getCurrent(), page.getSize(), page.getTotal(), itemList);
    }

    public CommResp<RewardResp> orderReward(Long orderId) {
        Integer rewardNum = wordRewardMapper.getRewardByOrderIds(Collections.singleton(orderId));
        RewardResp resp = new RewardResp();
        resp.setOrderId(orderId);
        resp.setRewardNum(rewardNum);
        return RespUtils.success(resp);
    }
}
