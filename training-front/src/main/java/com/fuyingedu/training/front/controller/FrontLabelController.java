package com.fuyingedu.training.front.controller;

import com.fuyingedu.training.common.model.CommResp;
import com.fuyingedu.training.front.model.label.ItemResp;
import com.fuyingedu.training.front.service.FrontLabelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 标签管理
 */
@RequestMapping("front/label")
@RestController
public class FrontLabelController {

    @Autowired
    private FrontLabelService frontLabelService;

    /**
     * 标签列表
     */
    @GetMapping("list")
    public CommResp<List<ItemResp>> list() {
        return frontLabelService.list();
    }
}
