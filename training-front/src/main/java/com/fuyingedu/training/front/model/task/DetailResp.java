package com.fuyingedu.training.front.model.task;

import com.fuyingedu.training.front.model.media.MediaResp;
import com.fuyingedu.training.front.model.task.remark.RemarkResp;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.List;

@ToString
@Getter
@Setter
public class DetailResp {

    private Long recordId;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 点赞数量
     */
    private Integer likeNum;

    /**
     * 点赞状态 0-未点赞 1-已点赞
     */
    private Byte like;

    /**
     * 1-普通作业 2-优秀作业
     */
    private Byte recordType;
    /**
     * 打卡/作业内容
     */
    private String content;

    /**
     * 打卡/作业上传的素材列表
     */
    private List<MediaResp> mediaList;

    /**
     * 打卡/作业时间
     */
    private LocalDateTime createdTime;

    /**
     * 点评列表
     */
    private List<RemarkResp> remarkList;


}
