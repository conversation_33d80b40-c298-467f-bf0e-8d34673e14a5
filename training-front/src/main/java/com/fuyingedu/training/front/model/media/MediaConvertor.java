package com.fuyingedu.training.front.model.media;

import com.fuyingedu.training.common.exception.WebBaseException;
import com.fuyingedu.training.common.util.JsonUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;

public class MediaConvertor {

    private static final String OSS_URL_PREFIX = "https://fuying-trainning.oss-cn-hangzhou.aliyuncs.com/";
    private static final String OSS_URL_PREFIX_HTTP = "http://fuying-trainning.oss-cn-hangzhou.aliyuncs.com/";
    private static final String CND_URL_PREFIX = "https://trainning-camp-oss.fuyingy.com/";


    public static String getUrlSuffix(String mediaUrl) {
        if (mediaUrl == null) {
            return null;
        }
        if (mediaUrl.startsWith("https")) {
            return mediaUrl.replace(OSS_URL_PREFIX, "");
        }
        return mediaUrl.replace(OSS_URL_PREFIX_HTTP, "");
    }

    public static String getMediaUrl(String mediaUrl) {
        if (!StringUtils.hasLength(mediaUrl)) {
            return "";
        }
        if (mediaUrl.startsWith("http")) {
            return mediaUrl;
        }
        return CND_URL_PREFIX + mediaUrl;
    }

    public static Byte getMediaType(String mediaType) {
        if (mediaType.startsWith("text")) {
            return 1;
        } else if (mediaType.startsWith("image")) {
            return 2;
        } else if (mediaType.startsWith("video")) {
            return 3;
        } else if (mediaType.startsWith("audio")) {
            return 4;
        } else if (mediaType.endsWith("mp3")) {
            return 4;
        }
        throw new WebBaseException(4000, "不支持的媒体类型");
    }

    public static List<MediaResp> getMediaList(String mediaUrls) {
        if (!StringUtils.hasLength(mediaUrls)) {
            return Collections.emptyList();
        }
        List<MediaResp> mediaRespList = JsonUtils.parseJsonToList(mediaUrls, MediaResp.class);
        return mediaRespList.stream().sorted(Comparator.comparingInt(MediaResp::getType))
                .peek(mediaResp -> mediaResp.setUrl(getMediaUrl(mediaResp.getUrl()))).toList();
    }

    public static String getMediaUrls(List<MediaReq> mediaReqList) {
        if (mediaReqList == null) {
            mediaReqList = Collections.emptyList();
        }
        List<MediaResp> respList = mediaReqList.stream().map(mediaReq -> {
            MediaResp mediaResp = new MediaResp();
            mediaResp.setUrl(getUrlSuffix(mediaReq.getUrl()));
            mediaResp.setType(mediaReq.getType());
            return mediaResp;
        }).toList();
        return JsonUtils.formatObjToJson(respList);
    }

    public static String getUrls(List<String> mediaUrls) {
        if (CollectionUtils.isEmpty(mediaUrls)) {
            return null;
        }
        return JsonUtils.formatObjToJson(mediaUrls.stream().map(MediaConvertor::getUrlSuffix).toList());
    }

    public static List<String> getUrlList(String mediaUrls) {
        if (!StringUtils.hasLength(mediaUrls)) {
            return Collections.emptyList();
        }
        return JsonUtils.parseJsonToList(mediaUrls, String.class).stream().map(MediaConvertor::getMediaUrl).toList();
    }
}
