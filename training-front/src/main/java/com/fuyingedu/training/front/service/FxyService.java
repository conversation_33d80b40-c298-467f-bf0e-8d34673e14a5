package com.fuyingedu.training.front.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fuyingedu.training.common.enums.OrderStatus;
import com.fuyingedu.training.common.enums.RespMetaEnum;
import com.fuyingedu.training.common.exception.WebBaseException;
import com.fuyingedu.training.common.model.CommResp;
import com.fuyingedu.training.common.util.RedisKey;
import com.fuyingedu.training.common.util.RespUtils;
import com.fuyingedu.training.common.util.TransactionUtils;
import com.fuyingedu.training.dto.fuying.FxyItemRet;
import com.fuyingedu.training.dto.fuying.FxyListParam;
import com.fuyingedu.training.entity.*;
import com.fuyingedu.training.front.manager.FxyWordManager;
import com.fuyingedu.training.front.model.fxy.DateItemResp;
import com.fuyingedu.training.front.model.fxy.StatisticResp;
import com.fuyingedu.training.front.model.fxy.UserInfoResp;
import com.fuyingedu.training.front.model.order.FxyListReq;
import com.fuyingedu.training.front.model.order.FxySaveReq;
import com.fuyingedu.training.front.model.order.MyCampResp;
import com.fuyingedu.training.mapper.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionStatus;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Slf4j
public class FxyService {

    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private OrderStatisticMapper orderStatisticMapper;
    @Autowired
    private ScheduleMapper scheduleMapper;
    @Autowired
    private OrderFxyMapper orderFxyMapper;
    @Autowired
    private FxyMapper fxyMapper;
    @Autowired
    private RedisTemplate<String, String> redisTemplate;
    @Autowired
    private FrontOrderService frontOrderService;
    @Autowired
    private FuyingMapper fuyingMapper;
    @Autowired
    private UserMapper userMapper;
    @Value("${wx.app.url}")
    private String appUrl;
    @Autowired
    private FxyWordManager fxyWordManager;
    @Autowired
    private WordRewardMapper wordRewardMapper;

    public CommResp<List<DateItemResp>> taskDateList(Long orderId) {
        Order order = orderMapper.selectOne(new QueryWrapper<Order>().select(
                Order.SCHEDULE_ID).eq(Order.ID, orderId));
        Schedule schedule = scheduleMapper.selectOne(new QueryWrapper<Schedule>().select(
                Schedule.START_TIME, Schedule.END_TIME).eq(Schedule.ID, order.getScheduleId()));
        List<DateItemResp> respList = new ArrayList<>();
        LocalDate startDate = schedule.getStartTime().toLocalDate();
        LocalDate endDate = schedule.getEndTime().toLocalDate();
        while (!startDate.isAfter(endDate)) {
            DateItemResp resp = new DateItemResp();
            resp.setDate(startDate);
            resp.setToday(startDate.isEqual(LocalDate.now()) ? (byte) 1 : (byte) 0);
            respList.add(resp);
            startDate = startDate.plusDays(1);
        }
        return RespUtils.success(respList);
    }

    public CommResp<UserInfoResp> userInfo(String openId, String accessToken) {
        UserInfoResp resp = new UserInfoResp();
        resp.setWxUrl(appUrl);
        Fxy fxy = fxyMapper.selectOne(new QueryWrapper<Fxy>().select(
                Fxy.ID).eq(Fxy.OPEN_ID, openId)
        );
        if (fxy == null) {
            resp.setExist((byte) 0);
            return RespUtils.success(resp);
        }
        List<OrderFxy> fxyOrderList = orderFxyMapper.selectList(new QueryWrapper<OrderFxy>().select(
                OrderFxy.ORDER_ID
        ).eq(OrderFxy.FXY_ID, fxy.getId()));
        if (fxyOrderList.isEmpty()) {
            resp.setExist((byte) 0);
            return RespUtils.success(resp);
        }
        Set<Long> orderIds = fxyOrderList.stream().map(OrderFxy::getOrderId).collect(Collectors.toSet());
        Order order = orderMapper.selectOne(new QueryWrapper<Order>().select(
                Order.ID, Order.CAMP_ID, Order.SCHEDULE_ID, Order.STUDENT_ID, Order.ORDER_REMARK, Order.USER_ID
        ).in(Order.ID, orderIds).in(Order.ORDER_STATUS, OrderStatus.NORMAL.getCode(), OrderStatus.MAY_REFUND.getCode()).orderByDesc(Order.ID).last("limit 1"));
        if (order == null) {
            resp.setExist((byte) 0);
            return RespUtils.success(resp);
        }
        MyCampResp myCampResp = frontOrderService.myCampList(Collections.singletonList(order)).getData().getFirst();
        resp.setMyCampResp(myCampResp);
        WordReward wordReward = wordRewardMapper.selectOne(new QueryWrapper<WordReward>().select(
                WordReward.REWARD_NUM
        ).eq(WordReward.ORDER_ID, order.getId()));
        if (wordReward != null) {
            resp.setWordReward(wordReward.getRewardNum());
        } else {
            resp.setWordReward(0);
        }
        redisTemplate.opsForValue().set(String.format(RedisKey.FXY_TOKEN, accessToken), openId, 30, TimeUnit.DAYS);
        fxyWordManager.login(accessToken);
        resp.setExist((byte) 1);
        return RespUtils.success(resp);
    }

    public CommResp<StatisticResp> campInfo(Long orderId) {
        OrderStatistic orderRecord = orderStatisticMapper.selectOne(new QueryWrapper<OrderStatistic>().select(
                OrderStatistic.FXY_REWARD
        ).eq(OrderStatistic.ORDER_ID, orderId));
        if (orderRecord == null) {
            return RespUtils.warning(RespMetaEnum.PARAM_ERROR);
        }
        StatisticResp resp = new StatisticResp();
        resp.setFxyReward(orderRecord.getFxyReward());
        return RespUtils.success(resp);
    }

    public CommResp<List<FxyItemRet>> fxyList(Long userId, FxyListReq fxyListReq) {
        FxyListParam param = new FxyListParam();
        if (!StringUtils.hasLength(fxyListReq.getCode())
                && !StringUtils.hasLength(fxyListReq.getAccount())) {
            User user = userMapper.selectOne(new QueryWrapper<User>().select(User.ID).eq(User.ID, userId));
            param.setUid(user.getId());
        } else {
            param.setUid(null);
            param.setCode(fxyListReq.getCode());
            param.setAccount(fxyListReq.getAccount());
        }
        return RespUtils.success(fuyingMapper.queryFxyList(param));
    }

    public void saveFxy(Long userId, FxySaveReq fxySaveReq) {
        OrderFxy fxyOrder = orderFxyMapper.selectOne(new QueryWrapper<OrderFxy>()
                .select(OrderFxy.FXY_ID)
                .eq(OrderFxy.ORDER_ID, fxySaveReq.getOrderId()));
        if (fxyOrder != null) {
            throw new WebBaseException(4000, "已绑定扶小鹰，不能重复绑定");
        }
        Fxy fxy = fxyMapper.selectOne(new QueryWrapper<Fxy>().select(Fxy.ID).eq(Fxy.OPEN_ID, fxySaveReq.getOpenId()));
        if (fxy == null) {
            fxy = new Fxy();
        }
        fxy.setOpenId(fxySaveReq.getOpenId());
        FxyItemRet fxyItemRet = fuyingMapper.queryFxyByOpenId(fxySaveReq.getOpenId());
        fxy.setNickName(fxyItemRet.getNickname());
        fxy.setAccountInfo(fxyItemRet.getFxyAccount());
        fxy.setIconUrl(fxyItemRet.getPhotoUrl());
        TransactionStatus transactionStatus = TransactionUtils.open();
        try {
            fxyMapper.insertOrUpdate(fxy);
            fxyOrder = new OrderFxy();
            fxyOrder.setOrderId(fxySaveReq.getOrderId());
            fxyOrder.setFxyId(fxy.getId());
            orderFxyMapper.insert(fxyOrder);
            TransactionUtils.commit(transactionStatus);
        } catch (Exception e) {
            TransactionUtils.rollback(transactionStatus);
        }
    }

    public CommResp<?> unbindFxy(Long userId, Long orderId) {
        log.info("用户[{}]解绑扶小鹰", userId);
        OrderFxy fxyOrder = orderFxyMapper.selectOne(new QueryWrapper<OrderFxy>()
                .select(OrderFxy.ID)
                .eq(OrderFxy.ORDER_ID, orderId));
        if (fxyOrder == null) {
            return RespUtils.success();
        }
        orderFxyMapper.delete(new QueryWrapper<OrderFxy>().eq(OrderFxy.ID, fxyOrder.getId()));
        return RespUtils.success();
    }
}
