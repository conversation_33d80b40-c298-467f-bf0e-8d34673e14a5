package com.fuyingedu.training.front.controller;

import com.fuyingedu.training.common.model.CommResp;
import com.fuyingedu.training.common.util.RespUtils;
import com.fuyingedu.training.front.model.word.*;
import com.fuyingedu.training.front.service.FrontHawkService;
import com.fuyingedu.training.front.service.FrontWordService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 扶小鹰-单词训练营
 */
@RestController
@RequestMapping("fxy/english")
public class FxyEnglishController {

    @Autowired
    private FrontWordService frontWordService;

    /**
     * 今日任务
     */
    @GetMapping("today/task")
    public CommResp<TaskResp> todayTask(Long orderId) {
        return frontWordService.todayTask(orderId);
    }

    /**
     * 任务列表
     */
    @GetMapping("list")
    public CommResp<List<WeekResp>> list(Long orderId, HttpServletRequest request) {
        return frontWordService.getTaskList(orderId, request);
    }
}
