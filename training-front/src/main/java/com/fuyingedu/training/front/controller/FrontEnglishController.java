package com.fuyingedu.training.front.controller;

import com.fuyingedu.training.common.annotation.Login;
import com.fuyingedu.training.common.model.CommResp;
import com.fuyingedu.training.front.model.word.TaskResp;
import com.fuyingedu.training.front.model.word.WeekResp;
import com.fuyingedu.training.front.service.FrontEnglishService;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 单词训练营
 */
@RestController
@Slf4j
@RequestMapping("front/english")
public class FrontEnglishController {

    @Autowired
    private FrontEnglishService frontEnglishService;

    /**
     * 今日任务
     */
    @GetMapping("today/task")
    public CommResp<TaskResp> todayTask(@Login Long userId, Long orderId) {
        return frontEnglishService.todayTask(orderId);
    }

    /**
     * 任务列表
     */
    @GetMapping("list")
    public CommResp<List<WeekResp>> list(@Login Long userId, Long orderId, HttpServletRequest request) {
        return frontEnglishService.getTaskList(orderId, request);
    }

}
