package com.fuyingedu.training.front.controller;

import com.fuyingedu.training.common.annotation.Login;
import com.fuyingedu.training.common.model.CommResp;
import com.fuyingedu.training.common.util.RespUtils;
import com.fuyingedu.training.front.model.clazz.*;
import com.fuyingedu.training.front.service.FrontClazzService;
import com.fuyingedu.training.front.service.FrontGroupService;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 班级管理
 */
@RequestMapping("front/clazz")
@RestController
public class FrontClazzController {

    @Autowired
    private FrontClazzService frontClazzService;
    @Autowired
    private FrontGroupService frontGroupService;

    /**
     * 获取班级信息
     */
    @GetMapping("info")
    public CommResp<InfoResp> info(@RequestParam("orderId") Long orderId) throws WxErrorException {
        return frontClazzService.info(orderId);
    }

    /**
     * 更新小组名称
     * @param userId 前端不传
     */
    @PostMapping("group/update")
    public CommResp<?> groupUpdate(@Login Long userId, @RequestBody GroupInfoResp groupInfoResp) {
        frontClazzService.groupUpdate(userId, groupInfoResp);
        return RespUtils.success();
    }

    /**
     * 学习圈-作业列表
     */
    @GetMapping("homework/list")
    public CommResp<List<HomeworkItemResp>> homeworkList(HomeworkListReq homeworkListReq) {
        return frontClazzService.homeworkList(homeworkListReq);
    }

    /**
     * 学习圈-详情
     */
    @GetMapping("homework/detail")
    public CommResp<HomeworkItemResp> homeworkDetail(@RequestParam("myOrderId") Long myOrderId,
                                               @RequestParam("recordId") Long recordId) {
        return frontClazzService.homeworkDetail(myOrderId, recordId);
    }

    /**
     * 个人作业列表
     */
    @GetMapping("homework/my")
    public CommResp<List<HomeworkItemResp>> myHomeworkList(@RequestParam("orderId") Long orderId) {
        return frontClazzService.myHomeworkList(orderId);
    }

    /**
     * 点赞的用户列表
     */
    @GetMapping("like/list")
    public CommResp<List<LikeResp>> likeList(@RequestParam("recordId") Long recordId) {
        return frontClazzService.likeList(recordId);
    }

    /**
     * 小组列表
     */
    @GetMapping("group/list")
    public CommResp<List<GroupResp>> list(@RequestParam("clazzId") Long clazzId) {
        return frontGroupService.list(clazzId);
    }
}
