apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: fuying-training-admin-api
  name: fuying-training-admin-api
spec:
  selector:
    matchLabels:
      app: fuying-training-admin-api
  template:
    metadata:
      labels:
        app: fuying-training-admin-api
    spec:
      containers:
        - env:
            - name: TZ
              value: Asia/Shanghai
          image: ${IMAGE}
          imagePullPolicy: Always
          name: fuying-training-admin-api
          ports:
            - containerPort: 8088
              protocol: TCP