import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;

public class LocalDateTest {

    @Test
    public void testBetween() {
        LocalDate start = LocalDate.of(2021, 1, 1);
        LocalDate end = LocalDate.of(2021, 1, 31);
        System.out.println(start.until(end, ChronoUnit.DAYS));
    }

    @Test
    public void testBetween2() {
        BigDecimal bigDecimal = new BigDecimal("100.");
        System.out.println(bigDecimal.intValue());
    }
}
