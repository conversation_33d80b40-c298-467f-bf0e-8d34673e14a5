import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.fuyingedu.training.admin.AdminApplication;
import com.fuyingedu.training.entity.Clazz;
import com.fuyingedu.training.entity.Group;
import com.fuyingedu.training.entity.OperationLog;
import com.fuyingedu.training.entity.Order;
import com.fuyingedu.training.mapper.ClazzMapper;
import com.fuyingedu.training.mapper.GroupMapper;
import com.fuyingedu.training.mapper.OperationLogMapper;
import com.fuyingedu.training.mapper.OrderMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.ArrayList;
import java.util.List;

@SpringBootTest(classes = AdminApplication.class)
public class FlushTest {

    @Autowired
    private OperationLogMapper operationLogMapper;
    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private GroupMapper groupMapper;
    @Autowired
    private ClazzMapper clazzMapper;

    @Test
    public void testFlush() {
        String str = "906871,906870,906867,906863,906861,906866,906860,906859,888148,854513,854534,907172,906880,906879,856975,855048,856856,904789,872674,904730,855193,857084,855128,910863,910903,852248,854402,895780,906963,851541,856879,906219,857016,851896,906046,905789,854580,857066,851875,851899,910823,904764,905915,905796,905407,906010,905445,905957,856705,851473,852246,896032,909264,851797,855229,856611,854848,854768,851692,851539,907489,907456,907484,907498,889826,851489,857073,851423,913290,907111,905495,906012,856781,906034,855231,911469,911663,855049,911451,911656,898694,856663,907783,906118,855319,910760,906853,905429,910711,910719,904727,856668,867859,901482,905829,856822,905862,854707,852077,851839,852061,905503,905307,856600,903142,905907,905631,905853,905452,905982,852241,896907,852064,890629,855022,905715,905468,905966,851498,907200,907181,907170,907094,907137,907083,907076,907023,905455,857058,907043,906876,906875,906874,905088,906850,906848,906847,906038,906911,906924,906918,906905,906916,906844,906843,906838,906837,906836,906841,906840,907269,907263,907262,903053,906857,906856,906855,911272,851664,907196,851572,906073,854829,857075,910927,910918,851402,910909,910972,854817,905840,904767,855377,913106,905867,854632,904979,889809,897629,895112,904736,906098,855081,905513,905469,854556,855207,888520,909270,909265,909275,909237,910695,909253,898257,851424,855196,895046,907485,907418,855080,904726,905869,913642,912250,913326,857049,911716,911714,911643,911660,911471,911426,911421,852042,907197,907204,910811,907495,905443,905845,905421,856929,905991,851307,854765,856774,855372,887342,851680,906092,908615,856986,906204,905635,905423,889822,905500,851363,906269,905692,851990,905508,857079,906197,904731,906072,887516,907206,907177,907168,907082,906852,906941";
        String[] split = str.split(",");
        List<Long> vaildList = new ArrayList<>();
        for (String idStr : split) {
            Long orderId = Long.valueOf(idStr);
            List<OperationLog> logList = operationLogMapper.selectList(new LambdaQueryWrapper<OperationLog>()
                    .eq(OperationLog::getOrderId, orderId).orderByDesc(OperationLog::getId));
            Long groupId = null;
            for (OperationLog log : logList) {
                if (log.getOperationType() == 9 && log.getNextSchedule() == 786) {
                    groupId = log.getNextContent();
                    break;
                }
            }
            if (groupId != null) {
                Group group = groupMapper.selectOne(new LambdaQueryWrapper<Group>().eq(Group::getId, groupId));
                Clazz clazz = clazzMapper.selectOne(new LambdaQueryWrapper<Clazz>().eq(Clazz::getId, group.getClazzId()));
            orderMapper.update(new LambdaUpdateWrapper<Order>()
                    .set(Order::getGroupId, group.getId())
                            .set(Order::getClazzId, group.getClazzId())
                            .set(Order::getTeacherId, clazz.getTeacherId())
                            .set(Order::getScheduleId, 786)
                    .eq(Order::getId, orderId));
                System.out.println(group);
            } else {
                System.out.println("---" + orderId);
                vaildList.add(orderId);
            }
        }
        for (Long orderId : vaildList) {
            System.out.println(orderId);
        }
    }
}
