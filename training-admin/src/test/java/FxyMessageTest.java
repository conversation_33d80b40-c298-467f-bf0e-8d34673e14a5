import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.Producer;
import com.aliyun.openservices.ons.api.SendResult;
import com.fuyingedu.training.admin.AdminApplication;
import com.fuyingedu.training.admin.model.task.OutPunchReq;
import com.fuyingedu.training.admin.mq.RocketMessageListener;
import com.fuyingedu.training.common.util.JsonUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.nio.charset.StandardCharsets;

@SpringBootTest(classes = AdminApplication.class)
public class FxyMessageTest {

    @Autowired
    private RocketMessageListener rocketMessageListener;
    @Autowired
    private Producer rocketProducer;
    @Test
    public void test() {
//        OutPunchReq outPunchReq = new OutPunchReq();
//        outPunchReq.setStudentOpenId("e6ebf17f6d8a4598ad07a6837f3dbe4e");
//        outPunchReq.setEventClassify((byte) 1);
//        outPunchReq.setStudyDay("20240724");
//        fxyMessageListener.parseMessage(outPunchReq);

        Message message = new Message("training_camp", "pk_complete_event", "JsonUtils.formatObjToJson(outPunchReq)".getBytes(StandardCharsets.UTF_8));
        SendResult result = rocketProducer.send(message);
        System.out.println(result);
    }
}
