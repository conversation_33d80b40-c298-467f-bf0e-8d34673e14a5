import com.fuyingedu.training.admin.AdminApplication;
import com.fuyingedu.training.front.feign.FuyingCourseFeign;
import com.fuyingedu.training.front.model.feign.OrderItem;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;
import java.util.Map;

@SpringBootTest(classes = AdminApplication.class)
public class FeignTest {

    @Autowired
    private FuyingCourseFeign fuyingCourseFeign;

    @Test
    public void testCamp() {
        Map<String, Object> params = Map.of("courseScheduleId", 594, "nos", List.of("KCjuew202411121705wleton"));
//        String campItems = fuyingCourseFeign.sign(params);
        List<OrderItem> orderItems = fuyingCourseFeign.listCourseTrainee(594L, null);
        System.out.println();
    }
}
