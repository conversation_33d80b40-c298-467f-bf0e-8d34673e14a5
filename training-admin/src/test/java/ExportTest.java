import com.alibaba.excel.EasyExcel;
import com.fuyingedu.training.admin.model.order.ExportItemResp;
import com.fuyingedu.training.admin.model.order.OrderConvertor;
import com.fuyingedu.training.dto.order.OrderItemRet;
import org.junit.jupiter.api.Test;

import java.io.FileOutputStream;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 导出功能测试
 */
public class ExportTest {

    @Test
    public void testExportExcel() throws IOException {
        // 创建测试数据
        List<ExportItemResp> exportList = createTestData();
        
        // 导出到文件
        String fileName = "test_export.xlsx";
        try (FileOutputStream outputStream = new FileOutputStream(fileName)) {
            EasyExcel.write(outputStream, ExportItemResp.class)
                    .sheet("服务单列表")
                    .doWrite(exportList);
        }
        
        System.out.println("导出完成，文件名：" + fileName);
    }
    
    private List<ExportItemResp> createTestData() {
        List<ExportItemResp> list = new ArrayList<>();
        
        // 创建测试数据
        for (int i = 1; i <= 5; i++) {
            ExportItemResp item = new ExportItemResp();
            item.setOrderNo("FY" + String.format("%06d", i));
            item.setRealOrderNo("SRC" + String.format("%06d", i));
            item.setId((long) i);
            item.setPhoneNum("1380000000" + i);
            item.setCampName("测试课程" + i);
            item.setScheduleName("测试排期" + i);
            item.setTeacherName("测试导师" + i);
            item.setClazzName("测试班级" + i);
            item.setGroupName("第" + i + "组");
            item.setOrderStatus((byte) (i % 4 + 1)); // 1-4的状态
            item.setSignTime(LocalDateTime.now().minusDays(i));
            
            list.add(item);
        }
        
        return list;
    }
    
    @Test
    public void testOrderConvertor() {
        // 测试转换器
        OrderItemRet orderItem = new OrderItemRet();
        orderItem.setOrderNo("FY123456");
        orderItem.setRealOrderNo("SRC123456");
        orderItem.setPhoneNum(13800000001L);
        orderItem.setOrderStatus((byte) 1);
        orderItem.setSignTime(LocalDateTime.now());
        orderItem.setScheduleName("测试排期");
        
        ExportItemResp exportResp = OrderConvertor.toExportItemResp(orderItem);
        
        System.out.println("转换结果：" + exportResp);
    }
}
