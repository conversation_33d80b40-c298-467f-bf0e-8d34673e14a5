import com.aliyun.openservices.ons.api.*;
import com.fuyingedu.training.admin.AdminApplication;
import com.fuyingedu.training.admin.model.task.OutPunchReq;
import com.fuyingedu.training.admin.mq.RocketMessageListener;
import com.fuyingedu.training.common.util.JsonUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.nio.charset.StandardCharsets;
import java.util.Properties;

@SpringBootTest(classes = AdminApplication.class)
public class MqTest {

    @Test
    public void test() {
        Properties consumerProperties = new Properties();
        consumerProperties.setProperty(PropertyKeyConst.GROUP_ID, "training_camp_group");
        consumerProperties.setProperty(PropertyKeyConst.AccessKey, "******************************");
        consumerProperties.setProperty(PropertyKeyConst.SecretKey, "LTAI5tMgtTFAeEK1XEzzdoVD");
        consumerProperties.setProperty(PropertyKeyConst.NAMESRV_ADDR, "http://1314593173251732.mqrest.cn-qingdao-public.aliyuncs.com");
        consumerProperties.setProperty(PropertyKeyConst.INSTANCE_ID, "MQ_INST_1314593173251732_BXwMt4a5");
        //注意！！！如果访问阿里云RocketMQ 5.0系列实例，不要设置PropertyKeyConst.INSTANCE_ID，否则会导致收发失败
        Consumer consumer = ONSFactory.createConsumer(consumerProperties);
        consumer.subscribe("training_camp", "*", new RocketMessageListener());
        consumer.start();
        System.out.println("Consumer start success.");

        //等待固定时间防止进程退出
        try {
            Thread.sleep(200000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }


    @Autowired
    private Producer producer;
    @Test
    public void send() {
        OutPunchReq outPunchReq = new OutPunchReq();
        outPunchReq.setEventClassify((byte) 1);
        outPunchReq.setStudyDay("20240716");
        outPunchReq.setStudentOpenId("e6ebf17f6d8a4598ad07a6837f3dbe4e");
        Message message = new Message("training_camp", "complete_event_sync", "", JsonUtils.formatObjToJson(outPunchReq).getBytes(StandardCharsets.UTF_8));
        SendResult result = producer.send(message);
        System.out.println(result);
    }
}
