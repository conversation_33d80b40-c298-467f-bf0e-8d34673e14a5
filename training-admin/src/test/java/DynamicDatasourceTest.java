import com.fuyingedu.training.admin.AdminApplication;
import com.fuyingedu.training.common.util.DateUtils;
import com.fuyingedu.training.common.util.JwtUtils;
import com.fuyingedu.training.mapper.FuyingMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.util.DigestUtils;
import org.springframework.web.client.RestTemplate;

import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.Map;

@SpringBootTest(classes = AdminApplication.class)
public class DynamicDatasourceTest {

    @Autowired
    private FuyingMapper fuyingMapper;
    @Autowired
    private RestTemplate restTemplate;

    @Test
    public void test01() {
        String token = "e472bb266bdb4c57be512ad09fdef557";
        HttpHeaders headers = new HttpHeaders();
        long currTimestamp = System.currentTimeMillis();
        headers.set("Request-Time", currTimestamp + "");
        headers.set("Sign", DigestUtils.md5DigestAsHex(String.format("requestTime=%d&token=%s&key=46AB61D2CB4E190A", currTimestamp, token).getBytes(StandardCharsets.UTF_8)).toUpperCase());
        HttpEntity<Object> httpEntity = new HttpEntity<>(headers);
        String s = restTemplate.postForObject(String.format("https://pad.fuyingy.com/api/pad/trainingCamp/login?uid=%d&token=%s&tokenCheckFlag=false",
                1455769L, token), httpEntity, String.class);
        System.out.println(s);
    }

    @Test
    public void test02() {
        LocalDateTime expiredTime = LocalDateTime.now().plusYears(100);
        Map<String, Object> param = Map.of("userId", 96L, "expiredTime", DateUtils.toMillis(expiredTime));
        String token = JwtUtils.createToken(param, DateUtils.asDate(expiredTime));
        System.out.println(token);
    }
}
