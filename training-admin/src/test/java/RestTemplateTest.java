import com.fasterxml.jackson.databind.JsonNode;
import com.fuyingedu.training.admin.AdminApplication;
import com.fuyingedu.training.common.util.JsonUtils;
import com.fuyingedu.training.front.model.login.CodeDto;
import com.fuyingedu.training.front.model.login.LoginReq;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.util.UUID;

@SpringBootTest(classes = AdminApplication.class)
public class RestTemplateTest {

    @Autowired
    private RestTemplate restTemplate;

    @Test
    public void testPost() {
        LoginReq loginReq = new LoginReq();
        loginReq.setCode("aaa");
        loginReq.setType((byte) 1);
        String s = restTemplate.postForObject("https://class-dev.fuyingy.com/training-api/front/login", loginReq, String.class);
        System.out.println(s);
    }

    @Test
    public void test() {

        CodeDto codeDto = new CodeDto();
        codeDto.setTemplateCode("DXYZM0002");
//        codeDto.setTemplateParam(new CodeDto.TemplateParam(String.valueOf(100), 10));
        codeDto.setServiceCode("TRAINING_CAMP");
        codeDto.setTargetUser("100");
        codeDto.setNo(UUID.randomUUID().toString().replace("-", ""));
        String s = JsonUtils.formatObjToJson(codeDto);
        System.out.println();
    }

    @Test
    public void test11() {
        String url = "https://api.tianditu.gov.cn/geocoder?postStr=%7B%27lon%27%3A120.147015%2C%27lat%27%3A30.207963%2C%27ver%27%3A1%7D&type=geocode&tk=fb6e0e4cd29c3dc743130770558b107a";
        String forObject = restTemplate.getForObject(url, String.class);
        System.out.println(forObject);
    }

    @Value("${fxy.base-url}")
    private String baseUrl;

    @Test
    public void test3() {
        HttpHeaders headers = new HttpHeaders();
        headers.set("Access-Key", "3447CB6560E99791");
        MultiValueMap<String, Object> formData = new LinkedMultiValueMap<>();
        formData.add("trainingOrderIds", "10000,10001,10002");
        HttpEntity<Object> httpEntity = new HttpEntity<>(formData, headers);
        String url = "/traApi/admin/campStudy/wrongQuestionCensus";
        ResponseEntity<String> respEntity = restTemplate.exchange(baseUrl + url, HttpMethod.POST, httpEntity, String.class);
        JsonNode jsonNode = JsonUtils.parseJsonToJsonNode(respEntity.getBody());
    }
}
