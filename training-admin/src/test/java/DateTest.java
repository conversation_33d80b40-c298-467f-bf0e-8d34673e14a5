import com.fuyingedu.training.common.util.DateUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;

public class DateTest {

    public static void main(String[] args) {
        boolean after = LocalDateTime.now().isAfter(DateUtils.parseDateTime("2000-01-01 00:00:00"));
        boolean before = LocalDateTime.now().isBefore(DateUtils.parseDateTime("2000-01-01 00:00:00"));
        System.out.println(after + " " + before);
    }
}
