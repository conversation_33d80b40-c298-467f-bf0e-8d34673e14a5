import com.fuyingedu.training.admin.AdminApplication;
import com.fuyingedu.training.admin.manager.SyncOrderManager;
import com.fuyingedu.training.common.util.JsonUtils;
import com.fuyingedu.training.front.model.feign.OrderItem;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest(classes = AdminApplication.class)
public class OrderSyncTest {

    @Autowired
    SyncOrderManager syncOrderManager;

    @Test
    public void test() {
        OrderItem orderItem = JsonUtils.parseJsonToObj(" {\"id\":642479,\"courseId\":10,\"serviceType\":0,\"scheduleId\":953,\"scheduleClassId\":2199,\"traineeId\":163651,\"uid\":920662,\"no\":\"KC42xe202501140926ay0vk4\",\"status\":0,\"createTime\":\"2025-01-14 09:26:49\"}", OrderItem.class);
        syncOrderManager.syncOrder(orderItem);
    }
}
