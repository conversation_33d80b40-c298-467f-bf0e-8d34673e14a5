import com.fuyingedu.training.admin.AdminApplication;
import com.fuyingedu.training.common.util.DateUtils;
import com.fuyingedu.training.common.util.JwtUtils;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.time.LocalDateTime;
import java.util.Map;

public class GenerateTokenTest {

    @Test
    public void test() {
        Long userId = 755650L;
        LocalDateTime expiredTime = LocalDateTime.now().plusDays(1400);
        JwtUtils.init("aafjadljaljfaljaw0@)U$05ru20u)ur)qejjgoiporju)uajaup120LJFAPP240280U4101!#)u!)gqhiptaladsjaljfojnlanlawkfan");
        String token = JwtUtils.createToken(Map.of("userId", userId, "type", "Admin",
                "expiredTime", DateUtils.toMillis(expiredTime)), expiredTime);
        System.out.println(token);
    }

    @Test
    public void test1() {
        JwtUtils.init("aafjadljaljfaljaw0@)U$05ru20u)ur)qejjgoiporju)uajaup120LJFAPP240280U4101!#)u!)gqhiptaladsjaljfojnlanlawkfan");
        String adminToken = JwtUtils.createAdminToken(1121487);
        System.out.println(adminToken);
    }
}
