import com.fuyingedu.training.admin.AdminApplication;
import com.fuyingedu.training.admin.mq.resolver.FxyMessageResolver;
import com.fuyingedu.training.admin.service.LiveSignService;
import com.fuyingedu.training.front.feign.FuyingCourseFeign;
import com.fuyingedu.training.front.manager.BaiJiaYunManager;
import com.fuyingedu.training.front.model.feign.TraineeItem;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Arrays;
import java.util.Set;

@SpringBootTest(classes = AdminApplication.class)
public class BaiJiaYunTest {


    @Autowired
    private BaiJiaYunManager baiJiaYunManager;

    @Test
    public void testRoomList() {
//        baiJiaYunManager.getRoomList();
//        baiJiaYunManager.questionList(24121097713594L, 2360061L);
//        baiJiaYunManager.createGroup(24121097713594L, Arrays.asList("测试1", "测试2"));
//        baiJiaYunManager.updateGroupName(24121080869026L, 1L, "测试002");
//        baiJiaYunManager.getGroupList(24121793193825L);
//        baiJiaYunManager.playbackList(24121097713594L);
//        baiJiaYunManager.checkinInfo(24121097713594L);
//        baiJiaYunManager.roomQuiz(24121097713594L);
//        baiJiaYunManager.getGroupList(25011540837488L);
//        baiJiaYunManager.deleteRoom(25011536651585L);
        baiJiaYunManager.deleteGroup(Set.of(1211L));
//        String s = baiJiaYunManager.setCallback("https://class-dev.fuyingy.com/training-api/admin/bjy/callback");
//        System.out.println(s);
    }

    @Test
    public void createGroup() {
        baiJiaYunManager.createGroup();
    }

    @Autowired
    private FxyMessageResolver fxyMessageResolver;

    @Test
    public void testMessage() {
        String msg = "{\"day\":10,\"eventClassify\":4,\"studentId\":7948,\"studentOpenId\":\"d37fd03cbd954b7fa3d2e58f7ed9d3c3\",\"studyDay\":\"20250205\",\"trainingOrderId\":852481,\"uid\":816715}";
        fxyMessageResolver.parseMessage(msg);
    }
    @Autowired
    private FuyingCourseFeign fuyingCourseFeign;

    @Test
    public void test() {
        TraineeItem trainee = fuyingCourseFeign.getTrainee(43694L);
        TraineeItem trainee1 = fuyingCourseFeign.getTrainee(40109L);
        System.out.println(trainee);
    }

    @Autowired
    private LiveSignService liveSignService;
    @Test
    public void testSync() {
        liveSignService.syncReward(783L, 204L);
    }
}
