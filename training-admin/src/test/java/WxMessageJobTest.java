import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaUniformMessage;
import com.fuyingedu.training.admin.AdminApplication;
import com.fuyingedu.training.admin.job.WxMessageJob;
import com.fuyingedu.training.common.util.JsonUtils;
import com.fuyingedu.training.front.feign.FuyingUserFeign;
import com.fuyingedu.training.front.manager.WxMaManager;
import com.fuyingedu.training.front.model.feign.UnionItem;
import com.fuyingedu.training.front.model.feign.UserItem;
import com.fuyingedu.training.front.model.feign.UserReq;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.template.WxMpTemplateData;
import me.chanjar.weixin.mp.bean.template.WxMpTemplateMessage;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@SpringBootTest(classes = AdminApplication.class)
public class WxMessageJobTest {

    @Autowired
    private WxMessageJob wxMessageJob;

    @Test
    public void liveMessageJob() {
        wxMessageJob.liveMessageJob();
    }

    @Autowired
    private WxMaManager wxMaManager;

    @Test
    public void testRemarkMessage() {
        wxMaManager.remarkMessage(9L, 101L);
    }

    @Autowired
    private WxMpService wxMaService;
    @Autowired
    private FuyingUserFeign fuyingUserFeign;

    @Test
    public void testSendUnionMessage() throws WxErrorException {
        UserReq userReq = new UserReq();
        userReq.setPhone("18668043237");
        userReq.setCountryCode("86");
        UserItem userItem = fuyingUserFeign.getUserByPhone(userReq);
        String openIdList = fuyingUserFeign.getOpenIdList(Map.of("typeCode", "ABM_WECHAT_GZH_OPEN_ID", "uidList",
                Collections.singletonList(userItem.getUid())));
        WxMpTemplateMessage message = WxMpTemplateMessage.builder()
                .templateId("wkK4rZStjdXTeeMn02xB_wagKacEqPQGCzIb04dh00w")
                .toUser(JsonUtils.parseJsonToList(openIdList, String.class).get(0))
                .clientMsgId(UUID.randomUUID().toString()).build();
        message.addData(new WxMpTemplateData("first", "测试", ""));
        message.addData(new WxMpTemplateData("keyword1", "测试1", ""));
        message.addData(new WxMpTemplateData("keyword2", "测试2", ""));
        message.addData(new WxMpTemplateData("remark", "测试remark", ""));
        wxMaService.getTemplateMsgService().sendTemplateMsg(message);
    }
}
