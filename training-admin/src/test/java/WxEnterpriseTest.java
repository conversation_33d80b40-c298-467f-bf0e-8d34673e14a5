import com.fuyingedu.training.admin.AdminApplication;
import com.fuyingedu.training.admin.service.WxEnterpriseService;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.cp.api.WxCpService;
import me.chanjar.weixin.cp.bean.external.WxCpContactWayResult;
import me.chanjar.weixin.cp.bean.external.contact.WxCpExternalContactBatchInfo;
import me.chanjar.weixin.cp.bean.external.contact.WxCpExternalContactInfo;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Collections;
import java.util.List;
import java.util.Set;

@SpringBootTest(classes = AdminApplication.class)
public class WxEnterpriseTest {

    @Autowired
    private WxEnterpriseService wxEnterpriseService;
    @Autowired
    private WxCpService wxCpService;

    @Test
    public void testGetAccessToken() throws WxErrorException {
    }

    /**
     * YeShuangWen
     * FengBo
     */
    @Test
    public void testGetUserId() throws WxErrorException {
    }

    @Test
    public void testContactList() throws WxErrorException {
    }

    @Test
    public void testGetContactInfo() {
    }

    @Test
    public void testBatchContactInfo() {
    }

    @Test
    public void testGetContactQrCode() throws WxErrorException {
    }

    @Test
    public void testGroupList() throws WxErrorException {
    }

    @Test
    public void testContactList1() throws WxErrorException {
        List<String> strings = wxCpService.getExternalContactService().listFollowers();
        for (String str : strings) {
            System.out.println();
        }
    }
}
