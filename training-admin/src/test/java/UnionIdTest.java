import com.fuyingedu.training.admin.AdminApplication;
import com.fuyingedu.training.front.feign.FuyingUserFeign;
import com.fuyingedu.training.front.model.feign.UnionItem;
import com.fuyingedu.training.mapper.UserMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest(classes = AdminApplication.class)
public class UnionIdTest {

    @Autowired
    private FuyingUserFeign fuyingUserFeign;
    @Autowired
    private UserMapper userMapper;

    @Test
    public void testUnionId() {
        UnionItem unionId = fuyingUserFeign.getUnionId(808590L);
        System.out.println(unionId.getThirdAuthId());
    }

}
