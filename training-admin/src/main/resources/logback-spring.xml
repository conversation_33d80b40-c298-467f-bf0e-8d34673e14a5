<?xml version="1.0" encoding="UTF-8"?>
<!-- 日志级别从低到高分为TRACE < DEBUG < INFO < WARN < ERROR < FATAL，如果设置为WARN，
    则低于WARN的信息都不会输出 -->
<!-- scan:当此属性设置为true时，配置文件如果发生改变，将会被重新加载，默认值为true -->
<!-- scanPeriod:设置监测配置文件是否有修改的时间间隔，如果没有给出时间单位，默认单位是毫秒。
    当scan为true时，此属性生效。默认的时间间隔为1分钟。 -->
<!-- debug:当此属性设置为true时，将打印出logback内部日志信息，实时查看logback运行状态。默认值为false。-->
<configuration scan="true" scanPeriod="10 seconds">

    <contextName>logback</contextName>

    <property name="server.name" value="training"/>
    <!-- 定义变量：name的值是变量的名称，value的值时变量定义的值。通过定义的值会被插入到logger上下文中。
        定义变量后，可以使“${}”来使用变量。 -->
    <property name="log.path" value="logs" />
    <!-- 日志格式 -->
    <property name="ENCODER_PATTERN" value="%d{HH:mm:ss.SSS} %-5level %logger{6} - %msg%n"/>

    <!-- 彩色日志 -->
    <!-- 彩色日志依赖的渲染类 -->
    <conversionRule conversionWord="clr" converterClass="org.springframework.boot.logging.logback.ColorConverter" />
    <conversionRule conversionWord="wex" converterClass="org.springframework.boot.logging.logback.WhitespaceThrowableProxyConverter" />
    <conversionRule conversionWord="wEx" converterClass="org.springframework.boot.logging.logback.ExtendedWhitespaceThrowableProxyConverter" />

    <!--输出到控制台-->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>${ENCODER_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <!--
        root节点是必选节点，用来指定最基础的日志输出级别。
        level:用来设置打印级别，大小写无关：TRACE, DEBUG, INFO, WARN, ERROR, ALL 和 OFF，
        不能设置为INHERITED或者同义词NULL。默认是DEBUG
        可以包含零个或多个元素，标识这个appender将会添加到这个logger。
    -->
    <springProfile name="dev">
        <root level="INFO">
            <appender-ref ref="CONSOLE"/>
        </root>
        <!--过滤掉一些无用的DEBUG信息 -->
        <logger name="org.springframework" level="info"/>
        <logger name="RocketmqClient" level="warn"/>
        <logger name="RocketmqRemoting" level="warn"/>
        <logger name="com.fuyingedu.training.mapper" level="debug"/>
    </springProfile>

    <springProfile name="test">
        <root level="INFO">
            <appender-ref ref="CONSOLE"/>
        </root>
        <logger name="RocketmqClient" level="warn"/>
        <logger name="RocketmqRemoting" level="warn"/>
    </springProfile>

    <springProfile name="prod">
        <root level="INFO">
            <appender-ref ref="CONSOLE"/>
        </root>
        <logger name="RocketmqClient" level="warn"/>
        <logger name="RocketmqRemoting" level="warn"/>
    </springProfile>
</configuration>
