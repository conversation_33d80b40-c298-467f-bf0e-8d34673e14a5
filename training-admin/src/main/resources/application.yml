# 配置服务器端口
server:
  port: 8088
  servlet:
    context-path: /training-api

spring:
  profiles:
    active: dev
  application:
    name: training-admin
  cache:
    type: caffeine
    caffeine:
      spec: initialCapacity=1000,maximumSize=10000,expireAfterWrite=1s,recordStats

mybatis-plus:
  configuration:
    #开启驼峰命名自动映射
    map-underscore-to-camel-case: true
  #扫描mapper文件
  mapper-locations: classpath:mapper/*.xml
