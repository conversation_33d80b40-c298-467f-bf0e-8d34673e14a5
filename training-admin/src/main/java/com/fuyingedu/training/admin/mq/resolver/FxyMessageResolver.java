package com.fuyingedu.training.admin.mq.resolver;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.fuyingedu.training.admin.model.task.OutPunchReq;
import com.fuyingedu.training.common.enums.RewardLogType;
import com.fuyingedu.training.common.enums.TaskType;
import com.fuyingedu.training.common.util.DateUtils;
import com.fuyingedu.training.common.util.JsonUtils;
import com.fuyingedu.training.common.util.TransactionUtils;
import com.fuyingedu.training.entity.*;
import com.fuyingedu.training.front.manager.RewardManager;
import com.fuyingedu.training.front.model.word.TaskItem;
import com.fuyingedu.training.front.service.FrontTaskStatisticService;
import com.fuyingedu.training.front.service.FrontWordService;
import com.fuyingedu.training.mapper.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Component("complete_event_sync")
@Slf4j
public class FxyMessageResolver implements MessageResolver {


    @Autowired
    private FxyMapper fxyMapper;
    @Autowired
    private OrderFxyMapper orderFxyMapper;
    @Autowired
    private TaskMapper taskMapper;
    @Autowired
    private TaskSubmitRecordMapper taskSubmitRecordMapper;
    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private OrderFxyRewardLogMapper orderFxyRewardLogMapper;
    @Autowired
    private FrontTaskStatisticService frontTaskStatisticService;
    @Autowired
    private FrontWordService frontWordService;
    @Autowired
    private OrderStatisticMapper orderStatisticMapper;
    @Autowired
    private RewardManager rewardManager;

    @Override
    public void parseMessage(String messageBody) {
        OutPunchReq message = JsonUtils.parseJsonToObj(messageBody, OutPunchReq.class);
        if (Byte.valueOf((byte) 4).equals(message.getEventClassify())) {
            log.info("单词训练营任务{}", messageBody);
            wordTask(message);
        } else {
            boolean needLog = otherTask(message);
            if (needLog) {
                log.info("训练营任务{}", messageBody);
            }
        }
    }

    private void wordTask(OutPunchReq message) {
        if (message.getTrainingOrderId() == null) {
            log.info("trainingOrderId为空，无法完成打卡任务");
            return;
        }
        Order order = orderMapper.selectOne(new QueryWrapper<Order>().select(
                Order.ID, Order.SCHEDULE_ID, Order.CLAZZ_ID, Order.USER_ID
        ).eq(Order.ID, message.getTrainingOrderId()));
        if (order == null) {
            log.info("trainingOrderId=[{}]未找到对应订单", message.getTrainingOrderId());
            return;
        }
        if (order.getClazzId() == null) {
            log.info("trainingOrderId=[{}]未找到对应班级", message.getTrainingOrderId());
            return;
        }
        Task task = taskMapper.selectOne(new QueryWrapper<Task>().select(
                Task.ID, Task.TASK_REWARD, Task.FXY_REWARD, Task.WORD_REWARD, Task.TASK_NAME
        ).eq(Task.SCHEDULE_ID, order.getScheduleId()).eq(Task.TASK_TYPE, TaskType.WORD.getCode()).last("limit 1"));
        if (task == null) {
            log.info("排期[{}]未找到对应任务", order.getScheduleId());
            return;
        }
        List<TaskItem> taskItems = frontWordService.taskList(order.getScheduleId());
        TaskItem taskItem = taskItems.stream().filter(t -> t.getDays().equals(message.getDay())).findFirst().orElseThrow();
        LocalDate recordDate = taskItem.getRealDate();
//        if (recordDate == null) {
//            recordDate = DateUtils.parseDate(message.getStudyDay(), DateUtils.DATE_NONE_FORMATTER);
//        }
        TaskSubmitRecord record = taskSubmitRecordMapper.selectOne(new QueryWrapper<TaskSubmitRecord>().select(
                        TaskSubmitRecord.ID, TaskSubmitRecord.TASK_ID
                ).in(TaskSubmitRecord.ORDER_ID, order.getId()).eq(TaskSubmitRecord.TASK_ID, task.getId())
                .eq(TaskSubmitRecord.SUBMIT_DATE, recordDate));
        if (record != null) {
            log.info("[{}]已打卡，无需重复打卡", record.getId());
            return;
        }
        TaskSubmitRecord newRecord = new TaskSubmitRecord();
        newRecord.setOrderId(order.getId());
        newRecord.setClazzId(order.getClazzId());
        newRecord.setTaskId(task.getId());
        newRecord.setTaskType(TaskType.WORD.getCode());
        newRecord.setSubmitDate(recordDate);
        newRecord.setUserId(order.getUserId());
        int insert = taskSubmitRecordMapper.insert(newRecord);
        if (insert > 0 && recordDate.isEqual(LocalDate.now())) {
            frontTaskStatisticService.addRecord(order.getClazzId(), task.getId());
            rewardManager.saveReward(newRecord.getOrderId(), order.getUserId(), order.getUserId(),
                    task.getTaskReward(), task.getFxyReward(), message.getClassScore(),
                    String.format("完成'%s'-day%d", task.getTaskName(), taskItem.getDays()));
        }
    }

    private boolean otherTask(OutPunchReq message) {
        Fxy fxy = fxyMapper.selectOne(new QueryWrapper<Fxy>().select(
                Fxy.ID
        ).eq(Fxy.OPEN_ID, message.getStudentOpenId()));
        if (fxy == null) {
            return false;
        }
        List<Long> orderIds = orderFxyMapper.selectList(new QueryWrapper<OrderFxy>().select(
                OrderFxy.ORDER_ID
        ).eq(OrderFxy.FXY_ID, fxy.getId())).stream().map(OrderFxy::getOrderId).toList();
        if (orderIds.isEmpty()) {
            log.info("openID=[{}]未报名任何课程", message.getStudentOpenId());
            return true;
        }
        LocalDate recordDate = DateUtils.parseDate(message.getStudyDay(), DateUtils.DATE_NONE_FORMATTER);
        List<Order> orderList = orderMapper.selectList(new QueryWrapper<Order>().select(
                Order.ID, Order.SCHEDULE_ID, Order.CLAZZ_ID
        ).in(Order.ID, orderIds));
        Map<Long, Order> orderMap = orderList.stream().collect(Collectors.toMap(Order::getId, o -> o));
        List<Long> scheduleIds = orderList.stream().map(Order::getScheduleId).distinct().toList();
        if (scheduleIds.isEmpty()) {
            log.info("openID=[{}]查不到对应的服务单", message.getStudentOpenId());
            return true;
        }
        List<Task> taskList = taskMapper.selectList(new QueryWrapper<Task>().select(
                                Task.ID, Task.FXY_REWARD, Task.SCHEDULE_ID, Task.UPLOAD_ITEMS, Task.TASK_TYPE
                        ).in(Task.SCHEDULE_ID, scheduleIds)
                        .eq(Task.TASK_TYPE, TaskType.OUT_PUNCH.getCode())
                        .le(Task.START_DATE, recordDate).ge(Task.END_DATE, recordDate))
                .stream().filter(task -> JsonUtils.parseJsonToList(task.getUploadItems(), Byte.class).contains(message.getEventClassify())).toList();
        if (taskList.isEmpty()) {
            log.info("openID=[{}]没有需要完成的任务", message.getStudentOpenId());
            return true;
        }
        Set<Long> taskIds = taskSubmitRecordMapper.selectList(new QueryWrapper<TaskSubmitRecord>().select(
                                TaskSubmitRecord.TASK_ID
                        ).in(TaskSubmitRecord.ORDER_ID, orderIds).in(TaskSubmitRecord.TASK_ID, taskList.stream().map(Task::getId).toList())
                        .eq(TaskSubmitRecord.SUBMIT_DATE, recordDate))
                .stream().map(TaskSubmitRecord::getTaskId).collect(Collectors.toSet());
        Map<Long, List<Task>> scheduleTaskMap = taskList.stream().filter(task -> !taskIds.contains(task.getId()))
                .collect(Collectors.groupingBy(Task::getScheduleId));
        if (scheduleTaskMap.isEmpty()) {
            log.info("openID=[{}]今天已经完成了所有任务", message.getStudentOpenId());
            return true;
        }
        List<TaskSubmitRecord> recordList = new ArrayList<>(taskList.size());
        for (Map.Entry<Long, Order> entry : orderMap.entrySet()) {
            Order order = entry.getValue();
            List<Task> scheduleTaskList = scheduleTaskMap.get(order.getScheduleId());
            if (scheduleTaskList == null) {
                continue;
            }
            for (Task task : scheduleTaskList) {
                TaskSubmitRecord record = new TaskSubmitRecord();
                record.setOrderId(order.getId());
                record.setClazzId(order.getClazzId());
                record.setTaskId(task.getId());
                record.setTaskType(task.getTaskType());
                record.setSubmitDate(recordDate);
                recordList.add(record);
                rewardManager.saveFxyReward(order.getId(), -1L, order.getUserId(), task.getFxyReward(), String.format("完成任务[%d]", task.getId()));
            }
        }
        if (!recordList.isEmpty()) {
            TransactionUtils.execute(() -> {
                taskSubmitRecordMapper.insert(recordList);
                for (TaskSubmitRecord record : recordList) {
                    frontTaskStatisticService.addRecord(record.getClazzId(), record.getTaskId());
                }
            });
        }
        return true;
    }
}
