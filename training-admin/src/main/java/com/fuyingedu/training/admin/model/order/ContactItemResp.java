package com.fuyingedu.training.admin.model.order;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fuyingedu.training.admin.excel.ConfirmStatusConverter;
import com.fuyingedu.training.admin.excel.OrderStatusConverter;
import com.fuyingedu.training.admin.excel.WxStatusConverter;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;

@Getter
@Setter
@ToString
public class ContactItemResp {

    private Long id;
    /**
     * 用户名
     */
    @ExcelProperty("用户名")
    private String realName;

    @ExcelProperty("用户备注")
    private String orderRemark;
    /**
     * 用户手机号
     */
    @ExcelProperty("用户手机号")
    private Long phoneNum;
    /**
     * 学员名称
     */
    @ExcelProperty("学员名称")
    private String studentName;

    /**
     * 用户头像
     */
    @ExcelIgnore
    private String userIcon;
    /**
     * 学员证件号
     */
    @ExcelProperty("学员证件号")
    private String cartNo;

    /**
     * 1-正常 2-已关闭 3-已退款
     */
    @ExcelProperty(value = "订单状态", converter = OrderStatusConverter.class)
    private Byte orderStatus;

    /**
     * 助教老师
     */
    @ExcelProperty("助教老师")
    private String assistantName;

    /**
     * 是否加导师微信 1-没有加 2-已加
     */
    @ExcelProperty(value = "是否加导师微信", converter = WxStatusConverter.class)
    private Byte teacherWxStatus;

    /**
     * 是否加助教微信 1-没有加 2-已加
     */
    @ExcelProperty(value = "是否加助教微信", converter = WxStatusConverter.class)
    private Byte assistantWxStatus;

    /**
     * 是否加群微信 1-没有加 2-已加
     */
    @ExcelProperty(value = "是否加群微信", converter = WxStatusConverter.class)
    private Byte groupWxStatus;

    /**
     * 确认情况 1-未确认 2-自己确认 3-助教确认 4-不来
     */
    @ExcelProperty(value = "确认情况", converter = ConfirmStatusConverter.class)
    private Byte confirmStatus;

    @ExcelIgnore
    private Long clazzId;
    @ExcelProperty("班级名称")
    private String clazzName;

    @ExcelIgnore
    private Long groupId;
    @ExcelProperty("小组名称")
    private String groupName;

    /**
     * 核销状态 1 - 未核销 2 - 已核销
     */
    @ExcelIgnore
    private Byte signStatus;
    /**
     * 核销时间
     */
    @ExcelIgnore
    private LocalDateTime signTime;

    /**
     * 扶小鹰绑定状态 1-未绑定 2-已绑定
     */
    @ExcelIgnore
    private Byte bindStatus;

    /**
     * 扶小鹰账号信息
     */
    @ExcelProperty("扶小鹰账号信息")
    private String fxyAccountInfo;

    /**
     * 扶小鹰用户名
     */
    @ExcelProperty("扶小鹰用户名")
    private String fxyNickName;
}
