package com.fuyingedu.training.admin.model.order;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Getter
@Setter
@ToString
public class AddRewardReq {

    /**
     * 班级id
     */
    @NotNull
    private Long clazzId;

    /**
     * 分数
     */
    @NotNull
    private Integer reward;

    /**
     * 备注
     */
    @NotEmpty
    private String remark;

    /**
     * 服务单列表ID
     */
    @NotEmpty
    private List<Long> orderIdList;

    /**
     * 类型：1-小红花 2-小太阳 3-word鹰
     */
    @NotNull
    private Integer type;
}
