package com.fuyingedu.training.admin.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fuyingedu.training.admin.model.login.LoginReq;
import com.fuyingedu.training.admin.model.login.LoginResp;
import com.fuyingedu.training.common.enums.RespMetaEnum;
import com.fuyingedu.training.common.exception.WebBaseException;
import com.fuyingedu.training.common.model.CommResp;
import com.fuyingedu.training.common.util.JwtUtils;
import com.fuyingedu.training.common.util.RespUtils;
import com.fuyingedu.training.entity.Role;
import com.fuyingedu.training.entity.User;
import com.fuyingedu.training.entity.UserRole;
import com.fuyingedu.training.mapper.FuyingMapper;
import com.fuyingedu.training.mapper.RoleMapper;
import com.fuyingedu.training.mapper.UserMapper;
import com.fuyingedu.training.mapper.UserRoleMapper;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.bean.WxOAuth2UserInfo;
import me.chanjar.weixin.common.bean.oauth2.WxOAuth2AccessToken;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.common.service.WxOAuth2Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;

@Service
@Slf4j
public class LoginService {

    @Autowired
    private WxOAuth2Service wxOAuth2Service;
    @Autowired
    private UserMapper userMapper;
    @Autowired
    private UserRoleMapper userRoleMapper;
    @Autowired
    private RoleMapper roleMapper;

    @Value("${spring.profiles.active}")
    private String profiles;

    public CommResp<LoginResp> login(LoginReq loginReq) {
        WxOAuth2AccessToken accessToken;
        try {
            accessToken = wxOAuth2Service.getAccessToken(loginReq.getCode());
        } catch (WxErrorException e) {
            throw new WebBaseException(500, "微信服务异常", e);
        }
        String unionId = accessToken.getUnionId();
        User user = userMapper.selectOne(new QueryWrapper<User>().select(
                User.ID,
                User.NICK_NAME,
                User.REAL_NAME,
                User.USER_ICON
        ).eq(User.UNION_ID, unionId));
        if (user == null) {
            throw new WebBaseException(RespMetaEnum.NO_USER);
        }
        if (!StringUtils.hasLength(user.getRealName()) || !StringUtils.hasLength(user.getNickName()) || !StringUtils.hasLength(user.getUserIcon())) {
            WxOAuth2UserInfo userInfo;
            try {
                userInfo = wxOAuth2Service.getUserInfo(accessToken, "zh_CN");
            } catch (WxErrorException e) {
                throw new WebBaseException(500, "微信服务异常", e);
            }
            if (!StringUtils.hasLength(user.getRealName())) {
                user.setRealName(userInfo.getNickname());
            }
            if (!StringUtils.hasLength(user.getNickName())) {
                user.setNickName(userInfo.getNickname());
            }
            if (!StringUtils.hasLength(user.getUserIcon())) {
                user.setUserIcon(userInfo.getHeadImgUrl());
            }
            userMapper.insertOrUpdate(user);
        }
        LoginResp loginResp = getLoginResp(user);
        return RespUtils.success(loginResp);
    }

    public CommResp<LoginResp> loginForTest(Long phoneNum) {
        if ("prod".equals(profiles)) {
            throw new WebBaseException(4000, "请使用测试环境");
        }
        User user = userMapper.selectOne(new QueryWrapper<User>().select(
                User.ID,
                User.NICK_NAME,
                User.REAL_NAME,
                User.USER_ICON
        ).eq(User.PHONE_NUM, phoneNum));
        return RespUtils.success(getLoginResp(user));
    }

    private LoginResp getLoginResp(User user) {
        LoginResp loginResp = toLoginResp(user);
        loginResp.setToken(JwtUtils.createAdminToken(user.getId()));
        List<UserRole> roles = userRoleMapper.selectList(new QueryWrapper<UserRole>().select(
                UserRole.ID, UserRole.ROLE_ID
        ).eq(UserRole.USER_ID, user.getId()));
        if (!CollectionUtils.isEmpty(roles)) {
            List<Role> roleList = roleMapper.selectList(new QueryWrapper<Role>().select(
                    Role.ROLE_NAME,
                    Role.ROLE_DESC
            ).in(Role.ID, roles.stream().map(UserRole::getRoleId).toList()));
            loginResp.setRoleList(roleList.stream().map(role -> {
                LoginResp.Role roleResp = new LoginResp.Role();
                roleResp.setRole(role.getRoleName());
                roleResp.setDesc(role.getRoleDesc());
                return roleResp;
            }).toList());
        }
        return loginResp;
    }

    private LoginResp toLoginResp(User user) {
        LoginResp loginResp = new LoginResp();
        loginResp.setId(user.getId());
        loginResp.setUserIcon(user.getUserIcon());
        loginResp.setRealName(user.getRealName());
        return loginResp;
    }
}
