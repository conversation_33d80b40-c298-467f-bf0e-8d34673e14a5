package com.fuyingedu.training.admin.model.bjy;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
public class SignResp {

    /**
     * 签到开关 1 关闭 2 开启
     */
    private byte signStatus = 1;

    /**
     * 任务积分
     */
    private Integer taskReward;

    /**
     * 扶小鹰小太阳
     */
    private Integer fxyReward;

    /**
     * Word鹰积分值
     */
    private Integer wordReward;
}
