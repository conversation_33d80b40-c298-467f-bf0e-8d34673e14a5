package com.fuyingedu.training.admin.job;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fuyingedu.training.common.enums.TaskType;
import com.fuyingedu.training.common.model.CommResp;
import com.fuyingedu.training.common.util.AsyncUtils;
import com.fuyingedu.training.common.util.RespUtils;
import com.fuyingedu.training.entity.Live;
import com.fuyingedu.training.entity.Task;
import com.fuyingedu.training.front.manager.WxMaManager;
import com.fuyingedu.training.mapper.LiveMapper;
import com.fuyingedu.training.mapper.TaskMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("job")
public class WxMessageJob {

    @Autowired
    private TaskMapper taskMapper;
    @Autowired
    private WxMaManager wxMaManager;
    @Autowired
    private LiveMapper liveMapper;

    /**
     * 打卡任务开始的30分钟发送消息通知
     */
    @GetMapping("message/punch")
    public CommResp<?> punchMessageJob() {
        log.info("打卡消息发送定时任务开始");
        List<Task> taskList = taskMapper.selectList(new QueryWrapper<Task>().select(
                Task.ID, Task.TASK_NAME, Task.START_DATE, Task.END_DATE, Task.SCHEDULE_ID,
                Task.TASK_TYPE, Task.CLAZZ_TYPE, Task.START_TIME, Task.END_TIME, Task.TASK_LEVEL
        ).eq(Task.TASK_TYPE, TaskType.PUNCH.getCode()).le(Task.START_DATE, LocalDate.now()).ge(Task.END_DATE, LocalDate.now())
                .lt(Task.START_TIME, LocalTime.now()).gt(Task.START_TIME, LocalTime.now().minusMinutes(30)));
        if (taskList.isEmpty()) {
            return RespUtils.success();
        }
        log.info("打卡消息发送任务开始，任务数量：{}", taskList.size());
        AsyncUtils.execute(() -> {
            for (Task task : taskList) {
                log.info("打卡消息发送任务开始，任务ID：{}", task.getId());
                wxMaManager.punchMessage(task);
            }
        }, "打卡消息发送");
        return RespUtils.success();
    }

    /**
     * 直播开始30分钟之前
     */
    @GetMapping("message/live")
    public CommResp<?> liveMessageJob() {
        log.info("直播消息发送定时任务开始");
        List<Live> liveList = liveMapper.selectList(new QueryWrapper<Live>().select(
                Live.ID, Live.LIVE_NAME, Live.START_TIME, Live.LIVE_DURATION, Live.SCHEDULE_ID
        ).lt(Live.START_TIME, LocalDateTime.now().plusMinutes(30)).gt(Live.START_TIME, LocalDateTime.now()));
        if (liveList.isEmpty()) {
            return RespUtils.success();
        }
        log.info("直播消息发送任务开始，任务数量：{}", liveList.size());
        AsyncUtils.execute(() -> {
            for (Live live : liveList) {
                log.info("直播消息发送任务开始，任务ID：{}", live.getId());
                wxMaManager.liveMessage(live);
            }
        }, "直播消息发送");
        return RespUtils.success();
    }
}
