package com.fuyingedu.training.admin.model.bjy;

import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
public class GroupItemResp {

    private Long groupId;

    /**
     * 小教室id
     */
    private Long roomId;

    /**
     * 小教室名称
     */
    private String roomName;

    /**
     * 关联导师
     */
    private String teacherName;

    private Long clazzId;
    /**
     * 关联班级
     */
    private String clazzName;

    /**
     * 学员数量
     */
    private Integer orderNum;

    /**
     * 导师观看地址
     */
    private String teacherUrl;

    /**
     * 导师入口
     */
    private String teacherCode;

    /**
     * 助教观看地址
     */
    private String assistantUrl;

    /**
     * 助教入口
     */
    private String assistantCode;

    /**
     * 学生观看地址
     */
    private String studentUrl;

    /**
     * 学生入口
     */
    private String studentCode;
    /**
     * 数据新建时间
     */
    private LocalDateTime createdTime;
}
