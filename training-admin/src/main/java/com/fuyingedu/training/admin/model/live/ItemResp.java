package com.fuyingedu.training.admin.model.live;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString(callSuper = true)
public class ItemResp extends Item {

    /**
     * 直播方式 1-公司直播 2-导师直播
     * 字典Key：LIVE_TYPE
     */
    private Byte liveType;

    /**
     * 直播端口 1-腾讯会议 2-保利威 3-百家云
     * 字典Key：LIVE_PORT
     */
    private Byte livePort;
    /**
     * 排期表主键
     */
    private Long scheduleId;

    /**
     * 直播间信息
     */
    private String liveInfo;
    /**
     * 直播密码
     */
    private String livePassword;
    /**
     * 回放链接
     */
    private String repeatUrl;

}
