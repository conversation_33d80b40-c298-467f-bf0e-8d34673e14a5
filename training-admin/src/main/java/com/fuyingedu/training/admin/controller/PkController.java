package com.fuyingedu.training.admin.controller;

import com.fuyingedu.training.admin.model.pk.SaveReq;
import com.fuyingedu.training.admin.service.PkService;
import com.fuyingedu.training.common.annotation.Login;
import com.fuyingedu.training.common.annotation.PreAuthorize;
import com.fuyingedu.training.common.model.CommResp;
import com.fuyingedu.training.admin.model.pk.ItemResp;
import com.fuyingedu.training.front.model.stats.GroupResp;
import com.fuyingedu.training.front.model.stats.RewardResp;
import com.fuyingedu.training.front.service.FrontPkService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 单词训练营PK配置
 */
@RestController
@RequestMapping("admin/pk")
public class PkController {

    @Autowired
    private PkService pkService;
    @Autowired
    private FrontPkService frontPkService;

    /**
     * 添加PK
     */
    @PreAuthorize({"管理员", "普通管理员"})
    @PostMapping("save")
    public CommResp<?> save(@Login Long userId, @Valid @RequestBody SaveReq req) {
        return pkService.save(userId, req);
    }

    /**
     * 删除PK
     */
    @PreAuthorize({"管理员", "普通管理员"})
    @PostMapping("delete")
    public CommResp<?> delete(@RequestParam Long id) {
        return pkService.delete(id);
    }
    
    /**
     * PK历史列表
     * @param scheduleId 排期ID
     */
    @GetMapping("list")
    public CommResp<List<ItemResp>> list(@RequestParam Long scheduleId) {
        return pkService.list(scheduleId);
    }

    /**
     * 个人榜
     */
    @GetMapping("reward")
    public CommResp<List<RewardResp>> reward(@RequestParam Long clazzId,
                                             @RequestParam(required = false) Long pkId) {
        return frontPkService.rewardByPkId(clazzId, pkId);
    }

    /**
     * 小组榜
     */
    @GetMapping("group")
    public CommResp<List<GroupResp>> group(@RequestParam Long clazzId,
                                           @RequestParam(required = false) Long pkId) {
        return frontPkService.groupByPkId(clazzId, pkId);
    }
}
