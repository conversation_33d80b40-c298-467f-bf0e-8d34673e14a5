package com.fuyingedu.training.admin.model.teacher;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.List;

@Getter
@Setter
@ToString
public class ScheduleDetailResp {

    /**
     * 课程id
     */
    private Long campId;

    /**
     * 课程名称
     */
    private String campName;

    /**
     * 排期名称
     */
    private String scheduleName;

    /**
     * 排期id
     */
    private Long scheduleId;
    /**
     * 排期开启时间
     */
    private LocalDateTime startTime;

    /**
     * 排期截止时间
     */
    private LocalDateTime endTime;

    /**
     * 讲师类型 1-辅导老师 2-助教
     */
    private Byte teacherType;
    /**
     * 1-普通 2-扶小鹰陪跑 3-单词训练
     */
    private Byte campType;
    /**
     * 辅导老师要求
     */
    private String campContent;

}
