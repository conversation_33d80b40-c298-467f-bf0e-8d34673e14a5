package com.fuyingedu.training.admin.controller;

import com.fuyingedu.training.admin.model.order.log.*;
import com.fuyingedu.training.admin.service.OrderLogService;
import com.fuyingedu.training.common.annotation.Login;
import com.fuyingedu.training.common.model.CommResp;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 服务单日志
 */
@RestController
@RequestMapping("/admin/orderLog")
@Slf4j
public class OrderLogController {

    @Autowired
    private OrderLogService orderLogService;

    /**
     * 服务单日志列表
     */
    @GetMapping("list")
    public CommResp<List<ItemResp>> list(@RequestParam Long orderId) {
        return orderLogService.list(orderId);
    }

    /**
     * 减少列表
     */
    @GetMapping("sub/list")
    public CommResp<List<SubResp>> subList(
            @Login Long userId,
            @Valid SubReq req
    ) {
        return orderLogService.subList(userId, req);
    }

    /**
     * 增加列表
     */
    @GetMapping("add/list")
    public CommResp<List<AddResp>> addList(
            @Login Long userId,
            @RequestParam  Long scheduleId
    ) {
        return orderLogService.addList(userId, scheduleId);
    }

    /**
     * 增加的详情列表
     */
    @GetMapping("add/detail/list")
    public CommResp<List<SubResp>> addDetailList(
            @Login Long userId,
            DetailReq req
    ) {
        return orderLogService.addDetailList(userId, req);
    }

    /**
     * 重新添加
     */
    @PostMapping("repeat")
    public CommResp<?> repeat(
            @Login Long userId,
            @RequestParam("id") Long id
    ) {
        return orderLogService.repeat(userId, id);
    }
}
