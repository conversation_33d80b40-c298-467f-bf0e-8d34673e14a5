package com.fuyingedu.training.admin.controller;

import com.fuyingedu.training.admin.model.medal.*;
import com.fuyingedu.training.admin.service.MedalRecordService;
import com.fuyingedu.training.admin.service.MedalService;
import com.fuyingedu.training.common.annotation.Login;
import com.fuyingedu.training.common.annotation.PreAuthorize;
import com.fuyingedu.training.common.annotation.UserInfo;
import com.fuyingedu.training.common.model.CommResp;
import com.fuyingedu.training.common.util.RespUtils;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 奖状管理
 */
@RestController
@RequestMapping("admin/medal")
@Slf4j
public class MedalController {

    @Autowired
    private MedalService medalService;
    @Autowired
    private MedalRecordService medalRecordService;

    /**
     * 奖状列表-管理后台
     */
    @PreAuthorize({"管理员", "普通管理员"})
    @GetMapping("list")
    public CommResp<List<ItemResp>> list(@Login UserInfo userInfo, ListReq listReq) {
        return medalService.list(userInfo, listReq);
    }

    /**
     * 奖状新增或编辑-管理后台
     * @param saveReq 奖状新增或编辑请求
     */
    @PreAuthorize({"管理员", "普通管理员"})
    @PostMapping("save")
    public CommResp<?> addOrEdit(@RequestBody @Valid SaveReq saveReq) {
        medalService.addOrEdit(saveReq);
        return RespUtils.success();
    }

    /**
     * 奖状列表-导师后台
     */
    @GetMapping("teacher/list")
    public CommResp<List<ItemResp>> listByTeacher(@Login Long userId, @RequestParam("scheduleId") Long scheduleId) {
        return medalService.listByTeacher(userId, scheduleId);
    }

    /**
     * 奖状详情
     * @param id 奖状id
     */
    @GetMapping("detail")
    public CommResp<DetailResp> detail(@RequestParam("id") Long id) {
        return medalService.detail(id);
    }

    /**
     * 奖状新增或编辑-导师后台
     */
    @PostMapping("teacher/save")
    public CommResp<?> addOrEditByTeacher(@Login Long userId, @RequestBody @Valid SaveReq saveReq) {
        medalService.addOrEditByTeacher(userId, saveReq);
        return RespUtils.success();
    }

    /**
     * 给学员添加奖状
     */
    @PostMapping("add")
    public CommResp<?> add(@Login Long userId, @RequestBody @Valid AddReq addReq) {
        medalService.add(userId, addReq);
        return RespUtils.success();
    }

    /**
     * 奖状颁发记录列表
     */
    @GetMapping("record/list")
    public CommResp<List<RecordItemResp>> recordList(@Valid RecordReq recordReq) {
        return medalRecordService.recordList(recordReq);
    }
}
