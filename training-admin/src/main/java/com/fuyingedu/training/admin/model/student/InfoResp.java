package com.fuyingedu.training.admin.model.student;

import lombok.*;

import java.time.LocalDateTime;
import java.util.List;

@Getter
@Setter
@ToString
public class InfoResp {

    private Long id;

    /**
     * 用户头像
     */
    private String userIcon;

    /**
     * 姓名
     */
    private String realName;

    /**
     * 任务积分
     */
    private Integer taskReward;
    /**
     * 单词训练营分数
     */
    private Integer wordReward;
    /**
     * 优秀推荐次数
     */
    private Integer recommendNum;

    /**
     * 小班名称
     */
    private String className;
    /**
     * 小组名称
     */
    private String groupName;
    /**
     * 1-普通学员 2-陪跑志愿者
     */
    private Byte studentType;

    /**
     * 扶小鹰昵称
     */
    private String fxyNickName;

    /**
     * 扶小鹰账号信息
     */
    private String fxyAccountInfo;

    /**
     * 任务类型列表
     */
    private List<TaskType> taskTypeList;
    /**
     * 家长列表
     */
    private List<User> userList;

    /**
     * 标签列表
     */
    private List<Label> labelList;

    /**
     * 奖状列表
     */
    private List<Medal> medalList;

    /**
     * 任务类型
     */
    @Getter
    @Setter
    @AllArgsConstructor
    @NoArgsConstructor
    public static class TaskType {
        private Byte type;
        private String desc;
    }

    @Getter
    @Setter
    @AllArgsConstructor
    @NoArgsConstructor
    public static class User {

        /**
         * 用户ID
         */
        private Long userId;

        /**
         * 用户头像
         */
        private String userIcon;

        /**
         * 姓名
         */
        private String realName;

        /**
         * 手机号
         */
        private Long phoneNum;

        /**
         * 1 - 主账号 2 - 辅助账号
         */
        private Byte accountType;
    }

    @Getter
    @Setter
    public static class Medal {
        /**
         * 奖状名称
         */
        private String medalName;
        /**
         * 模板名称
         */
        private String templateName;

        /**
         * 奖状图片
         */
        private String medalIcon;
        /**
         * 姓名定位
         */
        private String medalContent;

        /**
         * 获取奖状时间
         */
        private LocalDateTime medalTime;
        /**
         * 获奖姓名
         */
        private String realName;

        /**
         * 颁奖老师
         */
        private String teacherName;
        /**
         * 讲师类型 1-辅导老师 2-助教
         */
        private Byte teacherType;
    }

    @Getter
    @Setter
    public static class Label {
        /**
         * 徽章名称
         */
        private String labelName;

        /**
         * 徽章图片
         */
        private String labelIcon;

        /**
         * 标签类型 1-表扬 2-待改进
         */
        private Byte labelType;

        /**
         * 徽章数量
         */
        private Integer labelNum;

        private List<LabelTeacher> teacherList;
     }

     @Getter
    @Setter
    public static class LabelTeacher {
        /**
         * 颁布老师
         */
        private String teacherName;
         /**
          * 讲师类型 1-辅导老师 2-助教
          */
         private Byte teacherType;
         /**
          * 徽章数量
          */
        private Integer labelNum;
     }
}
