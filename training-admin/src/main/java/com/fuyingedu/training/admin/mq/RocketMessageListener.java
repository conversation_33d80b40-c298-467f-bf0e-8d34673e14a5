package com.fuyingedu.training.admin.mq;

import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.MessageListener;
import com.fuyingedu.training.admin.mq.resolver.MessageResolver;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

@Slf4j
@Component
public class RocketMessageListener implements MessageListener {

    @Autowired
    private ApplicationContext applicationContext;

    @Override
    public Action consume(Message message, ConsumeContext consumeContext) {
        String messageBody = new String(message.getBody(), StandardCharsets.UTF_8);
        String tag = message.getTag();
        try {
            MessageResolver messageResolver = applicationContext.getBean(tag, MessageResolver.class);
            messageResolver.parseMessage(messageBody);
            return Action.CommitMessage;
        } catch (Exception e) {
            log.warn("处理消息失败", e);
        }
        return Action.ReconsumeLater;
    }

}
