package com.fuyingedu.training.admin.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.fuyingedu.training.admin.model.grade.*;
import com.fuyingedu.training.common.enums.*;
import com.fuyingedu.training.common.exception.WebBaseException;
import com.fuyingedu.training.common.model.CommResp;
import com.fuyingedu.training.common.util.RespUtils;
import com.fuyingedu.training.entity.*;
import com.fuyingedu.training.mapper.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class GradeService {

    @Autowired
    private ClazzMapper clazzMapper;
    @Autowired
    private TeacherMapper teacherMapper;
    @Autowired
    private ScheduleTeacherMapper scheduleTeacherMapper;
    @Autowired
    private ScheduleMapper scheduleMapper;
    @Autowired
    private UserMapper userMapper;
    @Autowired
    private OrderMapper orderMapper;

    public CommResp<List<TeacherResp>> teacherList(TeacherReq teacherReq) {
        Map<Long, ScheduleTeacher> teacherMap = scheduleTeacherMapper.selectList(new QueryWrapper<ScheduleTeacher>().select(
                ScheduleTeacher.ID,
                ScheduleTeacher.TEACHER_ID,
                ScheduleTeacher.TEACHER_TYPE,
                ScheduleTeacher.GRADE_NUM,
                ScheduleTeacher.GRADE_WEIGHT
        ).eq(ScheduleTeacher.SCHEDULE_ID, teacherReq.getScheduleId())
                        .eq(teacherReq.getTeacherType() != null, ScheduleTeacher.TEACHER_TYPE, teacherReq.getTeacherType()))
                .stream().collect(Collectors.toMap(ScheduleTeacher::getTeacherId, scheduleTeacher -> scheduleTeacher));
        if (teacherMap.isEmpty()) {
            return RespUtils.success(Collections.emptyList());
        }
        List<Teacher> teacherList = teacherMapper.selectList(new QueryWrapper<Teacher>().select(
                Teacher.ID,
                Teacher.REAL_NAME,
                Teacher.USER_ID
        ).in(Teacher.ID, teacherMap.keySet()));
        List<Long> uidList = teacherList.stream().map(Teacher::getUserId).toList();
        Map<Long, Long> userMap = userMapper.selectList(new QueryWrapper<User>().select(
                User.ID, User.PHONE_NUM
        ).in(User.ID, uidList)).stream().collect(Collectors.toMap(User::getId, User::getPhoneNum));
        List<TeacherResp> respList = new ArrayList<>(teacherList.size());
        for (Teacher teacher : teacherList) {
            TeacherResp resp = new TeacherResp();
            ScheduleTeacher scheduleTeacher = teacherMap.get(teacher.getId());
            resp.setId(scheduleTeacher.getId());
            resp.setRealName(teacher.getRealName());
            resp.setPhoneNum(userMap.get(teacher.getUserId()));
            resp.setTeacherType(scheduleTeacher.getTeacherType());
            resp.setMaxNum(scheduleTeacher.getGradeNum());
            resp.setGradeWeight(scheduleTeacher.getGradeWeight());
            respList.add(resp);
        }
        respList = respList.stream().sorted(Comparator.comparing(TeacherResp::getGradeWeight).reversed()).toList();
        return RespUtils.success(respList);
    }

    @Transactional(rollbackFor = Exception.class)
    public void addOrEdit(SaveReq saveReq) {
        List<Teacher> tacherList = teacherMapper.selectList(new QueryWrapper<Teacher>().select(
                Teacher.ID
        ).in(Teacher.ID, saveReq.getTeacherIdList()).eq(Teacher.TEACHER_STATUS, Status.NORMAL.getCode()));
        if (tacherList.size() != saveReq.getTeacherIdList().size()) {
            throw new WebBaseException(RespMetaEnum.PARAM_ERROR);
        }
        List<ScheduleTeacher> scheduleTeacherList = scheduleTeacherMapper.selectList(new QueryWrapper<ScheduleTeacher>().select(
                ScheduleTeacher.ID
        ).eq(ScheduleTeacher.SCHEDULE_ID, saveReq.getScheduleId()).in(ScheduleTeacher.TEACHER_ID, saveReq.getTeacherIdList()));
        if (!scheduleTeacherList.isEmpty()) {
            throw new WebBaseException(RespMetaEnum.PARAM_ERROR);
        }
        int gradeNum = 0;
        if (TeacherType.TEACHER.getCode().equals(saveReq.getTeacherType())) {
            scheduleMapper.update(new UpdateWrapper<Schedule>().setSql(String.format("%s = %s + %d", Schedule.GRADE_NUM, Schedule.GRADE_NUM, saveReq.getTeacherIdList().size()))
                    .eq(Schedule.ID, saveReq.getScheduleId()));
            gradeNum = 20;
        } else if (TeacherType.ASSISTANT.getCode().equals(saveReq.getTeacherType())) {
            scheduleMapper.update(new UpdateWrapper<Schedule>().setSql(String.format("%s = %s + %d", Schedule.CLASS_NUM, Schedule.CLASS_NUM, saveReq.getTeacherIdList().size()))
                    .eq(Schedule.ID, saveReq.getScheduleId()));
        }
        int num = gradeNum;
        scheduleTeacherMapper.insert(saveReq.getTeacherIdList()
                .stream().map(teacherId -> {
                    ScheduleTeacher teacher = new ScheduleTeacher();
                    teacher.setScheduleId(saveReq.getScheduleId());
                    teacher.setTeacherId(teacherId);
                    teacher.setTeacherType(saveReq.getTeacherType());
                    teacher.setGradeNum(num);
                    return teacher;
                }).toList());
    }

    public void update(UpdateReq updateReq) {
        ScheduleTeacher scheduleTeacher = scheduleTeacherMapper.selectOne(new QueryWrapper<ScheduleTeacher>().select(
                ScheduleTeacher.ID, ScheduleTeacher.TEACHER_TYPE
        ).eq(ScheduleTeacher.ID, updateReq.getId()));
        if (scheduleTeacher != null && TeacherType.TEACHER.getCode().equals(scheduleTeacher.getTeacherType())) {
            scheduleTeacherMapper.update(new UpdateWrapper<ScheduleTeacher>()
                    .set(updateReq.getMaxNum() != null, ScheduleTeacher.GRADE_NUM, updateReq.getMaxNum())
                    .set(updateReq.getGradeWeight() != null, ScheduleTeacher.GRADE_WEIGHT, updateReq.getGradeWeight())
                    .eq(ScheduleTeacher.ID, updateReq.getId())
            );
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateType(UpdateTypeReq updateTypeReq) {
        ScheduleTeacher scheduleTeacher = scheduleTeacherMapper.selectOne(new QueryWrapper<ScheduleTeacher>().select(
                ScheduleTeacher.SCHEDULE_ID, ScheduleTeacher.TEACHER_TYPE
        ).eq(ScheduleTeacher.ID, updateTypeReq.getId()));
        if (scheduleTeacher == null) {
            throw new WebBaseException(RespMetaEnum.PARAM_ERROR);
        }
        if (scheduleTeacher.getTeacherType().equals(updateTypeReq.getTeacherType())) {
            return;
        }
        Order order = null;
        if (TeacherType.TEACHER.getCode().equals(scheduleTeacher.getTeacherType())) {
            order = orderMapper.selectOne(new QueryWrapper<Order>().select(
                    Order.ID
            ).eq(Order.SCHEDULE_ID, scheduleTeacher.getScheduleId()).eq(Order.TEACHER_ID, updateTypeReq.getTeacherId()).last("limit 1"));
        } else {
            Set<Long> clazzIds = clazzMapper.selectList(new QueryWrapper<Clazz>().select(
                    Clazz.ID
            ).eq(Clazz.SCHEDULE_ID, scheduleTeacher.getScheduleId()).eq(Clazz.ASSISTANT_ID, updateTypeReq.getTeacherId()))
                    .stream().map(Clazz::getId).collect(Collectors.toSet());
            if (!clazzIds.isEmpty()) {
                order = orderMapper.selectOne(new QueryWrapper<Order>().select(
                        Order.ID).in(Order.CLAZZ_ID, clazzIds).last("limit 1"));
            }
        }
        if (order != null) {
            throw new WebBaseException(4000, "该老师已分配学员，无法修改");
        }
        scheduleTeacherMapper.update(new UpdateWrapper<ScheduleTeacher>()
                .set(ScheduleTeacher.TEACHER_TYPE, updateTypeReq.getTeacherType())
                .eq(ScheduleTeacher.ID, updateTypeReq.getId()));
    }

    public void delete(DeleteReq deleteReq) {
        ScheduleTeacher scheduleTeacher = scheduleTeacherMapper.selectOne(new QueryWrapper<ScheduleTeacher>().select(
                ScheduleTeacher.SCHEDULE_ID,
                ScheduleTeacher.TEACHER_TYPE
        ).eq(ScheduleTeacher.ID, deleteReq.getTeacherId()));
        if (scheduleTeacher == null) {
            return;
        }
        int count = scheduleTeacherMapper.deleteById(deleteReq.getTeacherId());
        if (count > 0) {
            if (TeacherType.TEACHER.getCode().equals(scheduleTeacher.getTeacherType())) {
                scheduleMapper.update(new UpdateWrapper<Schedule>().setSql(String.format("%s = %s - 1", Schedule.GRADE_NUM, Schedule.GRADE_NUM))
                        .eq(Schedule.ID, scheduleTeacher.getScheduleId()));
            } else if (TeacherType.ASSISTANT.getCode().equals(scheduleTeacher.getTeacherType())) {
                scheduleMapper.update(new UpdateWrapper<Schedule>().setSql(String.format("%s = %s - 1", Schedule.CLASS_NUM, Schedule.CLASS_NUM))
                        .eq(Schedule.ID, scheduleTeacher.getScheduleId()));
            }
        }
    }

    public CommResp<ClazzInfoResp> clazzInfo(Long userId, Long scheduleId) {
        Teacher teacher = teacherMapper.selectOne(new QueryWrapper<Teacher>().select(
                Teacher.ID
        ).eq(Teacher.USER_ID, userId));
        if (teacher == null) {
            throw new WebBaseException(RespMetaEnum.PARAM_ERROR);
        }
        ClazzInfoResp resp = new ClazzInfoResp();
        List<Clazz> clazzList = clazzMapper.selectList(new QueryWrapper<Clazz>().select(
                Clazz.ID
        ).eq(Clazz.SCHEDULE_ID, scheduleId).eq(Clazz.TEACHER_ID, teacher.getId()));
        resp.setClazzNum(clazzList.size());
        List<Order> orderList = orderMapper.selectList(new QueryWrapper<Order>().select(
                Order.ID, Order.CLAZZ_ID, Order.STUDENT_FLAG).eq(Order.SCHEDULE_ID, scheduleId)
                .eq(Order.TEACHER_ID, teacher.getId()).in(Order.ORDER_STATUS, OrderStatus.NORMAL.getCode(), OrderStatus.MAY_REFUND.getCode())
        );
        resp.setAllocNum((int) orderList.stream().filter(order -> order.getClazzId() != null).count());
        resp.setUnAllocNum((int) orderList.stream().filter(order -> order.getClazzId() == null).count());
        resp.setNewNum((int) orderList.stream().filter(order ->
                order.getClazzId() == null && StudentFlag.NEW.getCode().equals(order.getStudentFlag())).count());
        resp.setOldNum((int) orderList.stream().filter(order ->
                order.getClazzId() == null && StudentFlag.OLD.getCode().equals(order.getStudentFlag())).count());
        return RespUtils.success(resp);
    }
}
