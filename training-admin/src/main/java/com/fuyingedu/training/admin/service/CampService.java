package com.fuyingedu.training.admin.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fuyingedu.training.admin.model.camp.DetailResp;
import com.fuyingedu.training.admin.model.camp.ItemResp;
import com.fuyingedu.training.admin.model.camp.ListReq;
import com.fuyingedu.training.admin.model.camp.SaveReq;
import com.fuyingedu.training.common.annotation.UserInfo;
import com.fuyingedu.training.common.enums.RespMetaEnum;
import com.fuyingedu.training.common.exception.WebBaseException;
import com.fuyingedu.training.common.model.CommResp;
import com.fuyingedu.training.common.util.RespUtils;
import com.fuyingedu.training.entity.AvailableCamp;
import com.fuyingedu.training.entity.Camp;
import com.fuyingedu.training.entity.Schedule;
import com.fuyingedu.training.front.feign.FuyingCourseFeign;
import com.fuyingedu.training.front.model.feign.CampItem;
import com.fuyingedu.training.front.model.media.MediaConvertor;
import com.fuyingedu.training.mapper.AvailableCampMapper;
import com.fuyingedu.training.mapper.CampMapper;
import com.fuyingedu.training.mapper.ScheduleMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CampService {

    @Autowired
    private CampMapper campMapper;
    @Autowired
    private FuyingCourseFeign fuyingCourseFeign;
    @Autowired
    private ScheduleMapper scheduleMapper;
    @Autowired
    private AvailableCampMapper availableCampMapper;

    public void addOrEdit(SaveReq saveReq) {
        Camp saveCamp = saveReqToCamp(saveReq);
        Camp camp = campMapper.selectOne(new QueryWrapper<Camp>()
                .select(Camp.ID, Camp.CAMP_TYPE)
                .eq(Camp.ID, saveReq.getId())
        );
        if (camp != null) {
            if (!camp.getCampType().equals(saveCamp.getCampType())) {
                Schedule schedule = scheduleMapper.selectOne(new QueryWrapper<Schedule>().select(Schedule.ID)
                        .eq(Schedule.CAMP_ID, saveReq.getId()).last("limit 1"));
                if (schedule != null) {
                    throw new WebBaseException(4000, "排期已存在，不能修改训练营类型");
                }
            }
            campMapper.updateById(saveCamp);
        } else {
            campMapper.insert(saveCamp);
        }
    }

    private Camp saveReqToCamp(SaveReq saveReq) {
        Camp camp = new Camp();
        camp.setId(saveReq.getId());
        camp.setCampName(saveReq.getCampName());
        camp.setCampType(saveReq.getCampType());
        camp.setNeedBinding(saveReq.getNeedBinding());
        camp.setNeedFxy(saveReq.getNeedFxy());
        camp.setWxPriority(saveReq.getWxPriority());
        camp.setCampContent(saveReq.getCampContent());
        camp.setRelationName(saveReq.getRelationName());
        camp.setMainMediaUrl(MediaConvertor.getUrlSuffix(saveReq.getMainMediaUrl()));
        camp.setSignDays(saveReq.getSignDays());
        return camp;
    }

    public CommResp<List<ItemResp>> list(UserInfo userInfo, ListReq listReq) {
        Long userId = userInfo.getUserId();
        List<Long> campIds = null;
        if (!userInfo.getRoleIds().contains(2L)) {
            campIds = availableCampMapper.selectList(new QueryWrapper<AvailableCamp>().select(AvailableCamp.CAMP_ID)
                    .eq(AvailableCamp.AVAILABLE_ID, userId).eq(AvailableCamp.AVAILABLE_TYPE, 4).eq(AvailableCamp.CAMP_STATUS, 1))
                    .stream().map(AvailableCamp::getCampId).toList();
            if (campIds.isEmpty()) {
                return RespUtils.warning(RespMetaEnum.NO_AUTH);
            }
        }
        IPage<Camp> page = new Page<>(listReq.getPageNum(), listReq.getPageSize());
        page = campMapper.selectPage(page, new QueryWrapper<Camp>().select(
                                Camp.ID, Camp.CAMP_NAME, Camp.CAMP_TYPE, Camp.NEED_BINDING, Camp.NEED_FXY,
                                Camp.WX_PRIORITY, Camp.SCHEDULE_NUM, Camp.MAIN_MEDIA_URL, Camp.RELATION_NAME
                        ).like(StringUtils.hasLength(listReq.getCampName()), Camp.CAMP_NAME, listReq.getCampName())
                        .in(campIds != null, Camp.ID, campIds)
                        .orderByDesc(Camp.ID)
        );
        List<ItemResp> respList = page.getRecords().stream().map(this::toItemResp).toList();
        return RespUtils.success(page.getCurrent(), page.getSize(), page.getTotal(), respList);
    }

    public CommResp<DetailResp> detail(Long campId) {
        Camp camp = campMapper.selectOne(new QueryWrapper<Camp>()
                .select(
                        Camp.ID,
                        Camp.CAMP_NAME,
                        Camp.CAMP_TYPE,
                        Camp.NEED_BINDING,
                        Camp.NEED_FXY,
                        Camp.WX_PRIORITY,
                        Camp.CAMP_CONTENT,
                        Camp.MAIN_MEDIA_URL, Camp.SIGN_DAYS, Camp.RELATION_NAME
                )
                .eq(Camp.ID, campId)
        );
        DetailResp detailResp = toDetailResp(camp);
        detailResp.setMainMediaUrl(MediaConvertor.getMediaUrl(camp.getMainMediaUrl()));
        detailResp.setRelationName(camp.getRelationName());
        detailResp.setSignDays(camp.getSignDays());
        return RespUtils.success(detailResp);
    }

    private DetailResp toDetailResp(Camp camp) {
        DetailResp detailResp = new DetailResp();
        detailResp.setId(camp.getId());
        detailResp.setCampName(camp.getCampName());
        detailResp.setCampType(camp.getCampType());
        detailResp.setNeedBinding(camp.getNeedBinding());
        detailResp.setNeedFxy(camp.getNeedFxy());
        detailResp.setWxPriority(camp.getWxPriority());
        detailResp.setCampContent(camp.getCampContent());
        return detailResp;
    }

    private ItemResp toItemResp(Camp camp) {
        ItemResp itemResp = new ItemResp();
        itemResp.setId(camp.getId());
        itemResp.setRelationName(camp.getRelationName());
        itemResp.setCampName(camp.getCampName());
        itemResp.setCampType(camp.getCampType());
        itemResp.setNeedBinding(camp.getNeedBinding());
        itemResp.setNeedFxy(camp.getNeedFxy());
        itemResp.setWxPriority(camp.getWxPriority());
        itemResp.setScheduleNum(camp.getScheduleNum());
        itemResp.setMainMediaUrl(MediaConvertor.getMediaUrl(camp.getMainMediaUrl()));
        return itemResp;
    }

    public CommResp<List<String>> fyCampNameList() {
        return RespUtils.success(new ArrayList<>(fyCampList().getData().stream().map(CampItem::getTitle).collect(Collectors.toSet())));
    }

    public CommResp<List<CampItem>> fyCampList() {
        List<CampItem> campList = fuyingCourseFeign.listCamp();
        Set<Long> pidSet = campMapper.selectList(new QueryWrapper<Camp>().select(Camp.ID))
                .stream().map(Camp::getId).collect(Collectors.toSet());
        return RespUtils.success(campList.stream().filter(campRet -> !pidSet.contains(campRet.getId())).toList());
    }
}
