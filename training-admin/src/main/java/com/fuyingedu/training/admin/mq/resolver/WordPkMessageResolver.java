package com.fuyingedu.training.admin.mq.resolver;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.fuyingedu.training.admin.model.task.PkReq;
import com.fuyingedu.training.common.util.DateUtils;
import com.fuyingedu.training.common.util.JsonUtils;
import com.fuyingedu.training.entity.WordPkConfig;
import com.fuyingedu.training.entity.WordPkRecord;
import com.fuyingedu.training.mapper.WordPkConfigMapper;
import com.fuyingedu.training.mapper.WordPkRecordMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component("pk_complete_event")
@Slf4j
public class WordPkMessageResolver implements MessageResolver {

    @Autowired
    private WordPkRecordMapper wordPkRecordMapper;
    @Autowired
    private WordPkConfigMapper wordPkConfigMapper;

    @Override
    public void parseMessage(String messageBody) {
        log.info("PK任务收到消息: {}", messageBody);
        PkReq message = JsonUtils.parseJsonToObj(messageBody, PkReq.class);
        WordPkConfig wordPk = wordPkConfigMapper.selectOne(new QueryWrapper<WordPkConfig>().select(
                WordPkConfig.ID, WordPkConfig.STOP_TIME, WordPkConfig.START_TIME
        ).eq(WordPkConfig.ID, message.getPkId()));
        if (wordPk == null) {
            log.warn("PK未空[{}]", message.getPkId());
            return;
        }
        if (wordPk.getStopTime() != null && DateUtils.toMillis(wordPk.getStopTime()) < message.getTimeStamp()) {
            log.warn("PK已经结束了[{}]-[{}]-[{}]", message.getPkId(), message.getTrainingOrderId(), message.getTimeStamp());
            return;
        }
        WordPkRecord pkRecord = wordPkRecordMapper.selectOne(new QueryWrapper<WordPkRecord>().select(
                WordPkRecord.PK_ID, WordPkRecord.ORDER_ID
        ).eq(WordPkRecord.PK_ID, message.getPkId()).eq(WordPkRecord.ORDER_ID, message.getTrainingOrderId()));
        if (pkRecord == null) {
            pkRecord = new WordPkRecord();
            pkRecord.setPkId(message.getPkId());
            pkRecord.setOrderId(message.getTrainingOrderId());
            pkRecord.setWinningNum(1);
            wordPkRecordMapper.insert(pkRecord);
        } else {
            wordPkRecordMapper.update(new UpdateWrapper<WordPkRecord>().setSql(
                    String.format("%s = %s + 1", WordPkRecord.WINNING_NUM, WordPkRecord.WINNING_NUM)
            ).eq(WordPkRecord.PK_ID, message.getPkId()).eq(WordPkRecord.ORDER_ID, message.getTrainingOrderId()));
        }
    }
}
