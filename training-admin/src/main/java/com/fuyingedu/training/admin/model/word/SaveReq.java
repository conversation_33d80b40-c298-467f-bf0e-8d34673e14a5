package com.fuyingedu.training.admin.model.word;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

@Getter
@Setter
public class SaveReq {

    @NotNull
    private Long campId;

    private Long scheduleId;
    /**
     * 外部的班级ID
     */
    private Long clazzId;

    private String clazzName;

    @NotBlank
    private String scheduleName;

    private String relationName;

    /**
     * 排期开始时间
     */
    private LocalDateTime startTime;

    /**
     * 课程天数
     */
    @NotNull
    @Positive
    private Integer days;
    /**
     * 单词书主键
     */
    private Long trainingCampPlanId;
    /**
     * 直播的天数集合
     */
    private List<Integer> liveDayList;

    /**
     * 直播的名称集合
     */
    private List<String> liveNameList;
    /**
     * 词书名称
     */
    private String name;
    /**
     * 直播方式 1-公司直播 2-导师直播
     */
    private Byte liveType;

    /**
     * 直播端口 1-腾讯会议 2-保利威 3-百家云
     */
    private Byte livePort;

    /**
     * 直播房间号
     */
    private Long liveRoom;

    /**
     * 直播开始时间
     */
    private LocalTime liveTime;
}
