package com.fuyingedu.training.admin.manager.camp;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.fuyingedu.training.entity.*;
import com.fuyingedu.training.mapper.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 共情式沟通21天陪跑训练营
 */
@Component("camp_9")
public class Camp9RuleManager implements CampRuleManager {

    @Autowired
    private LiveMapper liveMapper;
    @Autowired
    private TaskMapper taskMapper;
    @Autowired
    private TaskTemplateMapper taskTemplateMapper;
    @Autowired
    private TaskRelationMapper taskRelationMapper;
    @Autowired
    private ScheduleMapper scheduleMapper;

    @Override
    public void initRule(Long userId, Long scheduleId, LocalDate startDate, LocalTime liveTime) {
        LocalDateTime startTime = LocalDateTime.of(startDate, liveTime);
        List<Live> liveList = new ArrayList<>();
        liveList.add(buildLive(scheduleId, "第一课 共情式沟通的力量", 115, startTime));
        liveList.add(buildLive(scheduleId, "第二课 警惕4种错误的沟通模式", 115, startTime.plusDays(2)));
        liveList.add(buildLive(scheduleId, "第三课 诉请大法", 115, startTime.plusDays(4)));
        liveList.add(buildLive(scheduleId, "第四课 诉请大法的修炼秘籍", 115, startTime.plusDays(7)));
        liveList.add(buildLive(scheduleId, "第五课 成为化情高手—化情大法", 115, startTime.plusDays(9)));
        liveList.add(buildLive(scheduleId, "第六课 觉察情绪—通往共情的桥梁", 115, startTime.plusDays(11)));
        liveList.add(buildLive(scheduleId, "第七课 转变父母的错误信念", 115, startTime.plusDays(14)));
        liveList.add(buildLive(scheduleId, "第八课 转化孩子的负向信念", 115, startTime.plusDays(16)));
        liveList.add(buildLive(scheduleId, "第九课 觉察需要，提升我们的生命能量", 115, startTime.plusDays(18)));
        liveMapper.insert(liveList);
        List<Task> taskList = new ArrayList<>();
        taskList.add(buildTask(userId, scheduleId, "第一课课前思考", template(1000078L), startDate, startDate));
        taskList.add(buildTask(userId, scheduleId, "第一课课后作业", template(1000079L), startDate, startDate.plusDays(2)));
        taskList.add(buildTask(userId, scheduleId, "第二课课前思考", template(1000080L), startDate.plusDays(2), startDate.plusDays(2)));
        taskList.add(buildTask(userId, scheduleId, "第二课课后作业", template(1000081L), startDate.plusDays(2), startDate.plusDays(4)));
        taskList.add(buildTask(userId, scheduleId, "第三课课前思考", template(1000082L), startDate.plusDays(4), startDate.plusDays(4)));
        taskList.add(buildTask(userId, scheduleId, "第三课课后作业", template(1000083L), startDate.plusDays(4), startDate.plusDays(7)));
        taskList.add(buildTask(userId, scheduleId, "第四课课前思考", template(1000084L), startDate.plusDays(7), startDate.plusDays(7)));
        taskList.add(buildTask(userId, scheduleId, "第四课课后作业", template(1000085L), startDate.plusDays(7), startDate.plusDays(9)));
        taskList.add(buildTask(userId, scheduleId, "第五课课前思考", template(1000086L), startDate.plusDays(9), startDate.plusDays(9)));
        taskList.add(buildTask(userId, scheduleId, "第五课课后作业", template(1000087L), startDate.plusDays(9), startDate.plusDays(11)));
        taskList.add(buildTask(userId, scheduleId, "第六课课前思考", template(1000088L), startDate.plusDays(11), startDate.plusDays(11)));
        taskList.add(buildTask(userId, scheduleId, "第六课课后作业", template(1000089L), startDate.plusDays(11), startDate.plusDays(14)));
        taskList.add(buildTask(userId, scheduleId, "第七课课前思考", template(1000090L), startDate.plusDays(14), startDate.plusDays(14)));
        taskList.add(buildTask(userId, scheduleId, "第七课课后作业", template(1000091L), startDate.plusDays(14), startDate.plusDays(16)));
        taskList.add(buildTask(userId, scheduleId, "第八课课前思考", template(1000092L), startDate.plusDays(16), startDate.plusDays(16)));
        taskList.add(buildTask(userId, scheduleId, "第八课课后作业", template(1000093L), startDate.plusDays(16), startDate.plusDays(18)));
        taskList.add(buildTask(userId, scheduleId, "第九课课前思考", template(1000094L), startDate.plusDays(18), startDate.plusDays(18)));
        taskList.add(buildTask(userId, scheduleId, "第九课课后作业", template(1000095L), startDate.plusDays(18), startDate.plusDays(20)));
        taskMapper.insert(taskList);
        List<TaskRelation> relationList = new ArrayList<>();
        for (Task task : taskList) {
            TaskRelation relation = new TaskRelation();
            relation.setTaskId(task.getId());
            relation.setScheduleId(scheduleId);
            relation.setTeacherId(-1L);
            relation.setAssistantId(-1L);
            relation.setClazzId(-1L);
            relationList.add(relation);
        }
        taskRelationMapper.insert(relationList);
        scheduleMapper.update(new UpdateWrapper<Schedule>().setSql(String.format("%s = %s + %d", Schedule.TASK_NUM, Schedule.TASK_NUM, taskList.size()))
                .eq(Schedule.ID, scheduleId));
    }

    private TaskTemplate template(Long templateId) {
        return taskTemplateMapper.selectOne(new QueryWrapper<TaskTemplate>().eq(TaskTemplate.ID, templateId));
    }
}
