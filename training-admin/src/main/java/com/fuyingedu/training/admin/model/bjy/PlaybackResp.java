package com.fuyingedu.training.admin.model.bjy;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

@Getter
@Setter
@Accessors(chain = true)
public class PlaybackResp {

    private Long videoId;

    private LocalDateTime startTime;

    private LocalDateTime endTime;

    /**
     * 播放时长 秒
     */
    private Integer length;

    private Long scheduleId;

    /**
     * 关联排期
     */
    private String scheduleName;

    private Long liveId;

    /**
     * 关联直播
     */
    private String liveName;

    /**
     * 重播播放地址
     */
    private String playUrl;
}
