package com.fuyingedu.training.admin.controller;

import com.fuyingedu.training.admin.model.dict.DictTreeResp;
import com.fuyingedu.training.admin.service.DictionaryService;
import com.fuyingedu.training.common.model.CommResp;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 字典管理
 */
@RestController
@RequestMapping("admin/dict")
public class DictionaryController {

    @Autowired
    private DictionaryService dictionaryService;

    /**
     * 根据字典Key获取字典列表
     * @param dictKeyList 字典key列表
     * @return 字典列表
     */
    @GetMapping("list")
    public CommResp<List<DictTreeResp>> list(@Valid @NotEmpty @RequestParam("dictKeyList") List<String> dictKeyList) {
        // 排序便于缓存
        return dictionaryService.getDictList(dictKeyList.stream().sorted().toList());
    }
}
