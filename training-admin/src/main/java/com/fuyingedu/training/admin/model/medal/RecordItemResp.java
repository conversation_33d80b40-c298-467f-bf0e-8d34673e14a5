package com.fuyingedu.training.admin.model.medal;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.List;

@Getter
@Setter
@ToString
public class RecordItemResp {

    private Long id;

    /**
     * 数据新建时间
     */
    private LocalDateTime createdTime;

    /**
     * 奖状名称
     */
    private String medalName;
    /**
     * 模板名称
     */
    private String templateName;

    /**
     * 奖状图片
     */
    private String medalIcon;

    /**
     * 获奖人列表
     */
    private List<Student> studentList;

    @Getter
    @Setter
    public static class Student {

        /**
         * 获奖人姓名
         */
        private String realName;

        /**
         * 班级ID
         */
        private Long clazzId;

        /**
         * 班级名称
         */
        private String clazzName;

        /**
         * 小组ID
         */
        private Long groupId;

        /**
         * 小组名称
         */
        private String groupName;
    }
}
