package com.fuyingedu.training.admin.model.order;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;

@Getter
@Setter
@ToString
public class ItemResp {

    private Long id;
    /**
     * 来源服务单号
     */
    private String orderNo;

    /**
     * 订单号
     */
    private String realOrderNo;

    /**
     * 1-正常 2-已关闭 3-已退款 4-可能退款
     */
    private Byte orderStatus;
    /**
     * 用户名
     */
    private String realName;

    /**
     * 用户昵称
     */
    private String nickName;

    /**
     * 用户手机号
     */
    private Long phoneNum;

    /**
     * 训练营id
     */
    private Long campId;
    /**
     * 训练营名称
     */
    private String campName;

    /**
     * 排期id
     */
    private Long scheduleId;
    /**
     * 排期名称
     */
    private String scheduleName;

    /**
     * 1-未开始 2-进行中 3-已结束
     */
    private Byte scheduleStatus;

    /**
     * 辅导老师
     */
    private String teacherName;

    /**
     * 学员名称
     */
    private String studentName;

    /**
     * 班级名称
     */
    private String clazzName;

    /**
     * 分组名称
     */
    private String groupName;

    /**
     * 扶小鹰学员
     */
    private String fxyName;

    /**
     * 扶小鹰账号
     */
    private String fxyAccount;

    /**
     * 核销状态 1 - 未核销 2 - 已核销
     */
    private Byte signStatus;
    /**
     * 核销时间
     */
    private LocalDateTime signTime;
}
