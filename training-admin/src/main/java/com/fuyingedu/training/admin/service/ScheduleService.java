package com.fuyingedu.training.admin.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fuyingedu.training.admin.manager.SyncOrderManager;
import com.fuyingedu.training.admin.manager.camp.CampRuleManager;
import com.fuyingedu.training.admin.model.schedule.*;
import com.fuyingedu.training.common.enums.CampType;
import com.fuyingedu.training.common.enums.OrderStatus;
import com.fuyingedu.training.common.exception.WebBaseException;
import com.fuyingedu.training.common.model.CommResp;
import com.fuyingedu.training.common.util.AsyncUtils;
import com.fuyingedu.training.common.util.DateUtils;
import com.fuyingedu.training.common.util.RedisKey;
import com.fuyingedu.training.common.util.RespUtils;
import com.fuyingedu.training.dto.GroupNumRet;
import com.fuyingedu.training.dto.fuying.ScheduleRet;
import com.fuyingedu.training.entity.*;
import com.fuyingedu.training.front.feign.FuyingCourseFeign;
import com.fuyingedu.training.front.model.feign.ClazzItem;
import com.fuyingedu.training.front.model.feign.OrderItem;
import com.fuyingedu.training.front.model.feign.ScheduleItem;
import com.fuyingedu.training.mapper.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.NoSuchBeanDefinitionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.ApplicationContext;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ScheduleService {

    @Autowired
    private ScheduleMapper scheduleMapper;
    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private CampMapper campMapper;
    @Autowired
    private FuyingCourseFeign fuyingCourseFeign;
    @Autowired
    private SyncOrderManager syncOrderManager;
    @Autowired
    private WordScheduleMapper wordScheduleMapper;
    @Autowired
    private ApplicationContext applicationContext;
    @Autowired
    private ScheduleRoomMapper scheduleRoomMapper;
    @Autowired
    private RedisTemplate<String, String> redisTemplate;
    @Autowired
    private WordPkConfigMapper wordPkConfigMapper;

    @Transactional(rollbackFor = Exception.class)
    public void addOrEdit(Long userId, SaveReq saveReq) {
        Camp camp = campMapper.selectOne(new QueryWrapper<Camp>()
                .select(Camp.ID).eq(Camp.ID, saveReq.getCampId()));
        if (camp == null) {
            throw new WebBaseException(4000, "训练营不存在");
        }
        if (saveReq.getStartTime().isBefore(LocalDateTime.now())) {
            throw new WebBaseException(4000, "排期开始时间不能小于当前时间");
        }
        if (saveReq.getEndTime().isBefore(saveReq.getStartTime())) {
            throw new WebBaseException(4000, "排期结束时间不能小于开始时间");
        }
        Schedule schedule = scheduleMapper.selectOne(new QueryWrapper<Schedule>()
                .select(Schedule.ID, Schedule.START_TIME)
                .eq(Schedule.SCHEDULE_ID, saveReq.getScheduleId()).eq(Schedule.CLAZZ_ID, saveReq.getClazzId())
        );
        Schedule saveSchedule = toSchedule(saveReq);
        if (schedule == null) {
            scheduleMapper.insert(saveSchedule);
            campMapper.update(new UpdateWrapper<Camp>()
                    .setSql(String.format("%s = %s + 1", Camp.SCHEDULE_NUM, Camp.SCHEDULE_NUM))
                    .eq(Camp.ID, saveReq.getCampId()));
            String beanName = "camp_" + camp.getId();
            try {
                CampRuleManager ruleManager = applicationContext.getBean(beanName, CampRuleManager.class);
                ruleManager.initRule(userId, saveSchedule.getId(), saveSchedule.getStartTime().toLocalDate(), saveReq.getLiveTime());
            } catch (NoSuchBeanDefinitionException ignore) {
            }
            syncOrder(saveSchedule.getId());
        } else {
            if (LocalDateTime.now().isAfter(schedule.getStartTime())) {
                throw new WebBaseException(4000, "当前排期已开始，不能修改排期");
            }
            saveSchedule.setId(schedule.getId());
            scheduleMapper.updateById(saveSchedule);
            List<ScheduleRoom> scheduleRooms = scheduleRoomMapper.selectList(new QueryWrapper<ScheduleRoom>().select(ScheduleRoom.ID).eq(ScheduleRoom.SCHEDULE_ID, schedule.getId()));
            if (!CollectionUtils.isEmpty(scheduleRooms)) {
                scheduleRoomMapper.update(new UpdateWrapper<ScheduleRoom>().set(ScheduleRoom.SCHEDULE_ID, null)
                        .in(ScheduleRoom.ID, scheduleRooms.stream().map(ScheduleRoom::getId).toList()));
            }
        }
        if (saveReq.getLiveRoom() != null) {
            scheduleRoomMapper.update(new UpdateWrapper<ScheduleRoom>()
                    .set(ScheduleRoom.SCHEDULE_ID, saveSchedule.getId())
                    .eq(ScheduleRoom.ROOM_ID, saveReq.getLiveRoom())
            );
        }
    }

    public CommResp<List<ItemResp>> list(ListReq listReq) {
        Camp camp = campMapper.selectOne(new QueryWrapper<Camp>()
                .select(Camp.ID, Camp.CAMP_TYPE).eq(Camp.ID, listReq.getCampId()));
        if (camp == null) {
            return RespUtils.success(listReq.getPageNum(), listReq.getPageSize(), 0, Collections.emptyList());
        }
        IPage<Schedule> page = new Page<>(listReq.getPageNum(), listReq.getPageSize());
        page = scheduleMapper.selectPage(page, new QueryWrapper<Schedule>()
                .select(
                        Schedule.ID, Schedule.SCHEDULE_NAME, Schedule.START_TIME, Schedule.END_TIME, Schedule.GRADE_NUM,
                        Schedule.CLASS_NUM, Schedule.SCHEDULE_ID, Schedule.CLAZZ_ID, Schedule.CLAZZ_NAME,
                        Schedule.TASK_NUM, Schedule.LIVE_ROOM, Schedule.RELATION_NAME,
                        Schedule.LIVE_NUM, Schedule.LIVE_PORT, Schedule.LIVE_TYPE, Schedule.LIVE_TIME
                ).eq(Schedule.CAMP_ID, listReq.getCampId())
                .like(StringUtils.hasLength(listReq.getScheduleName()), Schedule.SCHEDULE_NAME, listReq.getScheduleName())
                .between(listReq.getStartTimeBegin() != null && listReq.getStartTimeEnd() != null,
                        Schedule.START_TIME, listReq.getStartTimeBegin(), listReq.getStartTimeEnd())
                .between(listReq.getEndTimeBegin() != null && listReq.getEndTimeEnd() != null,
                        Schedule.END_TIME, listReq.getEndTimeBegin(), listReq.getEndTimeEnd())
                .orderByDesc(Schedule.ID)
        );
        List<ItemResp> respList = page.getRecords().stream().map(this::toItemResp).toList();
        if (CampType.WORD.getCode().equals(camp.getCampType()) && !CollectionUtils.isEmpty(respList)) {
            List<Long> scheduleIds = respList.stream().map(ItemResp::getId).toList();
            Map<Long, Integer> pkNumMap = wordPkConfigMapper.groupByScheduleId(scheduleIds).stream().collect(Collectors.toMap(GroupNumRet::getId, GroupNumRet::getNum));
            Map<Long, Long> wordMap = wordScheduleMapper.selectList(new QueryWrapper<WordSchedule>().select(WordSchedule.SCHEDULE_ID, WordSchedule.WORD_ID).in(
                    WordSchedule.SCHEDULE_ID, respList.stream().map(ItemResp::getId).toList()
            )).stream().collect(Collectors.toMap(WordSchedule::getScheduleId, WordSchedule::getWordId));
            respList.forEach(resp -> {
                resp.setWordId(wordMap.get(resp.getId()));
                resp.setPkNum(pkNumMap.get(resp.getId()));
            });
        }
        return RespUtils.success(page.getCurrent(), page.getSize(), page.getTotal(), respList);
    }

    public CommResp<DetailResp> detail(Long id) {
        Schedule schedule = scheduleMapper.selectOne(new QueryWrapper<Schedule>()
                .select(
                        Schedule.ID, Schedule.CAMP_ID,
                        Schedule.SCHEDULE_NAME,
                        Schedule.START_TIME, Schedule.CLAZZ_NAME,
                        Schedule.END_TIME, Schedule.SCHEDULE_ID, Schedule.CLAZZ_ID,
                        Schedule.GRADE_NUM, Schedule.RELATION_NAME
                )
                .eq(Schedule.ID, id));
        return RespUtils.success(toDetailResp(schedule));
    }

    private Schedule toSchedule(SaveReq saveReq) {
        Schedule schedule = new Schedule();
        schedule.setId(saveReq.getId());
        schedule.setScheduleId(saveReq.getScheduleId());
        schedule.setClazzId(saveReq.getClazzId());
        schedule.setClazzName(saveReq.getClazzName());
        schedule.setCampId(saveReq.getCampId());
        schedule.setScheduleName(saveReq.getScheduleName());
        schedule.setStartTime(saveReq.getStartTime());
        schedule.setEndTime(saveReq.getEndTime());
        schedule.setRelationName(saveReq.getRelationName());
        schedule.setLiveTime(saveReq.getLiveTime());
        schedule.setLivePort(saveReq.getLivePort());
        schedule.setLiveRoom(saveReq.getLiveRoom());
        schedule.setLiveType(saveReq.getLiveType());
        return schedule;
    }

    private DetailResp toDetailResp(Schedule schedule) {
        DetailResp detailResp = new DetailResp();
        detailResp.setId(schedule.getId());
        detailResp.setScheduleName(schedule.getScheduleName());
        detailResp.setStartTime(schedule.getStartTime());
        detailResp.setEndTime(schedule.getEndTime());
        detailResp.setGradeNum(schedule.getGradeNum());
        detailResp.setRelationName(schedule.getRelationName());
        detailResp.setScheduleId(schedule.getScheduleId());
        detailResp.setClazzId(schedule.getClazzId());
        detailResp.setClazzName(schedule.getClazzName());
        return detailResp;
    }

    private ItemResp toItemResp(Schedule schedule) {
        ItemResp itemResp = new ItemResp();
        itemResp.setId(schedule.getId());
        itemResp.setScheduleName(schedule.getScheduleName());
        itemResp.setScheduleId(schedule.getScheduleId());
        itemResp.setRelationName(schedule.getRelationName());
        itemResp.setClazzId(schedule.getClazzId());
        itemResp.setClazzName(schedule.getClazzName());
        itemResp.setStartTime(schedule.getStartTime());
        itemResp.setEndTime(schedule.getEndTime());
        itemResp.setLiveNum(schedule.getLiveNum());
        itemResp.setTaskNum(schedule.getTaskNum());
        itemResp.setGradeNum(schedule.getGradeNum());
        itemResp.setClassNum(schedule.getClassNum());
        itemResp.setLivePort(schedule.getLivePort());
        itemResp.setLiveTime(schedule.getLiveTime());
        itemResp.setLiveType(schedule.getLiveType());
        itemResp.setLiveRoom(schedule.getLiveRoom());
        return itemResp;
    }

    public CommResp<List<ScheduleRet>> fyList(FyListReq fyListReq) {
        Camp camp = campMapper.selectOne(new QueryWrapper<Camp>().select(
                Camp.CAMP_NAME
        ).eq(Camp.ID, fyListReq.getCampId()));
        LocalDateTime startTime = fyListReq.getStartTime();
        if (startTime == null) {
            startTime = LocalDateTime.now().minusYears(1);
        }
        List<ScheduleItem> scheduleList = fuyingCourseFeign.listSchedule(Collections.singleton(fyListReq.getCampId()),
                DateUtils.format(startTime), fyListReq.getEndTime() == null ? null : DateUtils.format(fyListReq.getEndTime()));
        if (CollectionUtils.isEmpty(scheduleList)) {
            return RespUtils.success(Collections.emptyList());
        }
        List<ScheduleRet> respList = scheduleList.stream()
                .map(scheduleItem -> {
                    ScheduleRet scheduleRet = new ScheduleRet();
                    scheduleRet.setId(scheduleItem.getId());
                    scheduleRet.setPid(scheduleItem.getCourseId());
                    scheduleRet.setScheduleName(scheduleItem.getTitle());
                    scheduleRet.setStartTimeStr(scheduleItem.getStartTime());
                    scheduleRet.setCampName(camp.getCampName());
                    return scheduleRet;
                }).sorted(Comparator.comparing(ScheduleRet::getId).reversed()).toList();
        return RespUtils.success(respList);
    }

    public CommResp<List<ClazzItem>> fyClazzList(Long scheduleId) {
        List<ClazzItem> clazzList = fuyingCourseFeign.listClassSchedule(scheduleId);
        List<Long> clazzIds = scheduleMapper.selectList(new LambdaQueryWrapper<Schedule>().select(
                Schedule::getClazzId
        ).eq(Schedule::getScheduleId, scheduleId)).stream().map(Schedule::getClazzId).toList();
        clazzList = clazzList.stream().filter(clazzItem -> !clazzIds.contains(clazzItem.getId())).toList();
        return RespUtils.success(clazzList);
    }

    public void syncOrder(Long scheduleId) {
        SyncOrderReq syncOrderReq = new SyncOrderReq();
        syncOrderReq.setId(scheduleId);
        syncOrder(syncOrderReq);
    }

    public void syncOrder(SyncOrderReq syncOrderReq) {
        if (syncOrderReq.getOrderNo() == null) {
            Schedule schedule = scheduleMapper.selectOne(new QueryWrapper<Schedule>().select(
                    Schedule.ID, Schedule.SCHEDULE_ID, Schedule.CLAZZ_ID
            ).eq(Schedule.ID, syncOrderReq.getId()));
            List<OrderItem> orderItems = fuyingCourseFeign.listCourseTrainee(schedule.getScheduleId(), syncOrderReq.getOrderNo());
            String key = String.format(RedisKey.SYNC_ORDER_LOCK, syncOrderReq.getId());
            Boolean b = redisTemplate.opsForValue().setIfAbsent(key, "", 10, TimeUnit.MINUTES);
            if (Boolean.FALSE.equals(b)) {
                return;
            }
            AsyncUtils.execute(() -> {
                try {
                    for (OrderItem orderItem : orderItems) {
                        try {
                            if ((schedule.getClazzId() == null && orderItem.getScheduleClassId() == null)
                                    || (schedule.getClazzId() != null && schedule.getClazzId().equals(orderItem.getScheduleClassId()))) {
                                syncOrderManager.syncOrder(schedule, orderItem);
                            }
                        } catch (Exception e) {
                            log.info("服务单手动同步失败: {}", orderItem.getId(), e);
                        }
                    }
                } finally {
                    redisTemplate.delete(key);
                }
            }, "同步服务单");

        } else {
            List<OrderItem> orderItems = fuyingCourseFeign.listCourseTrainee(null, syncOrderReq.getOrderNo());
            if (!CollectionUtils.isEmpty(orderItems)) {
                syncOrderManager.syncOrder(orderItems.get(0));
            }
        }
    }

    public void flushClazz() {
        List<Schedule> scheduleList = scheduleMapper.selectList(new QueryWrapper<Schedule>().select(
                Schedule.ID, Schedule.SCHEDULE_ID
        ).isNull(Schedule.CLAZZ_ID));
        for (Schedule schedule : scheduleList) {
            List<ClazzItem> clazzItems = fuyingCourseFeign.listClassSchedule(schedule.getScheduleId());
            if (CollectionUtils.isEmpty(clazzItems)) {
                log.info("排期{}没有班级", schedule.getScheduleId());
                continue;
            }
            ClazzItem clazzItem = clazzItems.get(0);
            scheduleMapper.update(new UpdateWrapper<Schedule>()
                    .set(Schedule.CLAZZ_ID, clazzItem.getId())
                    .set(Schedule.CLAZZ_NAME, clazzItem.getName())
                    .eq(Schedule.ID, schedule.getId()));
        }
    }

    public CommResp<List<Relation>> orderList(Long orderId) {
        Order order = orderMapper.selectOne(new QueryWrapper<Order>().select(
                Order.USER_ID
        ).eq(Order.ID, orderId));
        Set<Long> scheduleIds = orderMapper.selectList(new QueryWrapper<Order>().select(
                        Order.SCHEDULE_ID
                ).eq(Order.USER_ID, order.getUserId()).eq(Order.ORDER_STATUS, OrderStatus.NORMAL.getCode())).stream()
                .map(Order::getScheduleId).collect(Collectors.toSet());
        List<Schedule> scheduleList = scheduleMapper.selectList(new QueryWrapper<Schedule>().select(
                Schedule.ID, Schedule.SCHEDULE_NAME
        ).in(Schedule.ID, scheduleIds).orderByDesc(Schedule.ID));
        return RespUtils.success(scheduleList.stream().map(schedule -> {
            Relation relation = new Relation();
            relation.setRelationId(orderId);
            relation.setRelationName(schedule.getScheduleName());
            return relation;
        }).toList());
    }
}
