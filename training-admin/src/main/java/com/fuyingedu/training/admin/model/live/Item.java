package com.fuyingedu.training.admin.model.live;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.validator.constraints.Range;

import java.time.LocalDateTime;

@Getter
@Setter
@ToString
public class Item {
    private Long id;

    /**
     * 直播计划名称
     */
    @NotBlank
    private String liveName;

    /**
     * 直播开始时间
     */
    @NotNull
    private LocalDateTime startTime;

    /**
     * 直播房间号
     */
    private String liveRoom;
    /**
     * 直播持续时间
     */
    private Integer liveDuration;

    /**
     * 重播开始时间
     */
    private LocalDateTime repeatStartTime;

    /**
     * 重播截止时间
     */
    private LocalDateTime repeatEndTime;
}
