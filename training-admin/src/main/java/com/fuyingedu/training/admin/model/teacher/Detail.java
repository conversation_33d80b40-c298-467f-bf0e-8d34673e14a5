package com.fuyingedu.training.admin.model.teacher;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Getter
@Setter
@ToString
public class Detail {
    private Long id;

    /**
     * 用户头像
     */
    private String userIcon;

    /**
     * 用户名
     */
    private String realName;

    /**
     * 手机号
     */
    @NotNull
    private Long phoneNum;

    /**
     * 微信unionid
     */
    private String unionId;

    /**
     * 傲爸妈uid
     */
    @NotNull
    private Long outerId;

    /**
     * 可服务训练营
     */
    @NotEmpty
    private List<Long> campIdList;

    /**
     * 老师状态 1-有效 2-无效
     */
    private Byte teacherStatus;
}
