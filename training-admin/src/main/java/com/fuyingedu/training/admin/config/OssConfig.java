package com.fuyingedu.training.admin.config;


import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.sts20150401.Client;
import com.aliyun.teaopenapi.models.Config;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class OssConfig {

    @Value("${oss.access-key}")
    private String accessKey;
    @Value("${oss.secret-key}")
    private String secretKey;

    @Value("${oss.persistent-key}")
    private String persistentKey;
    @Value("${oss.persistent-secret}")
    private String persistentSecret;

    @Value("${oss.endpoint}")
    private String endpoint;

    @Bean
    public Client ossClient() throws Exception {
        Config credentialConfig = new Config();
        credentialConfig.setType("access_key");
        credentialConfig.setAccessKeyId(accessKey);
        credentialConfig.setAccessKeySecret(secretKey);
        credentialConfig.setRegionId("cn-hangzhou");
        return new Client(credentialConfig);
    }


    @Bean
    public OSS oss() {
        return new OSSClientBuilder().build(endpoint, persistentKey, persistentSecret);
    }
}
