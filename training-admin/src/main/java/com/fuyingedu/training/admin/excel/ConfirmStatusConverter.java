package com.fuyingedu.training.admin.excel;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;

public class ConfirmStatusConverter implements Converter<Byte> {

    public WriteCellData<?> convertToExcelData(Byte value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration)
    {
        if (value == null) {
            return null;
        }
        if (value == 1) {
            return new WriteCellData<>("未确认");
        }
        if (value == 2) {
            return new WriteCellData<>("自己确认");
        }
        if (value == 3) {
            return new WriteCellData<>("助教确认");
        }
        return new WriteCellData<>("不来");
    }
}
