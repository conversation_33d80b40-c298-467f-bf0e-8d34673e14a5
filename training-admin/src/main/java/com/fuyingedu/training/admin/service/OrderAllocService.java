package com.fuyingedu.training.admin.service;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.SimpleColumnWidthStyleStrategy;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.fuyingedu.training.admin.model.order.alloc.*;
import com.fuyingedu.training.common.enums.*;
import com.fuyingedu.training.common.exception.WebBaseException;
import com.fuyingedu.training.common.model.CommResp;
import com.fuyingedu.training.common.util.JsonUtils;
import com.fuyingedu.training.common.util.RedisKey;
import com.fuyingedu.training.common.util.RespUtils;
import com.fuyingedu.training.entity.*;
import com.fuyingedu.training.front.manager.OperationManager;
import com.fuyingedu.training.mapper.*;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class OrderAllocService {

    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private ScheduleTeacherMapper scheduleTeacherMapper;
    @Autowired
    private UserRemarkService userRemarkService;
    @Autowired
    private GroupMapper groupMapper;
    @Autowired
    private RedisTemplate<String, String> redisTemplate;
    @Autowired
    private TeacherMapper teacherMapper;
    @Autowired
    private UserMapper userMapper;
    @Autowired
    private ScheduleMapper scheduleMapper;
    @Autowired
    private OperationManager operationManager;

    @Transactional(rollbackFor = Exception.class)
    public CommResp<?> allocByExcel(Long userId, MultipartFile file) {
        List<ExcelHead> notAllocList = new ArrayList<>();
        try {
            List<ExcelHead> list = EasyExcel.read(file.getInputStream()).autoCloseStream(true)
                    .sheet(0).head(ExcelHead.class).doReadSync();
            list.forEach(item -> {
                if (StringUtils.hasLength(item.getOrderNo())) {
                    item.setOrderNo(item.getOrderNo().trim());
                }
                if (StringUtils.hasLength(item.getTeacherName())) {
                    item.setTeacherName(item.getTeacherName().trim());
                }
            });
            Set<String> nameSet = list.stream().filter(item -> StringUtils.hasLength(item.getTeacherName()))
                    .map(item -> item.getTeacherName().trim()).collect(Collectors.toSet());
            if (nameSet.isEmpty()) {
                return RespUtils.warning(4000, "数据错误，没有老师信息");
            }
            List<Order> orderList = orderMapper.selectList(new LambdaQueryWrapper<Order>().select(
                            Order::getId, Order::getScheduleId, Order::getOrderNo, Order::getTeacherId, Order::getClazzId, Order::getGroupId
                    ).in(Order::getOrderNo, list.stream().map(ExcelHead::getOrderNo).toList())
            );
            Set<Long> scheduleIdSet = orderList.stream().map(Order::getScheduleId).collect(Collectors.toSet());
            if (scheduleIdSet.size() != 1) {
                return RespUtils.warning(4000, "数据错误，排期不一致");
            }
            Set<Long> scheduleTeacherIdSet = scheduleTeacherMapper.selectList(new LambdaQueryWrapper<ScheduleTeacher>().select(
                            ScheduleTeacher::getTeacherId
                    ).in(ScheduleTeacher::getScheduleId, scheduleIdSet)
            ).stream().map(ScheduleTeacher::getTeacherId).collect(Collectors.toSet());
            List<Teacher> teacherList = teacherMapper.selectList(new LambdaQueryWrapper<Teacher>().select(
                                    Teacher::getId, Teacher::getRealName
                            ).in(Teacher::getId, scheduleTeacherIdSet).in(Teacher::getRealName, nameSet)
            );
            Map<String, List<Teacher>> teacherMap = teacherList.stream().collect(Collectors.groupingBy(Teacher::getRealName));
            Map<String, Order> orderMap = orderList.stream().collect(Collectors.toMap(Order::getOrderNo, Function.identity()));
            List<Order> allocList = new ArrayList<>();
            for (ExcelHead excelHead : list) {
                List<Teacher> teachers = teacherMap.get(excelHead.getTeacherName());
                if (teachers == null || teachers.isEmpty()) {
                    excelHead.setErrorMsg("导师不存在");
                    notAllocList.add(excelHead);
                    continue;
                }
                if (teachers.size() > 1) {
                    excelHead.setErrorMsg("存在多个同名导师");
                    notAllocList.add(excelHead);
                    continue;
                }
                Order order = orderMap.get(excelHead.getOrderNo());
                if (order == null) {
                    excelHead.setErrorMsg("服务单不存在");
                    notAllocList.add(excelHead);
                    continue;
                }
                Order updateEntity = new Order();
                updateEntity.setId(order.getId());
                Teacher teacher = teachers.getFirst();
                updateEntity.setTeacherId(teacher.getId());
                updateEntity.setStudentType(StudentType.NORMAL.getCode());
                allocList.add(updateEntity);
            }
            if (!allocList.isEmpty()) {
                orderMapper.updateById(allocList);
                operationManager.allocTeacher(userId, orderList, allocList);
                Set<Long> orderIds = allocList.stream().filter(item -> item.getClazzId() != null)
                        .map(Order::getId).collect(Collectors.toSet());
                if (!orderIds.isEmpty()) {
                    orderMapper.update(new UpdateWrapper<Order>()
                            .setSql("clazz_id = null").setSql("group_id = null")
                            .in(Order.ID, orderIds));
                }
                Set<Long> groupIds = allocList.stream().map(Order::getGroupId).filter(Objects::nonNull).collect(Collectors.toSet());
                if (!groupIds.isEmpty()) {
                    groupMapper.update(new UpdateWrapper<Group>().setSql("monitor_id = null").in(Group.ID, groupIds));
                }
            }
        } catch (Exception e) {
            log.error("读取excel失败", e);
            return RespUtils.warning(4000, "模板错误，请检查上传的Excel");
        }
        String key = UUID.randomUUID().toString().replaceAll("-", "");
        if (!notAllocList.isEmpty()) {
            redisTemplate.opsForValue().set(String.format(RedisKey.ALLOC_FAIL_RESP, key),
                    JsonUtils.formatObjToJson(notAllocList), 1, TimeUnit.HOURS);
        }
        ExcelResp excelResp = new ExcelResp();
        excelResp.setKey(key);
        excelResp.setFailList(notAllocList);
        return RespUtils.success(excelResp);
    }

    public void downloadFailList(String key, HttpServletResponse response) throws IOException {
        String failList = redisTemplate.opsForValue().get(String.format(RedisKey.ALLOC_FAIL_RESP, key));
        List<ExcelHead> list = Collections.emptyList();
        if (StringUtils.hasLength(failList)) {
            list = JsonUtils.parseJsonToList(failList, ExcelHead.class);
        }
        EasyExcel.write(response.getOutputStream(), ExcelHead.class)
                .sheet("错误信息")
                .registerWriteHandler(new SimpleColumnWidthStyleStrategy(30))
                .doWrite(list);
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateTeacher(Long userId, UpdateTeacherReq updateTeacherReq) {
        Order order = orderMapper.selectOne(new QueryWrapper<Order>()
                .select(
                        Order.ID,
                        Order.SCHEDULE_ID, Order.CLAZZ_ID,
                        Order.USER_ID, Order.TEACHER_ID, Order.STUDENT_TYPE, Order.GROUP_ID
                ).eq(Order.ID, updateTeacherReq.getId())
                .in(Order.ORDER_STATUS, OrderStatus.NORMAL.getCode(), OrderStatus.MAY_REFUND.getCode())
        );
        if (order == null) {
            throw new WebBaseException(RespMetaEnum.PARAM_ERROR);
        }
        if (order.getScheduleId() == null) {
            throw new WebBaseException(500, "排期未分配，无法分配");
        }
        if (updateTeacherReq.getTeacherId().equals(order.getTeacherId())) {
            return;
        }
        ScheduleTeacher scheduleTeacher = scheduleTeacherMapper.selectOne(new QueryWrapper<ScheduleTeacher>().select(
                        ScheduleTeacher.TEACHER_ID
                ).eq(ScheduleTeacher.ID, updateTeacherReq.getTeacherId()).eq(ScheduleTeacher.TEACHER_TYPE, TeacherType.TEACHER.getCode())
                .eq(ScheduleTeacher.SCHEDULE_ID, order.getScheduleId()));
        if (scheduleTeacher == null) {
            throw new WebBaseException(500, "老师不存在");
        }
        orderMapper.update(new UpdateWrapper<Order>()
                .set(Order.TEACHER_ID, scheduleTeacher.getTeacherId())
                .setSql("clazz_id = null").setSql("group_id = null")
                .set(Order.STUDENT_TYPE, StudentType.NORMAL.getCode())
                .eq(Order.ID, updateTeacherReq.getId()));
        operationManager.updateTeacher(userId, order, scheduleTeacher.getTeacherId());
        resetGroupMonitor(order.getStudentType(), order.getGroupId());
        userRemarkService.addRemark(userId, order.getScheduleId(), order.getUserId(), updateTeacherReq.getRemark());
    }

    private void resetGroupMonitor(Byte studentType, Long groupId) {
        if (StudentType.VOLUNTEER.getCode().equals(studentType)) {
            groupMapper.update(new UpdateWrapper<Group>().setSql("monitor_id = null").eq(Group.ID, groupId));
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public CommResp<List<AllocResp>> alloc(Long userId, AllocReq allocReq) {
        List<ScheduleTeacher> scheduleTeacherList = scheduleTeacherMapper.selectList(new QueryWrapper<ScheduleTeacher>().select(
                                ScheduleTeacher.TEACHER_ID,
                                ScheduleTeacher.GRADE_WEIGHT,
                                ScheduleTeacher.GRADE_NUM
                        ).eq(ScheduleTeacher.SCHEDULE_ID, allocReq.getScheduleId())
                        .eq(ScheduleTeacher.TEACHER_TYPE, TeacherType.TEACHER.getCode()).orderByDesc(ScheduleTeacher.GRADE_WEIGHT)
        );
        if (scheduleTeacherList.isEmpty()) {
            return RespUtils.success(Collections.emptyList());
        }
        List<Order> orderList = orderMapper.selectList(new QueryWrapper<Order>().select(
                                Order.ID, Order.SCHEDULE_ID,
                                Order.TEACHER_ID,
                                Order.STUDENT_FLAG
                        ).eq(Order.SCHEDULE_ID, allocReq.getScheduleId())
                .in(Order.ORDER_STATUS, OrderStatus.NORMAL.getCode(), OrderStatus.MAY_REFUND.getCode())
        );
        if (orderList.isEmpty()) {
            return RespUtils.success(Collections.emptyList());
        }
        Boolean b = redisTemplate.opsForValue().setIfAbsent(String.format(RedisKey.ALLOC_TEACHER_LOCK, allocReq.getScheduleId()),
                "", 10, TimeUnit.MINUTES);
        if (Boolean.FALSE.equals(b)) {
            throw new WebBaseException(4000, "正在分配，请勿重复点击");
        }
        try {
            return RespUtils.success(alloc(userId, allocReq, scheduleTeacherList, orderList));
        } finally {
            redisTemplate.delete(String.format(RedisKey.ALLOC_TEACHER_LOCK, allocReq.getScheduleId()));
        }
    }

    private List<AllocResp> alloc(Long userId, AllocReq allocReq, List<ScheduleTeacher> scheduleTeacherList, List<Order> orderList) {
        Map<Long, List<Order>> allocatedMap = Collections.emptyMap();
        List<Order> needAllocList = orderList;
        if (AllocType.NOT_ALLOC.getCode().equals(allocReq.getAllocType())) {
            allocatedMap = orderList.stream().filter(order -> order.getTeacherId() != null)
                    .collect(Collectors.groupingBy(Order::getTeacherId));
            needAllocList = orderList.stream().filter(order -> order.getTeacherId() == null)
                    .toList();
        }
        Map<Long, List<Order>> newAllactedMap = new HashMap<>();
        if (AllocMode.AVERAGE.getCode().equals(allocReq.getAllocMode())) {
            // 平均分配不考虑老师权重和班级最大人数
            int gradeIdx = 0;
            for (Order order : needAllocList) {
                ScheduleTeacher teacher = scheduleTeacherList.get(gradeIdx);
                List<Order> newAllocatedIds = newAllactedMap.computeIfAbsent(teacher.getTeacherId(), v -> new ArrayList<>());
                Order newOrder = new Order();
                newOrder.setId(order.getId());
                newOrder.setTeacherId(teacher.getTeacherId());
                newAllocatedIds.add(newOrder);
                gradeIdx++;
                if (gradeIdx == scheduleTeacherList.size()) {
                    gradeIdx = 0;
                }
            }
        } else {
            int studentIdx = 0;
            for (ScheduleTeacher teacher : scheduleTeacherList) {
                List<Order> allocatedList = allocatedMap.get(teacher.getId());
                int canAllocNum = teacher.getGradeNum() - (allocatedList == null ? 0 : allocatedList.size());
                for (int i = 0; i < canAllocNum; i++) {
                    if (studentIdx == needAllocList.size()) {
                        break;
                    }
                    List<Order> newAllocatedIds = newAllactedMap.computeIfAbsent(teacher.getTeacherId(), v -> new ArrayList<>());
                    Order order = needAllocList.get(studentIdx++);
                    Order newOrder = new Order();
                    newOrder.setId(order.getId());
                    newOrder.setTeacherId(teacher.getTeacherId());
                    newAllocatedIds.add(newOrder);
                }
            }
            if (studentIdx != needAllocList.size()) {
                // 剩余的全部分配给最后一个班
                ScheduleTeacher teacher = scheduleTeacherList.getLast();
                List<Order> newAllocatedIds = newAllactedMap.computeIfAbsent(teacher.getTeacherId(), v -> new ArrayList<>());
                List<Order> lastOrderList = needAllocList.subList(studentIdx, needAllocList.size());
                newAllocatedIds.addAll(lastOrderList.stream().map(order -> {
                    Order newOrder = new Order();
                    newOrder.setId(order.getId());
                    order.setTeacherId(teacher.getTeacherId());
                    return newOrder;
                }).toList());
            }
        }
        Map<Long, Long> teacherMap = teacherMapper.selectList(new QueryWrapper<Teacher>()
                        .select(Teacher.ID, Teacher.USER_ID)
                        .in(Teacher.ID, scheduleTeacherList.stream().map(ScheduleTeacher::getTeacherId).toList()))
                .stream().collect(Collectors.toMap(Teacher::getId, Teacher::getUserId));
        Map<Long, User> userMap = userMapper.selectList(new QueryWrapper<User>()
                        .select(User.ID, User.PHONE_NUM, User.REAL_NAME)
                        .in(User.ID, teacherMap.values()))
                .stream().collect(Collectors.toMap(User::getId, Function.identity()));
        List<AllocResp> respList = new ArrayList<>(scheduleTeacherList.size());
        for (ScheduleTeacher scheduleTeacher : scheduleTeacherList) {
            User teacher = userMap.get(teacherMap.get(scheduleTeacher.getTeacherId()));
            AllocResp resp = new AllocResp();
            resp.setTeacherName(teacher.getRealName());
            resp.setPhoneNum(teacher.getPhoneNum());
            resp.setGradeWeight(scheduleTeacher.getGradeWeight());
            List<Order> newAllactedList = newAllactedMap.getOrDefault(scheduleTeacher.getTeacherId(), Collections.emptyList());
            resp.setRealNum(newAllactedList.size());
            resp.setNewNum((int) newAllactedList.stream().filter(student -> StudentFlag.NEW.getCode().equals(student.getStudentFlag())).count());
            resp.setOldNum((int) newAllactedList.stream().filter(student -> StudentFlag.OLD.getCode().equals(student.getStudentFlag())).count());
            respList.add(resp);
        }
        if (AllocSave.SAVE.getCode().equals(allocReq.getAllocSave())) {
            List<Order> allocList = new ArrayList<>();
            for (List<Order> list : newAllactedMap.values()) {
                allocList.addAll(list);
            }
            orderMapper.updateById(allocList);
            operationManager.allocTeacher(userId, needAllocList, allocList);
        }
        return respList;
    }
}
