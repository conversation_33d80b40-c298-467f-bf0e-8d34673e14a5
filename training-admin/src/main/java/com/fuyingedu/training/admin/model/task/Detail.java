package com.fuyingedu.training.admin.model.task;

import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;

@Getter
@Setter
@ToString
public class Detail {

    private Long id;
    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 签到/打卡/作业开始时间
     */
    private LocalTime startTime;

    /**
     * 签到/打卡/作业结束时间
     */
    private LocalTime endTime;

    /**
     * 任务开始日期
     */
    @NotNull
    private LocalDate startDate;

    /**
     * 任务结束日期
     */
    @NotNull
    private LocalDate endDate;

    /**
     * 任务类型 1-签到 2-打卡 3-作业
     */
    private Byte taskType;

    /**
     * 任务介绍
     */
    private String taskContent;


    /**
     * 上传项目类型
     */
    private List<UploadItem> itemList;

    /**
     * 任务积分
     */
    private Integer taskReward;

    /**
     * 扶小鹰小太阳
     */
    private Integer fxyReward;

    /**
     * Word鹰积分值
     */
    private Integer wordReward;

    /**
     * 1 所有班级 2 指定班级
     */
    private Byte clazzType;

    /**
     * 班级ID列表
     */
    private List<Long> clazzIdList;
}
