package com.fuyingedu.training.admin.model.task.template;

import com.fuyingedu.training.admin.model.task.UploadItem;
import com.fuyingedu.training.front.model.media.MediaResp;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalTime;
import java.util.List;

@Getter
@Setter
@ToString
public class Detail {

    private Long id;
    /**
     * 任务名称
     */
    @NotBlank
    private String taskName;

    /**
     * 开始时间
     */
    private LocalTime startTime;

    /**
     * 结束时间
     */
    private LocalTime endTime;

    /**
     * 适用的训练营类型 1-所有 2-指定
     */
    @NotNull
    private Byte campType;

    /**
     * 训练营id列表
     */
    @NotEmpty
    private List<Long> campIdList;

    /**
     * 任务类型 1-签到 2-打卡 3-作业
     */
    @NotNull
    private Byte taskType;

    /**
     * 任务介绍
     */
    private String taskContent;

    /**
     * 上传项目类型
     */
    private List<UploadItem> itemList;

    /**
     * 任务积分
     */
    private Integer taskReward;

    /**
     * 扶小鹰小太阳
     */
    private Integer fxyReward;

    private Integer wordReward;
}
