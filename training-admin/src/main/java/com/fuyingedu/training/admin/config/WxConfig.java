package com.fuyingedu.training.admin.config;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.api.impl.WxMaServiceImpl;
import cn.binarywang.wx.miniapp.config.impl.WxMaDefaultConfigImpl;
import me.chanjar.weixin.common.service.WxOAuth2Service;
import me.chanjar.weixin.cp.api.WxCpService;
import me.chanjar.weixin.cp.api.impl.WxCpServiceImpl;
import me.chanjar.weixin.cp.config.impl.WxCpDefaultConfigImpl;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.api.impl.WxMpServiceImpl;
import me.chanjar.weixin.mp.config.impl.WxMpDefaultConfigImpl;
import me.chanjar.weixin.open.api.WxOpenConfigStorage;
import me.chanjar.weixin.open.api.impl.WxOpenInMemoryConfigStorage;
import me.chanjar.weixin.open.api.impl.WxOpenOAuth2ServiceImpl;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class WxConfig {
    @Value("${wx.config.app-id}")
    private String appId;
    @Value("${wx.config.secret}")
    private String secret;
    @Value("${wx.config.corp-id}")
    private String corpId;
    @Value("${wx.config.corp-secret}")
    private String corpSecret;
    @Value("${wx.config.ma.app-id}")
    private String maAppId;
    @Value("${wx.config.ma.secret}")
    private String maSecret;

    /**
     * 公众平台 - 后台扫码登录
     */
    @Bean
    public WxOAuth2Service wxOAuth2Service() {
        WxOpenConfigStorage wxOpenConfigStorage = new WxOpenInMemoryConfigStorage();
        return new WxOpenOAuth2ServiceImpl(appId, secret, wxOpenConfigStorage);
    }

    /**
     * 小程序
     */
    @Bean
    public WxMaService wxMaService() {
        WxMaDefaultConfigImpl wxMaDefaultConfig = new WxMaDefaultConfigImpl();
        wxMaDefaultConfig.setAppid(maAppId);
        wxMaDefaultConfig.setSecret(maSecret);
        wxMaDefaultConfig.useStableAccessToken(true);
        WxMaServiceImpl wxMaService = new WxMaServiceImpl();
        wxMaService.setWxMaConfig(wxMaDefaultConfig);
        return wxMaService;
    }

    /**
     * 企业微信
     */
    @Bean
    public WxCpService wxCpService() {
        WxCpDefaultConfigImpl configStorage = new WxCpDefaultConfigImpl();
        configStorage.setCorpId(corpId);
        configStorage.setCorpSecret(corpSecret);
        WxCpService wxCpService = new WxCpServiceImpl();
        wxCpService.setWxCpConfigStorage(configStorage);
        return wxCpService;
    }

    /**
     * 公众号
     */
    @Bean
    public WxMpService wxOpenService() {
        WxMpService wxMpService = new WxMpServiceImpl();
        WxMpDefaultConfigImpl wxMpDefaultConfig = new WxMpDefaultConfigImpl();
        wxMpDefaultConfig.setAppId("wxc0b36513fcb20562");
        wxMpDefaultConfig.setSecret("c1ed8474b401a24edbb70a13a8a2e49b");
        wxMpDefaultConfig.useStableAccessToken(true);
        wxMpService.setWxMpConfigStorage(wxMpDefaultConfig);
        return wxMpService;
    }
}
