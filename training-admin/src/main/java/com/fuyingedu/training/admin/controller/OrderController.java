package com.fuyingedu.training.admin.controller;

import com.alibaba.excel.EasyExcel;
import com.fuyingedu.training.admin.manager.SyncOrderManager;
import com.fuyingedu.training.admin.model.order.*;
import com.fuyingedu.training.admin.model.order.alloc.AllocReq;
import com.fuyingedu.training.admin.model.order.alloc.AllocResp;
import com.fuyingedu.training.admin.model.order.alloc.UpdateTeacherReq;
import com.fuyingedu.training.admin.service.*;
import com.fuyingedu.training.common.annotation.Login;
import com.fuyingedu.training.common.annotation.PreAuthorize;
import com.fuyingedu.training.common.annotation.UserInfo;
import com.fuyingedu.training.common.model.CommResp;
import com.fuyingedu.training.common.util.RedisKey;
import com.fuyingedu.training.common.util.RespUtils;
import com.fuyingedu.training.dto.order.ContactListParam;
import com.fuyingedu.training.dto.order.OrderListParam;
import com.fuyingedu.training.front.service.FxyService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 服务单管理
 */
@RestController
@RequestMapping("admin/order")
@Slf4j
public class OrderController {

    @Autowired
    private OrderService orderService;
    @Autowired
    private OrderRecordService orderRecordService;
    @Autowired
    private UserRemarkService userRemarkService;
    @Autowired
    private SyncOrderManager syncOrderManager;
    @Autowired
    private HomeExportService homeExportService;
    @Autowired
    private OrderAllocService orderAllocService;
    @Autowired
    private FxyService fxyService;
    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    /**
     * 服务单列表
     */
    @PreAuthorize({"管理员", "普通管理员"})
    @GetMapping("list")
    public CommResp<List<ItemResp>> list(@Login UserInfo userInfo, OrderListParam listReq) {
        return orderService.list(userInfo, listReq);
    }

    /**
     * 服务单列表导出
     */
    @PreAuthorize({"管理员", "普通管理员"})
    @GetMapping("download")
    public void download(@Login UserInfo userInfo, OrderListParam listReq, HttpServletResponse response) throws IOException {
        List<ExportItemResp> exportList = orderService.exportList(userInfo, listReq);
        response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
        response.setHeader("Content-disposition", "attachment;filename=" + URLEncoder.encode("服务单列表", StandardCharsets.UTF_8) + ".xlsx");
        EasyExcel.write(response.getOutputStream(), ExportItemResp.class).sheet("服务单列表").doWrite(exportList);
    }

    /**
     * 批量分配导师
     */
    @PreAuthorize({"管理员", "普通管理员"})
    @PostMapping("teacher/alloc")
    public CommResp<List<AllocResp>> alloc(@Login Long userId, @RequestBody @Valid AllocReq allocReq) {
        return orderAllocService.alloc(userId, allocReq);
    }

    /**
     * 通过Excel批量分配导师
     */
    @PreAuthorize({"管理员", "普通管理员"})
    @PostMapping("excel/alloc")
    public CommResp<?> allocByExcel(@Login Long userId, @RequestParam("file") MultipartFile file) {
        return orderAllocService.allocByExcel(userId, file);
    }

    /**
     * 导师端-学员课前联系列表
     */
    @GetMapping("contact/list")
    public CommResp<List<ContactItemResp>> contactList(@Login Long userId, ContactListParam listReq) {
        return orderService.contactList(userId, listReq);
    }

    /**
     * 导师端-学员课前联系列表导出
     */
    @GetMapping("contact/download")
    public void contactDownload(@Login Long userId, ContactListParam listReq, HttpServletResponse response) throws IOException {
        listReq.setPageNum(1);
        listReq.setPageSize(5000);
        List<ContactItemResp> listCommResp = orderService.contactList(userId, listReq).getData();
        response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
        response.setHeader("Content-disposition", "attachment;filename=" + URLEncoder.encode("学员课前联系列表", StandardCharsets.UTF_8) +".xlsx");
        EasyExcel.write(response.getOutputStream(), ContactItemResp.class).sheet("学员课前联系列表").doWrite(listCommResp);
    }

    /**
     * 导出作业数据
     */
    @GetMapping("homework/download")
    public void homeworkDownload(@Login Long userId, RecordListReq listReq, HttpServletResponse response) throws IOException {
        response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
        response.setHeader("Content-disposition", "attachment;filename=" + URLEncoder.encode("学员作业数据列表", StandardCharsets.UTF_8) +".xlsx");
        homeExportService.export(listReq, response);
    }

    /**
     * 服务单详情
     */
    @GetMapping("detail")
    public CommResp<DetailResp> detail(Long id) {
        return orderService.detail(id);
    }

    /**
     * 分配老师数量统计
     */
    @GetMapping("count")
    public CommResp<CountResp> count(Long scheduleId) {
        return orderService.count(scheduleId);
    }

    /**
     * 下载分配结果
     */
    @GetMapping("excel/download")
    public void download(@RequestParam("key") String key, HttpServletResponse response) throws IOException {
        response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
        response.setHeader("Content-disposition", "attachment;filename=" + URLEncoder.encode("分配失败列表", StandardCharsets.UTF_8) +".xlsx");
        orderAllocService.downloadFailList(key, response);
    }

    /**
     * 单个分配导师
     */
    @PostMapping("teacher/update")
    public CommResp<?> updateTeacher(@Login Long userId, @RequestBody @Valid UpdateTeacherReq updateTeacherReq) {
        orderAllocService.updateTeacher(userId, updateTeacherReq);
        return RespUtils.success();
    }

    /**
     * 修改排期
     */
    @PostMapping("schedule/update")
    public CommResp<?> updateSchedule(@Login Long userId, @RequestBody @Valid UpdateScheduleReq updateScheduleReq) {
        orderService.updateSchedule(userId, updateScheduleReq);
        return RespUtils.success();
    }

    /**
     * 个人分班（改班）
     */
    @PostMapping("clazz/update")
    public CommResp<?> updateClazz(@Login Long userId, @RequestBody @Valid UpdateClazzReq updateClazzReq) {
        orderService.updateClazz(userId, updateClazzReq);
        return RespUtils.success();
    }

    /**
     * 个人分组（改组）
     */
    @PostMapping("group/update")
    public CommResp<?> updateGroup(@Login Long userId, @RequestBody @Valid UpdateGroupReq updateGroupReq) {
        orderService.updateGroup(userId, updateGroupReq);
        return RespUtils.success();
    }

    /**
     * 学员学习管理列表
     */
    @GetMapping("record/list")
    public CommResp<List<RecordItemResp>> recordList(@Login Long userId, @Valid RecordListReq listReq) {
        return orderService.recordList(listReq);
    }

    /**
     * PC端-用户查找
     */
    @GetMapping("search")
    public CommResp<List<SearchItemResp>> searchUser(SearchReq listReq) {
        return orderService.searchUser(listReq);
    }

    /**
     * 给学员加分
     * @param userId 前端不需要传
     */
    @PostMapping("reward/add")
    public CommResp<?> addReward(@Login Long userId, @RequestBody @Valid AddRewardReq addRewardReq) {
        orderRecordService.addReward(userId, addRewardReq);
        return RespUtils.success();
    }

    /**
     * 给学员添加备注
     * @param userId 前端不需要传
     */
    @PostMapping("remark/add")
    public CommResp<?> addRemark(@Login Long userId, @RequestBody @Valid AddRemarkReq addRemarkReq) {
        userRemarkService.addRemark(userId, addRemarkReq);
        return RespUtils.success();
    }

    /**
     * 查看服务单的备注列表
     */
    @GetMapping("remarks")
    public CommResp<List<RemarkResp>> remarkList(@RequestParam("id") Long id,
                                                 @RequestParam(value = "scheduleId", required = false) Long scheduleId) {
        return userRemarkService.remarkList(id, scheduleId);
    }

    /**
     * 关闭服务单
     */
    @PostMapping("close")
    public CommResp<?> close(@Login Long userId, @RequestBody @Valid CloseReq closeReq) {
        orderService.close(userId, closeReq);
        return RespUtils.success();
    }

    /**
     * 生成服务单
     */
    @PostMapping("generate")
    public CommResp<?> generate(@RequestBody @Valid List<OrderReq> orderList) {
        syncOrderManager.generateOrder(orderList);
        return RespUtils.success();
    }

    /**
     * 服务单用户备注
     */
    @PostMapping("user/remark")
    public CommResp<?> addUserRemark(@Login Long userId, @RequestBody @Valid RemarkReq remarkReq) {
        orderService.addUserRemark(remarkReq);
        return RespUtils.success();
    }

    /**
     * 服务单核销
     * 传orderId列表
     */
    @PostMapping("sign")
    public CommResp<?> sign(@Login Long userId, @RequestBody @Valid List<Long> orderIds) {
        String key = String.format(RedisKey.SIGN_LOCK, userId);
        Boolean b = redisTemplate.opsForValue().setIfAbsent(key, "1", 5, TimeUnit.MINUTES);
        if (Boolean.FALSE.equals(b)) {
            return RespUtils.warning(500, "服务单核销中，该任务执行时间较长，请刷新页面查看核销结果");
        }
        try {
            return orderService.sign(userId, orderIds);
        } finally {
            redisTemplate.delete(key);
        }
    }

    /**
     * 解绑扶小鹰
     */
    @PostMapping("fxy/unbind")
    public CommResp<?> unbindFxy(@Login Long userId, @RequestParam("orderId") Long orderId) {
        return fxyService.unbindFxy(userId, orderId);
    }

    /**
     * 修改服务单状态
     */
    @PostMapping("status/update")
    public CommResp<?> updateStatus(@Login Long userId, @RequestBody @Valid UpdateStatusReq req) {
        orderService.updateStatus(userId, req);
        return RespUtils.success();
    }
}
