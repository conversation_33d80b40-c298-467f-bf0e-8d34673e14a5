package com.fuyingedu.training.admin.controller;

import com.fuyingedu.training.admin.model.task.template.*;
import com.fuyingedu.training.admin.service.TaskTemplateService;
import com.fuyingedu.training.common.annotation.Login;
import com.fuyingedu.training.common.annotation.PreAuthorize;
import com.fuyingedu.training.common.annotation.UserInfo;
import com.fuyingedu.training.common.model.CommResp;
import com.fuyingedu.training.common.util.RespUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 任务模板管理
 */
@RestController
@Slf4j
@RequestMapping("admin/task/template")
public class TaskTemplateController {

    @Autowired
    private TaskTemplateService taskTemplateService;

    /**
     * 列表查询
     */
    @PreAuthorize({"管理员", "普通管理员"})
    @GetMapping("list")
    public CommResp<List<ItemResp>> list(@Login UserInfo userInfo, ListReq listReq) {
        return taskTemplateService.list(userInfo, listReq);
    }

    /**
     * 添加/编辑模板
     */
    @PreAuthorize({"管理员", "普通管理员"})
    @PostMapping("save")
    public CommResp<?> addOrEdit(@RequestBody SaveReq saveReq) {
        taskTemplateService.addOrEdit(saveReq);
        return RespUtils.success();
    }

    /**
     * 根据类型查询任务列表
     * @param type 任务类型 1-签到 2-打卡 3-作业
     */
    @GetMapping("type/list")
    public CommResp<List<TypeItemResp>> typeList(@RequestParam("scheduleId") Long scheduleId,
                                                 @RequestParam("type") Byte type) {
        return taskTemplateService.typeList(scheduleId, type);
    }

    /**
     * 任务模板详情
     */
    @GetMapping("detail")
    public CommResp<DetailResp> detail(@RequestParam(value = "id") Long id) {
        return taskTemplateService.detail(id);
    }

}
