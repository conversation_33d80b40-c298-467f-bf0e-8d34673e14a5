package com.fuyingedu.training.admin.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.fasterxml.jackson.databind.JsonNode;
import com.fuyingedu.training.common.model.CommResp;
import com.fuyingedu.training.common.util.DateUtils;
import com.fuyingedu.training.common.util.JsonUtils;
import com.fuyingedu.training.common.util.RedisKey;
import com.fuyingedu.training.common.util.RespUtils;
import com.fuyingedu.training.entity.*;
import com.fuyingedu.training.front.manager.BaiJiaYunManager;
import com.fuyingedu.training.front.manager.RewardManager;
import com.fuyingedu.training.mapper.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
public class LiveSignService {

    @Autowired
    private LiveSignMapper liveSignMapper;
    @Autowired
    private LiveSignRecordMapper liveSignRecordMapper;
    @Autowired
    private BaiJiaYunManager baiJiaYunManager;
    @Autowired
    private ScheduleMapper scheduleMapper;
    @Autowired
    private LiveMapper liveMapper;
    @Autowired
    private RewardManager rewardManager;
    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private LiveQuizRecordMapper liveQuizRecordMapper;
    @Autowired
    private LiveQuizConfigMapper liveQuizConfigMapper;
    @Autowired
    private LiveQuizRelationMapper liveQuizRelationMapper;
    @Autowired
    private LiveAnswerMapper liveAnswerMapper;
    @Autowired
    private LiveAnswerRecordMapper liveAnswerRecordMapper;
    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    public CommResp<?> callback(String messageBody) {
        log.info("接收到回调消息: {}", messageBody);
        JsonNode jsonNode = JsonUtils.parseJsonToObj(messageBody, JsonNode.class);
        Long roomId = jsonNode.get("room_id").asLong();
        String op = jsonNode.get("op").asText();
//        if ("end".equals(op)) {
//            Schedule schedule = scheduleMapper.selectOne(new QueryWrapper<Schedule>().select(
//                    Schedule.ID, Schedule.LIVE_TYPE, Schedule.LIVE_PORT, Schedule.LIVE_ROOM
//            ).eq(Schedule.LIVE_ROOM, roomId));
//            syncReward(schedule);
//        }
        return RespUtils.success();
    }

    public CommResp<?> syncReward(Long scheduleId, Long liveId) {
        Schedule schedule = scheduleMapper.selectOne(new QueryWrapper<Schedule>().select(
                Schedule.ID, Schedule.LIVE_TYPE, Schedule.LIVE_PORT, Schedule.LIVE_ROOM
        ).eq(Schedule.ID, scheduleId));
        return syncReward(schedule, liveId);
    }

    public CommResp<?> syncReward(Schedule schedule, Long liveId) {
        Long scheduleId = schedule.getId();
        if (schedule.getLiveRoom() == null) {
            log.info("scheduleId:{}没有配置直播教室", scheduleId);
            return RespUtils.success();
        }
        if (!baiJiaYunManager.isBaiJiaYun(schedule)) {
            log.info("scheduleId:{}不是百家云直播", scheduleId);
            return RespUtils.success();
        }
        Live live = liveMapper.selectOne(new QueryWrapper<Live>().select(
                Live.ID, Live.START_TIME, Live.LIVE_DURATION, Live.LIVE_NAME
        ).eq(Live.ID, liveId));
        if (live == null) {
            log.info("scheduleId:{}没有配置直播", scheduleId);
            return RespUtils.success();
        }
        String key = String.format(RedisKey.SYNC_BJY_LOCK, scheduleId);
        Boolean b = redisTemplate.opsForValue().setIfAbsent(key, "", 10, TimeUnit.MINUTES);
        if (b == null || !b) {
            log.info("scheduleId:{}已经在同步中", scheduleId);
            return RespUtils.success();
        }
        try {
            syncSign(schedule, live);
//            syncLiveQuiz(schedule, live);
            syncAnswer(schedule, live);
        } finally {
            redisTemplate.delete(key);
        }
        return RespUtils.success();
    }

    private void syncSign(Schedule schedule, Live live) {
        LiveSign liveSign = liveSignMapper.selectOne(new QueryWrapper<LiveSign>().select(
                        LiveSign.ID, LiveSign.LIVE_ID, LiveSign.FXY_REWARD, LiveSign.TASK_REWARD, LiveSign.WORD_REWARD
                ).eq(LiveSign.SIGN_STATUS, (byte) 2).eq(LiveSign.SYNC_STATUS, (byte) 1)
                .eq(LiveSign.LIVE_ID, live.getId()));
        if (liveSign == null) {
            log.info("liveId:{}没有配置签到", live.getId());
            return;
        }
        JsonNode jsonNode = baiJiaYunManager.checkinInfo(schedule.getLiveRoom());
        JsonNode checkInList = jsonNode.get("checkin_list");
        for (JsonNode checkIn : checkInList) {
            LocalDateTime checkInTime = DateUtils.parseDateTime(checkIn.get("time").asText());
            LocalDateTime startTime = live.getStartTime();
            LocalDateTime endTime = startTime.plusMinutes(live.getLiveDuration());
            if (checkInTime.isAfter(startTime) && checkInTime.isBefore(endTime)) {
                handleSign(liveSign, live, checkIn.get("user_list"));
            }
        }
    }

    private void syncLiveQuiz(Schedule schedule, Live live) {
        LiveQuizConfig config = liveQuizConfigMapper.selectOne(new QueryWrapper<LiveQuizConfig>().select(
                LiveQuizConfig.LIVE_ID, LiveQuizConfig.TASK_REWARD, LiveQuizConfig.FXY_REWARD, LiveQuizConfig.WORD_REWARD
        ).eq(LiveQuizConfig.LIVE_ID, live.getId()));
        LiveQuizRelation relation = liveQuizRelationMapper.selectOne(new QueryWrapper<LiveQuizRelation>().select(
                LiveQuizRelation.ID, LiveQuizRelation.LIVE_ID, LiveQuizRelation.ROOM_ID, LiveQuizRelation.QUIZ_ID
        ).eq(LiveQuizRelation.LIVE_ID, live.getId()).eq(LiveQuizRelation.SYNC_STATUS, 1));
        if (config == null || relation == null) {
            log.info("liveId:{}没有配置答题", live.getId());
            return;
        }
        JsonNode jsonNode = baiJiaYunManager.questionList(relation.getRoomId(), relation.getQuizId());
        JsonNode answerList = jsonNode.get("student_answer_list");
        if (answerList == null || answerList.isEmpty()) {
            log.info("roomId:{}没有答题", relation.getRoomId());
            return ;
        }
        Set<Long> orderIdSet = liveQuizRecordMapper.selectList(new QueryWrapper<LiveQuizRecord>().select(
                        LiveQuizRecord.ID, LiveQuizRecord.ORDER_ID
                ).eq(LiveQuizRecord.RELATION_ID, relation.getId()))
                .stream().map(LiveQuizRecord::getOrderId).collect(Collectors.toSet());
        for (JsonNode answer : answerList) {
            Long orderId = answer.get("user_number").asLong();
            int totalScore = answer.get("total_score").asInt();
            if (!orderIdSet.contains(orderId)) {
                Order order = orderMapper.selectOne(new QueryWrapper<Order>().select(
                        Order.ID, Order.USER_ID
                ).eq(Order.ID, orderId));
                rewardManager.saveReward(orderId, order.getUserId(), order.getUserId(),
                        config.getTaskReward() * totalScore, config.getFxyReward() * totalScore,
                        config.getWordReward() * totalScore,
                        String.format("直播答题奖励[%d]", relation.getId()));
                LiveQuizRecord liveQuizRecord = new LiveQuizRecord();
                liveQuizRecord.setRelationId(relation.getId());
                liveQuizRecord.setOrderId(orderId);
                liveQuizRecordMapper.insert(liveQuizRecord);
            }
        }
        liveQuizRelationMapper.update(new UpdateWrapper<LiveQuizRelation>()
                .set(LiveQuizRelation.SYNC_STATUS, 2).eq(LiveQuizRelation.ID, relation.getId()));
    }

    private void syncAnswer(Schedule schedule, Live live) {
        LiveAnswer liveAnswer = liveAnswerMapper.selectOne(new QueryWrapper<LiveAnswer>().select(
                        LiveAnswer.ID, LiveAnswer.LIVE_ID, LiveAnswer.FXY_REWARD,
                        LiveAnswer.TASK_REWARD, LiveAnswer.WORD_REWARD
                ).eq(LiveAnswer.LIVE_ID, live.getId()).eq(LiveAnswer.SYNC_STATUS, 1)
                .eq(LiveAnswer.ANSWER_STATUS, 2));
        if (liveAnswer == null) {
            log.info("liveId:{}没有配置答题器", live.getId());
            return;
        }
        JsonNode jsonNode = baiJiaYunManager.roomAnswer(schedule.getLiveRoom(), live.getStartTime().toLocalDate());
        Map<Long, Integer> answerCountMap = new HashMap<>();
        JsonNode dataList = jsonNode.get("list");
        if (dataList == null) {
            log.info("roomId:{}没有答题", schedule.getLiveRoom());
            return ;
        }
        Set<Long> syncOrderIds = liveAnswerRecordMapper.selectList(new QueryWrapper<LiveAnswerRecord>().select(
                        LiveAnswerRecord.ID, LiveAnswerRecord.ORDER_ID
                ).eq(LiveAnswerRecord.RELATION_ID, live.getId()))
               .stream().map(LiveAnswerRecord::getOrderId).collect(Collectors.toSet());
        for (JsonNode node : dataList) {
            JsonNode studentList = node.get("student_answer");
            if (studentList == null) {
                continue;
            }
            for (JsonNode student : studentList) {
                Long orderId = student.get("user_number").asLong();
                boolean isRight = student.get("is_right").asBoolean(false);
                if (isRight) {
                    int answerCount = answerCountMap.getOrDefault(orderId, 0);
                    answerCount++;
                    answerCountMap.put(orderId, answerCount);
                }
            }
        }
        for (Map.Entry<Long, Integer> entry : answerCountMap.entrySet()) {
            Long orderId = entry.getKey();
            int answerCount = entry.getValue();
            Order order = orderMapper.selectOne(new QueryWrapper<Order>().select(
                    Order.ID, Order.USER_ID
            ).eq(Order.ID, orderId));
            if (order == null) {
                log.warn("orderId:{}没有订单", orderId);
                continue;
            }
            if (syncOrderIds.contains(orderId)) {
                continue;
            }
            rewardManager.saveReward(orderId, order.getUserId(), order.getUserId(),
                    liveAnswer.getTaskReward() * answerCount,
                    liveAnswer.getFxyReward() * answerCount,
                    liveAnswer.getWordReward() * answerCount,
                    "直播答题正确");
            LiveAnswerRecord liveAnswerRecord = new LiveAnswerRecord();
            liveAnswerRecord.setRelationId(live.getId());
            liveAnswerRecord.setOrderId(orderId);
            liveAnswerRecord.setAnswerReward(liveAnswer.getWordReward() * answerCount);
            liveAnswerRecordMapper.insert(liveAnswerRecord);
        }
        liveAnswer.setSyncStatus((byte) 2);
        liveAnswerMapper.updateById(liveAnswer);
    }

    private void handleSign(LiveSign liveSign, Live live, JsonNode userList) {
        Set<Long> orderIdSet = liveSignRecordMapper.selectList(new QueryWrapper<LiveSignRecord>().select(
                LiveSignRecord.ORDER_ID
        ).eq(LiveSignRecord.LIVE_ID, live.getId()))
                .stream().map(LiveSignRecord::getOrderId).collect(Collectors.toSet());
        for (JsonNode node : userList) {
            Long orderId = node.get("user_number").asLong();
            int checkin = node.get("checkin").asInt();
            if (checkin == 0) {
                continue;
            }
            if (!orderIdSet.contains(orderId)) {
                Order order = orderMapper.selectOne(new QueryWrapper<Order>().select(
                        Order.ID, Order.USER_ID
                ).eq(Order.ID, orderId));
                if (order == null) {
                    log.warn("orderId:{}没有订单", orderId);
                    continue;
                }
                rewardManager.saveReward(orderId, order.getUserId(), order.getUserId(), liveSign.getTaskReward(),
                        liveSign.getFxyReward(), liveSign.getWordReward(), String.format("观看直播'%s'", live.getLiveName()));
                LiveSignRecord liveSignRecord = new LiveSignRecord();
                liveSignRecord.setLiveId(live.getId());
                liveSignRecord.setOrderId(orderId);
                liveSignRecordMapper.insert(liveSignRecord);
            }
        }
        liveSign.setSyncStatus((byte) 2);
        liveSignMapper.updateById(liveSign);
    }
}
