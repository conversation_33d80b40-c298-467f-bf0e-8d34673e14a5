package com.fuyingedu.training.admin.model.task;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Getter
@Setter
@ToString
public class StudentResp {

    /**
     * 单词训练营使用，1-直播 2-学练测 3-复习
     */
    private Byte wordTaskType;
    /***
     * 提交列表
     */
    private List<Clazz> committedList;

    /**
     * 未提交列表
     */
    private List<Clazz> unCommittedList;

    /**
     * 超时提交记录
     */
    private List<Clazz> timeoutList;

    @Getter
    @Setter
    public static class Clazz {
        private Long id;
        /**
         * 班级名称
         */
        private String clazzName;

        private Long teacherId;

        private String teacherName;
        /**
         * 分组列表
         */
        private List<Group> groupList;
    }

    @Getter
    @Setter
    public static class Group {
        private Long id;
        /**
         * 分组名称
         */
        private String groupName;
        /**
         * 学生列表
         */
        private List<Student> studentList;
    }

    @Getter
    @Setter
    public static class Student {
        private Long id;
        private Long orderId;
        /**
         * 用户头像
         */
        private String userIcon;

        /**
         * 姓名
         */
        private String realName;
        /**
         * 手机号
         */
        private Long phoneNo;

        /**
         * 错题总数
         */
        private Integer wrongQueCnt;

        /**
         * 错题有效数量
         */
        private Integer wrongQueValidCnt;
    }
}
