package com.fuyingedu.training.admin.model.task;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class RecordItemResp {

    private Long id;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 任务类型 1-签到 2-打卡 3-作业
     */
    private Byte taskType;

    /**
     * 状态 1-进行中 2-未开始 3-已完成
     */
    private Byte taskStatus;

    public Byte getTaskStatus() {
        if (currentDay < 0) {
            return 2;
        }
        if (currentDay > allDay) {
            return 3;
        }
        return 1;
    }

    /**
     * 完成的人数
     */
    private Integer doneNum;

    /**
     * 总人数
     */
    private Integer totalNum;

    /**
     * 当前天数
     */
    private Integer currentDay;
    /**
     * 总天数
     */
    private Integer allDay;
}
