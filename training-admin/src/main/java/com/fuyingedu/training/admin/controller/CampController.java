package com.fuyingedu.training.admin.controller;

import com.fuyingedu.training.admin.model.camp.DetailResp;
import com.fuyingedu.training.admin.model.camp.ItemResp;
import com.fuyingedu.training.admin.model.camp.ListReq;
import com.fuyingedu.training.admin.model.camp.SaveReq;
import com.fuyingedu.training.admin.service.CampService;
import com.fuyingedu.training.common.annotation.Login;
import com.fuyingedu.training.common.annotation.PreAuthorize;
import com.fuyingedu.training.common.annotation.UserInfo;
import com.fuyingedu.training.common.model.CommResp;
import com.fuyingedu.training.common.util.RespUtils;
import com.fuyingedu.training.front.model.feign.CampItem;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 训练营管理
 */
@RestController
@RequestMapping("admin/camp")
public class CampController {

    @Autowired
    private CampService campService;

    /**
     * 添加/编辑训练营
     */
    @PreAuthorize({"管理员", "普通管理员"})
    @PostMapping("save")
    public CommResp<?> addOrEdit(@RequestBody @Valid SaveReq saveReq) {
        campService.addOrEdit(saveReq);
        return RespUtils.success();
    }

    /**
     * 训练营列表
     */
    @PreAuthorize({"管理员", "普通管理员"})
    @GetMapping("list")
    public CommResp<List<ItemResp>> list(@Login UserInfo  userInfo, ListReq listReq) {
        return campService.list(userInfo, listReq);
    }

    /**
     * 训练营详情
     */
    @GetMapping("detail")
    public CommResp<DetailResp> detail(@RequestParam("id") Long campId) {
        return campService.detail(campId);
    }

    /**
     * 扶鹰系统的训练营名称
     */
    @GetMapping("fy/name/list")
    public CommResp<List<String>> fyCampNameList() {
        return campService.fyCampNameList();
    }

    /**
     * 扶鹰系统的训练营列表
     */
    @GetMapping("fy/list")
    public CommResp<List<CampItem>> fyCampList() {
        return campService.fyCampList();
    }
}
