package com.fuyingedu.training.admin.model.role;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Getter
@Setter
@ToString
public class SaveReq {

    private Long id;

    /**
     * 角色
     */
    @NotBlank
    private String roleName;

    /**
     * 描述
     */
    private String roleDesc;

    @NotEmpty
    private List<Long> resourceIds;
}
