package com.fuyingedu.training.admin.config;

import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalTimeSerializer;
import com.fuyingedu.training.common.util.DateUtils;
import org.springframework.boot.autoconfigure.jackson.Jackson2ObjectMapperBuilderCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class JacksonConfig {

    @Bean
    public Jackson2ObjectMapperBuilderCustomizer jackson2ObjectMapperBuilderCustomizer() {

        return builder -> {
            builder.deserializers(new LocalTimeDeserializer(DateUtils.TIME_FORMATTER));
            builder.deserializers(new LocalDateDeserializer(DateUtils.DATE_FORMATTER));
            builder.deserializers(new LocalDateTimeDeserializer(DateUtils.DATE_TIME_FORMATTER));

            builder.serializers(new LocalTimeSerializer(DateUtils.TIME_FORMATTER));
            builder.serializers(new LocalDateSerializer(DateUtils.DATE_FORMATTER));
            builder.serializers(new LocalDateTimeSerializer(DateUtils.DATE_TIME_FORMATTER));
        };
    }
}
