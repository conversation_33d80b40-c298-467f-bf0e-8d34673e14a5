package com.fuyingedu.training.admin.interceptor;

import com.fuyingedu.training.common.util.JsonUtils;
import com.fuyingedu.training.common.util.RespUtils;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

@Slf4j
@Component
public class InnerAuthInterceptor implements HandlerInterceptor {

    private static final String TOKEN = "3lasfjasldfjalafmnbvmnzbvlkze5184a3286";

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws IOException {
        String token = request.getHeader("token");
        boolean b = TOKEN.equals(token);
        if (!b) {
            response.setStatus(HttpStatus.OK.value());
            response.setContentType(MediaType.APPLICATION_JSON_VALUE);
            response.setCharacterEncoding(StandardCharsets.UTF_8.name());
            response.getWriter().write(JsonUtils.formatObjToJson(RespUtils.warning(500, "Token错误")));
        }
        return b;
    }

}
