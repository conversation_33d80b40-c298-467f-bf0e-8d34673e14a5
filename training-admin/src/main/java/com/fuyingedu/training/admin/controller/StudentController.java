package com.fuyingedu.training.admin.controller;

import com.fuyingedu.training.admin.model.student.*;
import com.fuyingedu.training.admin.service.StudentService;
import com.fuyingedu.training.common.annotation.Login;
import com.fuyingedu.training.common.model.CommResp;
import com.fuyingedu.training.common.util.RespUtils;
import com.fuyingedu.training.front.model.task.TaskRecordResp;
import com.fuyingedu.training.front.service.FrontTaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 学员管理
 */
@RestController
@RequestMapping("admin/student")
@Slf4j
public class StudentController {

    @Autowired
    private StudentService studentService;
    @Autowired
    private FrontTaskService frontTaskService;

    /**
     * 学员列表
     */
    @GetMapping("list")
    public CommResp<List<ItemResp>> list(ListReq listReq) {
        return studentService.list(listReq);
    }

    /**
     * 学员详情
     */
    @GetMapping("detail")
    public CommResp<DetailResp> detail(@RequestParam("id") Long id) {
        return studentService.detail(id);
    }

    /**
     * 删除备注
     */
    @PostMapping("remark/delete")
    public CommResp<?> deleteRemark(@Login Long userId, @RequestBody DeleteReq deleteReq) {
        studentService.deleteRemark(userId, deleteReq.getId());
        return RespUtils.success();
    }

    /**
     * PC端-学员详情
     */
    @GetMapping("info")
    public CommResp<InfoResp> info(@RequestParam("orderId") Long orderId) {
        return studentService.info(orderId);
    }

    /**
     * PC端-学员详情-任务列表
     * @param orderId 服务单ID
     * @param type 任务类型 1-签到 2-打卡 3-作业 5-接口打卡
     */
    @GetMapping("task/list")
    private CommResp<List<TaskItemResp>> taskList(@RequestParam("orderId") Long orderId, @RequestParam("type") Byte type) {
        return studentService.taskList(orderId, type);
    }

    /**
     * 签到记录
     * @param userId 前端不需要传
     */
    @GetMapping("enrollment/list")
    public CommResp<TaskRecordResp> enrollmentList(@Login Long userId,
                                                   @RequestParam("orderId") Long orderId, @RequestParam("taskId") Long taskId) {
        return frontTaskService.enrollmentList(orderId, taskId);
    }

    /**
     * 打卡记录
     * @param userId 前端不需要传
     * @param orderId 服务单ID
     * @param taskId 任务ID
     */
    @GetMapping("punch/list")
    public CommResp<TaskRecordResp> punchList(@Login Long userId,
                                              @RequestParam("orderId") Long orderId, @RequestParam("taskId") Long taskId) {
        return frontTaskService.punchList(orderId, taskId);
    }

    /**
     * 获取打卡详情
     */
    @GetMapping("punch/detail")
    public CommResp<com.fuyingedu.training.front.model.task.DetailResp> punchDetail(@RequestParam("id") Long id,
                                                                                    @RequestParam(value = "orderId", required = false) Long orderId) {
        return frontTaskService.punchDetail(id, orderId);
    }

    /**
     * 作业详情列表
     */
    @GetMapping("homework/list")
    public CommResp<List<com.fuyingedu.training.front.model.task.DetailResp>> homeworkList(@Login Long userId,
                                                                                           @RequestParam("orderId") Long orderId) {
        return frontTaskService.homeworkList(orderId);
    }

    /**
     * 作业详情
     */
    @GetMapping("homework/detail")
    public CommResp<com.fuyingedu.training.front.model.task.DetailResp> homeworkDetail(@Login Long userId, @RequestParam("orderId") Long orderId, @RequestParam("taskId") Long taskId) {
        return frontTaskService.homeworkDetail(orderId, taskId);
    }
}
