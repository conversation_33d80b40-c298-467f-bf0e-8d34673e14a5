package com.fuyingedu.training.admin.model.student;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.List;

@Getter
@Setter
@ToString
public class DetailResp {
    /**
     * 昵称
     */
    private String realName;

    /**
     * 证件类型 1-身份证 2-护照 3-港澳通行证
     */
    private Byte cardType;

    /**
     * 证件号
     */
    private String cardNum;

    /**
     * 备注列表
     */
    private List<Remark> remarkList;

    /**
     * 课程列表
     */
    private List<Camp> campList;

    @Getter
    @Setter
    public static class Remark {

        private Long id;

        /**
         * 评论内容
         */
        private String content;

        /**
         * 评论人
         */
        private String realName;

        /**
         * 评论时间
         */
        private LocalDateTime remarkTime;
        /**
         * 图片
         */
        private List<String> imageUrlList;
        /**
         * 视频
         */
        private List<String> videoUrlList;
    }

    @Getter
    @Setter
    public static class Camp {

        private Long id;

        /**
         * 课程名称
         */
        private String campName;

        /**
         * 排期名称
         */
        private String scheduleName;

        /**
         * 1-未开始 2-进行中 3-已结束
         */
        private Byte scheduleStatus;
    }
}
