package com.fuyingedu.training.admin.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fuyingedu.training.common.util.AsyncUtils;
import com.fuyingedu.training.common.util.JsonUtils;
import com.fuyingedu.training.common.util.JwtUtils;
import com.fuyingedu.training.common.util.TransactionUtils;
import jakarta.annotation.PostConstruct;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;

import java.util.concurrent.ThreadPoolExecutor;

@Configuration
public class UtilsConfig {

    @Autowired
    private ObjectMapper objectMapper;
    @Autowired
    private ThreadPoolExecutor executor;
    @Autowired
    private TransactionDefinition transactionDefinition;
    @Autowired
    private PlatformTransactionManager platformTransactionManager;
    @Value("${training.secret}")
    private String secret;

    @PostConstruct
    public void init() {
        JsonUtils.init(objectMapper);
        AsyncUtils.init(executor);
        TransactionUtils.init(platformTransactionManager, transactionDefinition);
        JwtUtils.init(secret);
    }

}
