package com.fuyingedu.training.admin.model.task;

import com.fuyingedu.training.front.model.media.MediaResp;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.List;

@Getter
@Setter
@ToString
public class CommittedResp {

    /**
     * 点评过的记录列表
     */
    private List<Record> remarkRecordList;

    /**
     * 未点评的记录列表
     */
    private List<Record> unRemarkRecordList;

    @Getter
    @Setter
    public static class Record {

        private Long teacherId;
        private Long recordId;

        private LocalDateTime createdTime;
        /**
         * 学生名称
         */
        private String realName;
        /**
         * 头像
         */
        private String userIcon;
        private Long clazzId;
        /**
         * 班级名称
         */
        private String clazzName;

        private Long groupId;
        /**
         * 班级分组名称
         */
        private String groupName;

        /**
         * 文本信息
         */
        private String content;

        /**
         * 素材，存放JSON数组
         */
        private List<MediaResp> urlList;

        /**
         * 点赞数
         */
        private Integer likeNum;
        /**
         * 点评数
         */
        private Integer remarkNum;
        /**
         * 1-普通作业 2-优秀作业
         */
        private Byte recordType;

        /**
         * 点评列表
         */
        private List<Remark> remarkList;
    }

    @Getter
    @Setter
    public static class Remark {
        private Long id;

        /**
         * 点评的用户ID
         */
        private Long userId;

        /**
         *  点评的用户
         */
        private String realName;

        private String userIcon;

        /**
         * 点评时间
         */
        private LocalDateTime createdTime;
        /**
         * 点评内容
         */
        private String remarkContent;
        /**
         * 点评的素材
         */
        private List<MediaResp> remarkUrlList;
        /**
         * 点评的标签
         */
        private List<Label> labelList;
    }

    @Getter
    @Setter
    public static class Label {
        private Long id;
        /**
         * 标签名称
         */
        private String labelName;

        /**
         * 标签图
         */
        private String mediaUrl;
    }
}
