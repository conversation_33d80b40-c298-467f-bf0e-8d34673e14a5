package com.fuyingedu.training.admin.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fuyingedu.training.admin.model.teacher.*;
import com.fuyingedu.training.admin.model.user.UserConvertor;
import com.fuyingedu.training.common.enums.*;
import com.fuyingedu.training.common.exception.WebBaseException;
import com.fuyingedu.training.common.model.CommResp;
import com.fuyingedu.training.common.util.RespUtils;
import com.fuyingedu.training.entity.*;
import com.fuyingedu.training.front.feign.FuyingUserFeign;
import com.fuyingedu.training.front.model.feign.UnionItem;
import com.fuyingedu.training.front.model.feign.UserItem;
import com.fuyingedu.training.front.model.feign.UserReq;
import com.fuyingedu.training.mapper.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class TeacherService {

    @Autowired
    private TeacherMapper teacherMapper;
    @Autowired
    private AvailableCampService availableCampService;
    @Autowired
    private AvailableCampMapper availableCampMapper;
    @Autowired
    private UserMapper userMapper;
    @Autowired
    private ScheduleMapper scheduleMapper;
    @Autowired
    private ClazzMapper classMapper;
    @Autowired
    private FuyingUserFeign fuyingUserFeign;
    @Autowired
    private CampMapper campMapper;
    @Autowired
    private ScheduleTeacherMapper scheduleTeacherMapper;
    @Autowired
    private LiveMapper liveMapper;
    @Autowired
    private UserRoleMapper userRoleMapper;
    @Autowired
    private ScheduleTeacherStatisticService scheduleTeacherStatisticService;

    public CommResp<List<ItemResp>> list(ListReq listReq) {
        Long phoneNum = listReq.getPhoneNum();
        String realName = listReq.getTeacherInfo();
        Set<Long> userIds = Collections.emptySet();
        if (phoneNum != null) {
            userIds = userMapper.selectList(new QueryWrapper<User>()
                    .select(User.ID)
                    .eq(User.PHONE_NUM, phoneNum)
            ).stream().map(User::getId).collect(Collectors.toSet());
            if (userIds.isEmpty()) {
                return RespUtils.success(Collections.emptyList());
            }
        }
        IPage<Teacher> page = new Page<>(listReq.getPageNum(), listReq.getPageSize());
        page = teacherMapper.selectPage(page, new QueryWrapper<Teacher>()
                .select(
                        Teacher.ID, Teacher.USER_ID, Teacher.CAMP_NUM, Teacher.TEACHER_STATUS,
                        Teacher.REAL_NAME
                ).in(!userIds.isEmpty(), Teacher.USER_ID, userIds)
                .like(realName != null, Teacher.REAL_NAME, realName)
                .orderByDesc(Teacher.ID)
        );
        if (page.getRecords().isEmpty()) {
            return RespUtils.success(page.getCurrent(), page.getSize(), page.getTotal(), Collections.emptyList());
        }
        userIds = page.getRecords().stream().map(Teacher::getUserId).collect(Collectors.toSet());
        Map<Long, User> userMap = userMapper.selectList(new QueryWrapper<User>()
                .select(User.ID, User.NICK_NAME, User.UNION_ID, User.USER_ICON, User.PHONE_NUM)
                .in(User.ID, userIds)).stream().collect(Collectors.toMap(User::getId, user -> user));
        List<ItemResp> respList = new ArrayList<>(page.getRecords().size());
        for (Teacher teacher : page.getRecords()) {
            User user = userMap.get(teacher.getUserId());
            respList.add(toItemResp(teacher, user));
        }
        return RespUtils.success(page.getCurrent(), page.getSize(), page.getTotal(), respList);
    }

    public CommResp<List<ValidItemResp>> validList(Long scheduleId) {
        Schedule schedule = scheduleMapper.selectOne(new QueryWrapper<Schedule>().select(
                Schedule.ID,
                Schedule.CAMP_ID
        ).eq(Schedule.ID, scheduleId));
        if (schedule == null) {
            throw new WebBaseException(RespMetaEnum.PARAM_ERROR);
        }
        List<Long> teacherIds = availableCampMapper.selectList(new QueryWrapper<AvailableCamp>().select(
                AvailableCamp.AVAILABLE_ID
        ).eq(AvailableCamp.CAMP_ID, schedule.getCampId())
                        .eq(AvailableCamp.AVAILABLE_TYPE, AvailableType.TEACHER.getCode())
                        .eq(AvailableCamp.CAMP_STATUS, Status.NORMAL.getCode())
        ).stream().map(AvailableCamp::getAvailableId).toList();
        if (CollectionUtils.isEmpty(teacherIds)) {
            return RespUtils.success(Collections.emptyList());
        }
        Set<Long> addedTeacherIds = addedTeacherIds(scheduleId);
        teacherIds = teacherIds.stream().filter(teacherId -> !addedTeacherIds.contains(teacherId)).toList();
        if (CollectionUtils.isEmpty(teacherIds)) {
            return RespUtils.success(Collections.emptyList());
        }
        List<Teacher> teacherList = teacherMapper.selectList(new QueryWrapper<Teacher>().select(
                Teacher.ID,
                Teacher.REAL_NAME, Teacher.USER_ID
        ).in(Teacher.ID, teacherIds).eq(Teacher.TEACHER_STATUS, Status.NORMAL.getCode()));
        List<Long> userIds = teacherList.stream().map(Teacher::getUserId).toList();
        Map<Long, User> userMap = userMapper.selectList(new QueryWrapper<User>().select(
                User.ID,
                User.REAL_NAME,
                User.PHONE_NUM
        ).in(User.ID, userIds)).stream().collect(Collectors.toMap(User::getId, user -> user));
        List<ValidItemResp> respList = teacherList.stream().map(teacher -> {
            ValidItemResp validItemResp = toValidItemResp(teacher);
            User user = userMap.get(teacher.getUserId());
            validItemResp.setPhoneNum(user.getPhoneNum());
            return validItemResp;
        }).toList();
        return RespUtils.success(respList);
    }

    public Set<Long> addedTeacherIds(Long scheduleId) {
        return scheduleTeacherMapper.selectList(new QueryWrapper<ScheduleTeacher>().select(
                ScheduleTeacher.TEACHER_ID
        ).eq(ScheduleTeacher.SCHEDULE_ID, scheduleId)).stream().map(ScheduleTeacher::getTeacherId).collect(Collectors.toSet());
    }

    public CommResp<DetailResp> detail(Long id) {
        Teacher teacher = teacherMapper.selectOne(new QueryWrapper<Teacher>().select(
                Teacher.ID, Teacher.USER_ID, Teacher.TEACHER_STATUS
        ).eq(Teacher.ID, id));
        if (teacher == null) {
            throw new WebBaseException(RespMetaEnum.PARAM_ERROR);
        }
        User user = userMapper.selectOne(new QueryWrapper<User>().select(
                User.ID,
                User.UNION_ID,
                User.USER_ICON,
                User.PHONE_NUM,
                User.REAL_NAME
        ).eq(User.ID, teacher.getUserId()));
        DetailResp detailResp = toDetailResp(teacher, user);
        List<Long> campIds = availableCampService.getCampIds(id, AvailableType.TEACHER.getCode());
        detailResp.setCampIdList(campIds);
        return RespUtils.success(detailResp);
    }

    @Transactional(rollbackFor = Exception.class)
    public void addOrEdit(SaveReq saveReq) {
        User user = userMapper.selectOne(new QueryWrapper<User>().select(
                User.ID).eq(User.ID, saveReq.getOuterId()));
        Teacher oldTeacher = null;
        if (user != null) {
            oldTeacher = teacherMapper.selectOne(new QueryWrapper<Teacher>().select(
                    Teacher.ID,
                    Teacher.TEACHER_STATUS
            ).eq(Teacher.USER_ID, user.getId()));
        }
        List<Long> campIds = campMapper.selectList(new QueryWrapper<Camp>().select(
                Camp.ID
        ).in(Camp.ID, saveReq.getCampIdList())).stream().map(Camp::getId).toList();
        if (campIds.isEmpty()) {
            return;
        }
        saveReq.setCampIdList(campIds);
        if (oldTeacher == null) {
            add(user, saveReq);
        } else {
            if (saveReq.getId() == null || !oldTeacher.getId().equals(saveReq.getId())) {
                throw new WebBaseException(4000, "用户已是导师不能重复添加");
            }
            edit(user, oldTeacher, saveReq);
        }
    }

    private void edit(User user, Teacher teacher, SaveReq saveReq) {
        teacherMapper.update(new UpdateWrapper<Teacher>()
                    .set(Teacher.CAMP_NUM, saveReq.getCampIdList().size())
                    .set(Teacher.TEACHER_STATUS, saveReq.getTeacherStatus())
                    .eq(Teacher.ID, teacher.getId()));
        availableCampService.add(teacher.getId(), AvailableType.TEACHER.getCode(), saveReq.getCampIdList());
        UserRole userRole = userRoleMapper.selectOne(new QueryWrapper<UserRole>().select(
                UserRole.ID
        ).eq(UserRole.USER_ID, user.getId()).eq(UserRole.ROLE_ID, RoleType.TEACHER.getCode()));
        if (Status.DELETE.getCode().equals(saveReq.getTeacherStatus()) && userRole != null) {
            userRoleMapper.deleteById(userRole.getId());
        }
        if (Status.NORMAL.getCode().equals(saveReq.getTeacherStatus()) && userRole == null) {
            userRole = new UserRole();
            userRole.setUserId(user.getId());
            userRole.setRoleId(RoleType.TEACHER.getCode());
            userRoleMapper.insert(userRole);
        }
    }

    private void add(User user, SaveReq saveReq) {
        if (user == null) {
            user = UserConvertor.toUser(saveReq);
            userMapper.insert(user);
        }
        Teacher teacher = toTeacher(saveReq);
        teacher.setUserId(user.getId());
        teacherMapper.insert(teacher);
        UserRole userRole = new UserRole();
        userRole.setUserId(user.getId());
        userRole.setRoleId(RoleType.TEACHER.getCode());
        userRoleMapper.insert(userRole);
        availableCampService.add(teacher.getId(), AvailableType.TEACHER.getCode(), saveReq.getCampIdList());
    }

    private ItemResp toItemResp(Teacher teacher, User user) {
        ItemResp itemResp = new ItemResp();
        itemResp.setId(teacher.getId());
        itemResp.setCampNum(teacher.getCampNum());
        itemResp.setTeacherStatus(teacher.getTeacherStatus());
        itemResp.setRealName(teacher.getRealName());
        if (!StringUtils.hasLength(teacher.getRealName())) {
            itemResp.setRealName(user.getNickName());
        }
        itemResp.setNickName(user.getNickName());
        itemResp.setPhoneNum(user.getPhoneNum());
        itemResp.setUserIcon(user.getUserIcon());
        itemResp.setOuterId(user.getId());
        return itemResp;
    }

    private DetailResp toDetailResp(Teacher teacher, User user) {
        DetailResp detailResp = new DetailResp();
        detailResp.setId(teacher.getId());
        detailResp.setUserIcon(user.getUserIcon());
        detailResp.setRealName(user.getRealName());
        detailResp.setPhoneNum(user.getPhoneNum());
        detailResp.setUnionId(user.getUnionId());
        detailResp.setOuterId(user.getId());
        detailResp.setTeacherStatus(teacher.getTeacherStatus());
        return detailResp;
    }

    private Teacher toTeacher(SaveReq saveReq) {
        Teacher teacher = new Teacher();
        teacher.setId(saveReq.getId());
        teacher.setTeacherStatus(saveReq.getTeacherStatus());
        teacher.setRealName(saveReq.getRealName().trim());
        teacher.setCampNum(saveReq.getCampIdList().size());
        return teacher;
    }

    private ValidItemResp toValidItemResp(Teacher teacher) {
        ValidItemResp validItemResp = new ValidItemResp();
        validItemResp.setId(teacher.getId());
        validItemResp.setRealName(teacher.getRealName());
        return validItemResp;
    }

    public CommResp<SearchResp> search(Long phoneNum) {
        UserReq userReq = new UserReq();
        userReq.setPhone(String.valueOf(phoneNum));
        userReq.setCountryCode("86");
        UserItem userInfo = fuyingUserFeign.getUserByPhone(userReq);
        if (userInfo == null) {
            return RespUtils.success(null);
        }
        UnionItem unionItem = fuyingUserFeign.getUnionId(userInfo.getUid());
        String unionId = null;
        if (unionItem != null) {
            unionId = unionItem.getThirdAuthId();
        }
        return RespUtils.success(toSearchResp(userInfo, phoneNum, unionId));
    }

    private SearchResp toSearchResp(UserItem userInfo, Long phoneNum, String unionId) {
        SearchResp searchResp = new SearchResp();
        searchResp.setUserIcon(userInfo.getAvatarUrl());
        searchResp.setRealName(userInfo.getNickname());
        searchResp.setPhoneNum(phoneNum);
        searchResp.setOuterId(userInfo.getUid());
        searchResp.setUnionId(unionId);
        return searchResp;
    }

    public CommResp<InfoResp> info(Long userId) {
        Teacher teacher = teacherMapper.selectOne(new QueryWrapper<Teacher>().select(
                Teacher.ID,
                Teacher.REAL_NAME,
                Teacher.WX_URL
        ).eq(Teacher.USER_ID, userId));
        User user = userMapper.selectOne(new QueryWrapper<User>().select(
                User.PHONE_NUM
        ).eq(User.ID, userId));
        InfoResp infoResp = toInfoResp(teacher);
        infoResp.setPhoneNum(user.getPhoneNum());
        return RespUtils.success(infoResp);
    }

    private InfoResp toInfoResp(Teacher teacher) {
        InfoResp infoResp = new InfoResp();
        infoResp.setRealName(teacher.getRealName());
        return infoResp;
    }

    public CommResp<List<CampItemResp>> campList(Long userId) {
        Teacher teacher = teacherMapper.selectOne(new QueryWrapper<Teacher>().select(
                Teacher.ID
        ).eq(Teacher.USER_ID, userId));
        List<Long> campIds = availableCampMapper.selectList(new QueryWrapper<AvailableCamp>().select(
                                AvailableCamp.CAMP_ID
                        ).eq(AvailableCamp.AVAILABLE_ID, teacher.getId())
                        .eq(AvailableCamp.AVAILABLE_TYPE, AvailableType.TEACHER.getCode())
                        .eq(AvailableCamp.CAMP_STATUS, Status.NORMAL.getCode())
        ).stream().map(AvailableCamp::getCampId).toList();
        if (CollectionUtils.isEmpty(campIds)) {
            return RespUtils.success(Collections.emptyList());
        }
        List<Camp> campList = campMapper.selectList(new QueryWrapper<Camp>().select(
                Camp.ID, Camp.CAMP_NAME
        ).in(Camp.ID, campIds));
        List<CampItemResp> campItemRespList = campList.stream().map(camp -> {
            CampItemResp campItemResp = new CampItemResp();
            campItemResp.setCampId(camp.getId());
            campItemResp.setCampName(camp.getCampName());
            return campItemResp;
        }).toList();
        return RespUtils.success(campItemRespList);
    }

    public CommResp<List<ScheduleItemResp>> scheduleList(Long userId) {
        Teacher teacher = teacherMapper.selectOne(new QueryWrapper<Teacher>().select(
                Teacher.ID
        ).eq(Teacher.USER_ID, userId));
        List<ScheduleTeacher> scheduleTeacherList = scheduleTeacherMapper.selectList(new QueryWrapper<ScheduleTeacher>().select(
                ScheduleTeacher.SCHEDULE_ID, ScheduleTeacher.TEACHER_TYPE
        ).eq(ScheduleTeacher.TEACHER_ID, teacher.getId()));
        if (CollectionUtils.isEmpty(scheduleTeacherList)) {
            return RespUtils.success(Collections.emptyList());
        }
        List<Schedule> scheduleList = scheduleMapper.selectList(new QueryWrapper<Schedule>().select(
                Schedule.ID, Schedule.CAMP_ID, Schedule.SCHEDULE_NAME,
                Schedule.START_TIME, Schedule.END_TIME
        ).in(Schedule.ID, scheduleTeacherList.stream().map(ScheduleTeacher::getScheduleId).toList()).orderByDesc(Schedule.ID));
        List<Long> campIds = scheduleList.stream().map(Schedule::getCampId).toList();
        Map<Long, Camp> campMap = campMapper.selectList(new QueryWrapper<Camp>().select(
                Camp.ID, Camp.CAMP_NAME
        ).in(Camp.ID, campIds)).stream().collect(Collectors.toMap(Camp::getId, Function.identity()));
        LocalDateTime now = scheduleMapper.queryNow();
        Map<Long, Byte> teacherTypeMap = scheduleTeacherList.stream()
                .collect(Collectors.toMap(ScheduleTeacher::getScheduleId, ScheduleTeacher::getTeacherType));
        return RespUtils.success(scheduleList.stream().map(schedule -> {
            Camp camp = campMap.get(schedule.getCampId());
            ScheduleItemResp scheduleItemResp = toScheduleItemResp(now, schedule, camp);
            scheduleItemResp.setTeacherType(teacherTypeMap.get(schedule.getId()));
            return scheduleItemResp;
        }).toList());
    }

    private ScheduleItemResp toScheduleItemResp(LocalDateTime now, Schedule schedule, Camp camp) {
        ScheduleItemResp scheduleItemResp = new ScheduleItemResp();
        scheduleItemResp.setScheduleStatus(ScheduleStatus.convert(schedule.getStartTime(), schedule.getEndTime(), now).getCode());
        scheduleItemResp.setCampId(schedule.getCampId());
        scheduleItemResp.setCampName(camp.getCampName());
        scheduleItemResp.setScheduleName(schedule.getScheduleName());
        scheduleItemResp.setScheduleId(schedule.getId());
        return scheduleItemResp;
    }

    public CommResp<ScheduleDetailResp> scheduleDetail(Long userId, Long scheduleId) {
        Teacher teacher = teacherMapper.selectOne(new QueryWrapper<Teacher>().select(
                Teacher.ID
        ).eq(Teacher.USER_ID, userId));
        ScheduleTeacher scheduleTeacher = scheduleTeacherMapper.selectOne(new QueryWrapper<ScheduleTeacher>().select(
                ScheduleTeacher.TEACHER_TYPE
        ).eq(ScheduleTeacher.SCHEDULE_ID, scheduleId).eq(ScheduleTeacher.TEACHER_ID, teacher.getId()));
        if (scheduleTeacher == null) {
            throw new WebBaseException(RespMetaEnum.PARAM_ERROR);
        }
        Schedule schedule = scheduleMapper.selectOne(new QueryWrapper<Schedule>().select(
                Schedule.ID, Schedule.CAMP_ID, Schedule.SCHEDULE_NAME,
                Schedule.START_TIME, Schedule.END_TIME
        ).eq(Schedule.ID, scheduleId));
        Camp camp = campMapper.selectOne(new QueryWrapper<Camp>().select(
                Camp.ID, Camp.CAMP_NAME, Camp.CAMP_TYPE, Camp.CAMP_CONTENT
        ).eq(Camp.ID, schedule.getCampId()));
        return RespUtils.success(toScheduleDetailResp(schedule, camp, scheduleTeacher.getTeacherType()));
    }

    public ScheduleDetailResp toScheduleDetailResp(Schedule schedule, Camp camp, Byte teacherType) {
        ScheduleDetailResp scheduleDetailResp = new ScheduleDetailResp();
        scheduleDetailResp.setCampId(schedule.getCampId());
        scheduleDetailResp.setCampName(camp.getCampName());
        scheduleDetailResp.setScheduleName(schedule.getScheduleName());
        scheduleDetailResp.setScheduleId(schedule.getId());
        scheduleDetailResp.setStartTime(schedule.getStartTime());
        scheduleDetailResp.setEndTime(schedule.getEndTime());
        scheduleDetailResp.setTeacherType(teacherType);
        scheduleDetailResp.setCampType(camp.getCampType());
        scheduleDetailResp.setCampContent(camp.getCampContent());
        return scheduleDetailResp;
    }

    public CommResp<List<AssistantItemResp>> assistantList(Long userId, Long scheduleId) {
        List<Long> teacherIds = scheduleTeacherMapper.selectList(new QueryWrapper<ScheduleTeacher>().select(
                ScheduleTeacher.TEACHER_ID
        ).eq(ScheduleTeacher.SCHEDULE_ID, scheduleId).eq(ScheduleTeacher.TEACHER_TYPE, TeacherType.ASSISTANT.getCode()))
                .stream().map(ScheduleTeacher::getTeacherId).toList();
        if (CollectionUtils.isEmpty(teacherIds)) {
            return RespUtils.success(Collections.emptyList());
        }
        List<Teacher> teacherList = teacherMapper.selectList(new QueryWrapper<Teacher>().select(
                Teacher.ID, Teacher.REAL_NAME, Teacher.USER_ID
        ).in(Teacher.ID, teacherIds));
        Map<Long, User> userMap = userMapper.selectList(new QueryWrapper<User>().select(
                User.ID, User.PHONE_NUM
        ).in(User.ID, teacherList.stream().map(Teacher::getUserId).toList())).stream().collect(Collectors.toMap(User::getId, Function.identity()));
        Map<Long, List<Clazz>> teacherMap = classMapper.selectList(new QueryWrapper<Clazz>().select(
                        Clazz.ID, Clazz.ASSISTANT_ID
                ).in(Clazz.ASSISTANT_ID, teacherIds).eq(Clazz.SCHEDULE_ID, scheduleId))
                .stream().collect(Collectors.groupingBy(Clazz::getAssistantId));
        List<AssistantItemResp> respList = teacherList.stream().map(teacher -> {
            AssistantItemResp assistantItemResp = new AssistantItemResp();
            assistantItemResp.setAssistantId(teacher.getId());
            assistantItemResp.setAssistantName(teacher.getRealName());
            assistantItemResp.setClazzNum(teacherMap.getOrDefault(teacher.getId(), Collections.emptyList()).size());
            User user = userMap.get(teacher.getUserId());
            assistantItemResp.setPhoneNum(user.getPhoneNum());
            return assistantItemResp;
        }).toList();
        return RespUtils.success(respList);
    }

    public CommResp<List<CourseItemResp>> courseList(Long userId) {
        Teacher teacher = teacherMapper.selectOne(new QueryWrapper<Teacher>().select(
                Teacher.ID
        ).eq(Teacher.USER_ID, userId));
        if (teacher == null) {
            return RespUtils.success(Collections.emptyList());
        }
        Map<Long, ScheduleTeacher> scheduleTeacherMap = scheduleTeacherMapper.selectList(new QueryWrapper<ScheduleTeacher>().select(
                ScheduleTeacher.SCHEDULE_ID, ScheduleTeacher.TEACHER_TYPE
        ).eq(ScheduleTeacher.TEACHER_ID, teacher.getId())).stream().collect(Collectors.toMap(ScheduleTeacher::getScheduleId, Function.identity()));
        if (CollectionUtils.isEmpty(scheduleTeacherMap)) {
            return RespUtils.success(Collections.emptyList());
        }
        List<Schedule> scheduleList = scheduleMapper.selectList(new QueryWrapper<Schedule>().select(
                Schedule.ID, Schedule.START_TIME, Schedule.END_TIME
        ).in(Schedule.ID, scheduleTeacherMap.keySet()));
        LocalDateTime now = scheduleMapper.queryNow();
        Map<Long, List<Clazz>> clazzMap = classMapper.selectList(new QueryWrapper<Clazz>().select(
                Clazz.ID, Clazz.SCHEDULE_ID
        ).eq(Clazz.TEACHER_ID, teacher.getId())).stream().collect(Collectors.groupingBy(Clazz::getScheduleId));
        Map<Byte, CourseItemResp> respMap = new HashMap<>();
        for (Schedule schedule : scheduleList) {
            ScheduleStatus scheduleStatus = ScheduleStatus.convert(schedule.getStartTime(), schedule.getEndTime(), now);
            CourseItemResp courseItemResp = respMap.computeIfAbsent(scheduleStatus.getCode(), key -> {
                CourseItemResp itemResp = new CourseItemResp();
                itemResp.setCourseNum(0);
                itemResp.setStudentNum(0);
                itemResp.setScheduleStatus(scheduleStatus.getCode());
                return itemResp;
            });
            courseItemResp.setCourseNum(courseItemResp.getCourseNum() + 1);
            List<Clazz> clazzList = clazzMap.get(schedule.getId());
            ScheduleTeacher scheduleTeacher = scheduleTeacherMap.get(schedule.getId());
            Integer orderNum = scheduleTeacherStatisticService.getOrderNum(teacher.getId(), scheduleTeacher, clazzList, scheduleStatus.getCode());
            courseItemResp.setStudentNum(orderNum);
        }
        return RespUtils.success(new ArrayList<>(respMap.values()));
    }

    public CommResp<List<LiveItemResp>> liveList(Long userId) {
        Teacher teacher = teacherMapper.selectOne(new QueryWrapper<Teacher>().select(
                Teacher.ID
        ).eq(Teacher.USER_ID, userId));
        List<ScheduleTeacher> scheduleTeacherList = scheduleTeacherMapper.selectList(new QueryWrapper<ScheduleTeacher>().select(
                ScheduleTeacher.SCHEDULE_ID
        ).eq(ScheduleTeacher.TEACHER_ID, teacher.getId()));
        if (CollectionUtils.isEmpty(scheduleTeacherList)) {
            return RespUtils.success(Collections.emptyList());
        }
        List<Live> liveList = liveMapper.selectList(new QueryWrapper<Live>().select(
                Live.ID, Live.LIVE_NAME, Live.START_TIME, Live.LIVE_TYPE, Live.LIVE_PORT, Live.SCHEDULE_ID
        ).in(Live.SCHEDULE_ID, scheduleTeacherList.stream().map(ScheduleTeacher::getScheduleId).toList())
                .ge(Live.START_TIME, LocalDate.now()).orderByAsc(Live.START_TIME));
        if (CollectionUtils.isEmpty(liveList)) {
            return RespUtils.success(Collections.emptyList());
        }
        List<Long> scheduleIds = liveList.stream().map(Live::getScheduleId).toList();
        Map<Long, Schedule> scheduleMap = scheduleMapper.selectList(new QueryWrapper<Schedule>().select(
                Schedule.ID, Schedule.CAMP_ID, Schedule.SCHEDULE_NAME
        ).in(Schedule.ID, scheduleIds)).stream().collect(Collectors.toMap(Schedule::getId, Function.identity()));
        List<Long> campIds = scheduleMap.values().stream().map(Schedule::getCampId).toList();
        Map<Long, Camp> campMap = campMapper.selectList(new QueryWrapper<Camp>().select(
                Camp.ID, Camp.CAMP_NAME
        ).in(Camp.ID, campIds)).stream().collect(Collectors.toMap(Camp::getId, Function.identity()));
        return RespUtils.success(liveList.stream().map(live -> {
            Schedule schedule = scheduleMap.get(live.getScheduleId());
            Camp camp = campMap.get(schedule.getCampId());
            return toLiveItemResp(live, schedule, camp);
        }).toList());
    }

    private LiveItemResp toLiveItemResp(Live live, Schedule schedule, Camp camp) {
        LiveItemResp liveItemResp = new LiveItemResp();
        liveItemResp.setLiveName(live.getLiveName());
        liveItemResp.setStartTime(live.getStartTime());
        liveItemResp.setLiveType(live.getLiveType());
        liveItemResp.setLivePort(live.getLivePort());
        liveItemResp.setCampId(camp.getId());
        liveItemResp.setCampName(camp.getCampName());
        liveItemResp.setScheduleId(live.getScheduleId());
        liveItemResp.setScheduleName(schedule.getScheduleName());
        return liveItemResp;
    }


    public CommResp<?> remark(Long userId, RemarkReq req) {
        teacherMapper.update(new UpdateWrapper<Teacher>()
                .set(Teacher.REAL_NAME, req.getRemark()).eq(Teacher.ID, req.getTeacherId()));
        return RespUtils.success();
    }
}
