package com.fuyingedu.training.admin.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.databind.JsonNode;
import com.fuyingedu.training.admin.interceptor.auth.AuthorizeManager;
import com.fuyingedu.training.admin.model.order.*;
import com.fuyingedu.training.common.annotation.UserInfo;
import com.fuyingedu.training.common.enums.*;
import com.fuyingedu.training.common.exception.WebBaseException;
import com.fuyingedu.training.common.model.CommResp;
import com.fuyingedu.training.common.util.JsonUtils;
import com.fuyingedu.training.common.util.RespUtils;
import com.fuyingedu.training.dto.order.*;
import com.fuyingedu.training.dto.word.OrderGroupRet;
import com.fuyingedu.training.entity.*;
import com.fuyingedu.training.front.feign.FuyingCourseFeign;
import com.fuyingedu.training.front.manager.OperationManager;
import com.fuyingedu.training.front.service.FrontScheduleService;
import com.fuyingedu.training.mapper.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class OrderService {

    @Autowired
    private StudentMapper studentMapper;
    @Autowired
    private OrderFxyMapper orderFxyMapper;
    @Autowired
    private FxyMapper fxyMapper;
    @Autowired
    private CampMapper campMapper;
    @Autowired
    private UserMapper userMapper;
    @Autowired
    private ScheduleMapper scheduleMapper;
    @Autowired
    private ClazzMapper classMapper;
    @Autowired
    private GroupMapper groupMapper;
    @Autowired
    private TeacherMapper teacherMapper;
    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private ScheduleTeacherMapper scheduleTeacherMapper;
    @Autowired
    private UserRemarkService userRemarkService;
    @Autowired
    private FrontScheduleService frontScheduleService;
    @Autowired
    private UserRoleMapper userRoleMapper;
    @Autowired
    private ClazzMapper clazzMapper;
    @Autowired
    private AuthorizeManager authorizeManager;
    @Autowired
    private FuyingCourseFeign fuyingCourseFeign;
    @Autowired
    private LiveSignRecordMapper liveSignRecordMapper;
    @Autowired
    private LiveAnswerRecordMapper liveAnswerRecordMapper;
    @Autowired
    private WordRewardMapper wordRewardMapper;
    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private AvailableCampMapper availableCampMapper;
    @Autowired
    private OperationManager operationManager;

    @Value("${fxy.base-url}")
    private String baseUrl;

    public CommResp<List<ItemResp>> list(UserInfo userInfo, OrderListParam listReq) {
        if (!userInfo.getRoleIds().contains(2L)) {
            List<Long> campIds = availableCampMapper.selectList(new QueryWrapper<AvailableCamp>().select(
                            AvailableCamp.CAMP_ID
                    ).eq(AvailableCamp.AVAILABLE_ID, userInfo.getUserId()).eq(AvailableCamp.AVAILABLE_TYPE, 4).eq(AvailableCamp.CAMP_STATUS, 1))
                    .stream().map(AvailableCamp::getCampId).toList();
            if (campIds.isEmpty()) {
                return RespUtils.warning(RespMetaEnum.NO_AUTH);
            }
            if (listReq.getCampId() != null && !campIds.contains(listReq.getCampId())) {
                return RespUtils.success(listReq.getPageNum(), listReq.getPageSize(), 0L, Collections.emptyList());
            }
            listReq.setCampIds(campIds);
        } else if (listReq.getCampId() != null) {
            listReq.setCampIds(List.of(listReq.getCampId()));
        }
        IPage<OrderItemRet> page = new Page<>(listReq.getPageNum(), listReq.getPageSize());
        page = orderMapper.pageListForOrder(page, listReq);
        List<Long> campIds = page.getRecords().stream().map(OrderItemRet::getCampId).filter(Objects::nonNull).toList();
        List<Long> teacherIds = page.getRecords().stream().map(OrderItemRet::getTeacherId).filter(Objects::nonNull).toList();
        Map<Long, Camp> campMap = Collections.emptyMap();
        if (!campIds.isEmpty()) {
            campMap = campMapper.selectList(new QueryWrapper<Camp>().select(
                    Camp.ID, Camp.CAMP_NAME
            ).in(Camp.ID, campIds)).stream().collect(Collectors.toMap(Camp::getId, Function.identity()));
        }
        Map<Long, Teacher> teacherMap = Collections.emptyMap();
        if (!teacherIds.isEmpty()) {
            teacherMap = teacherMapper.selectList(new QueryWrapper<Teacher>().select(
                    Teacher.ID, Teacher.REAL_NAME
            ).in(Teacher.ID, teacherIds)).stream().collect(Collectors.toMap(Teacher::getId, Function.identity()));
        }
        List<Long> orderIds = page.getRecords().stream().map(OrderItemRet::getId).toList();
        Map<Long, Long> fxyMap = Collections.emptyMap();
        if (!orderIds.isEmpty()) {
            fxyMap = orderFxyMapper.selectList(new QueryWrapper<OrderFxy>().select(
                    OrderFxy.ORDER_ID, OrderFxy.FXY_ID
            ).in(OrderFxy.ORDER_ID, orderIds)).stream().collect(Collectors.toMap(OrderFxy::getOrderId, OrderFxy::getFxyId));
        }
        Map<Long, Fxy> fxyNameMap = Collections.emptyMap();
        if (!fxyMap.isEmpty()) {
            fxyNameMap = fxyMapper.selectList(new QueryWrapper<Fxy>().select(
                    Fxy.ID, Fxy.NICK_NAME, Fxy.ACCOUNT_INFO
            ).in(Fxy.ID, fxyMap.values())).stream().collect(Collectors.toMap(Fxy::getId, Function.identity()));
        }
        LocalDateTime now = scheduleMapper.queryNow();
        List<ItemResp> respList = new ArrayList<>(page.getRecords().size());
        for (OrderItemRet item : page.getRecords()) {
            ItemResp itemResp = OrderConvertor.toItemResp(item);
            Camp camp = campMap.get(item.getCampId());
            itemResp.setCampId(camp != null ? camp.getId() : null);
            itemResp.setCampName(camp != null ? camp.getCampName() : "");
            if (item.getScheduleId() != null) {
                itemResp.setScheduleStatus(ScheduleStatus.convert(item.getStartTime(), item.getEndTime(), now).getCode());
            }
            Teacher teacher = teacherMap.get(item.getTeacherId());
            itemResp.setTeacherName(teacher != null ? teacher.getRealName() : "");
            Long fxyId = fxyMap.get(item.getId());
            if (fxyId != null) {
                Fxy fxy = fxyNameMap.get(fxyId);
                itemResp.setFxyName(fxy.getNickName());
                itemResp.setFxyAccount(fxy.getAccountInfo());
            }
            if (item.getSignTime() != null) {
                itemResp.setSignStatus((byte) 2);
            } else {
                itemResp.setSignStatus((byte) 1);
            }
            itemResp.setSignTime(item.getSignTime());
            respList.add(itemResp);
        }
        return RespUtils.success(page.getCurrent(), page.getSize(), page.getTotal(), respList);
    }

    public List<ExportItemResp> exportList(UserInfo userInfo, OrderListParam listReq) {
        if (!userInfo.getRoleIds().contains(2L)) {
            List<Long> campIds = availableCampMapper.selectList(new QueryWrapper<AvailableCamp>().select(
                            AvailableCamp.CAMP_ID
                    ).eq(AvailableCamp.AVAILABLE_ID, userInfo.getUserId()).eq(AvailableCamp.AVAILABLE_TYPE, 4).eq(AvailableCamp.CAMP_STATUS, 1))
                    .stream().map(AvailableCamp::getCampId).toList();
            if (campIds.isEmpty()) {
                return Collections.emptyList();
            }
            if (listReq.getCampId() != null && !campIds.contains(listReq.getCampId())) {
                return Collections.emptyList();
            }
            listReq.setCampIds(campIds);
        } else if (listReq.getCampId() != null) {
            listReq.setCampIds(List.of(listReq.getCampId()));
        }

        // 设置导出数量限制
        listReq.setPageNum(1);
        listReq.setPageSize(5000);

        IPage<OrderItemRet> page = new Page<>(listReq.getPageNum(), listReq.getPageSize());
        page = orderMapper.pageListForOrder(page, listReq);

        if (page.getRecords().isEmpty()) {
            return Collections.emptyList();
        }

        List<Long> campIds = page.getRecords().stream().map(OrderItemRet::getCampId).filter(Objects::nonNull).toList();
        List<Long> teacherIds = page.getRecords().stream().map(OrderItemRet::getTeacherId).filter(Objects::nonNull).toList();
        List<Long> clazzIds = page.getRecords().stream().map(OrderItemRet::getClazzId).filter(Objects::nonNull).toList();
        List<Long> groupIds = page.getRecords().stream().map(OrderItemRet::getGroupId).filter(Objects::nonNull).toList();

        Map<Long, Camp> campMap = Collections.emptyMap();
        if (!campIds.isEmpty()) {
            campMap = campMapper.selectList(new QueryWrapper<Camp>().select(
                    Camp.ID, Camp.CAMP_NAME
            ).in(Camp.ID, campIds)).stream().collect(Collectors.toMap(Camp::getId, Function.identity()));
        }

        Map<Long, Teacher> teacherMap = Collections.emptyMap();
        if (!teacherIds.isEmpty()) {
            teacherMap = teacherMapper.selectList(new QueryWrapper<Teacher>().select(
                    Teacher.ID, Teacher.REAL_NAME
            ).in(Teacher.ID, teacherIds)).stream().collect(Collectors.toMap(Teacher::getId, Function.identity()));
        }

        Map<Long, Clazz> clazzMap = Collections.emptyMap();
        if (!clazzIds.isEmpty()) {
            clazzMap = classMapper.selectList(new QueryWrapper<Clazz>().select(
                    Clazz.ID, Clazz.CLASS_NAME
            ).in(Clazz.ID, clazzIds)).stream().collect(Collectors.toMap(Clazz::getId, Function.identity()));
        }

        Map<Long, Group> groupMap = Collections.emptyMap();
        if (!groupIds.isEmpty()) {
            groupMap = groupMapper.selectList(new QueryWrapper<Group>().select(
                    Group.ID, Group.GROUP_NAME
            ).in(Group.ID, groupIds)).stream().collect(Collectors.toMap(Group::getId, Function.identity()));
        }

        List<ExportItemResp> respList = new ArrayList<>(page.getRecords().size());
        for (OrderItemRet item : page.getRecords()) {
            ExportItemResp exportResp = OrderConvertor.toExportItemResp(item);

            Camp camp = campMap.get(item.getCampId());
            exportResp.setCampName(camp != null ? camp.getCampName() : "");

            exportResp.setScheduleName(item.getScheduleName());

            Teacher teacher = teacherMap.get(item.getTeacherId());
            exportResp.setTeacherName(teacher != null ? teacher.getRealName() : "");

            Clazz clazz = clazzMap.get(item.getClazzId());
            exportResp.setClazzName(clazz != null ? clazz.getClassName() : "");

            Group group = groupMap.get(item.getGroupId());
            exportResp.setGroupName(group != null ? group.getGroupName() : "");

            respList.add(exportResp);
        }
        return respList;
    }

    public CommResp<DetailResp> detail(Long id) {
        Order order = orderMapper.selectOne(new QueryWrapper<Order>().select(
                Student.ID, Order.SCHEDULE_ID, Order.USER_ID, Order.CAMP_ID,
                Order.TEACHER_ID, Order.CLAZZ_ID, Order.GROUP_ID, Order.ORDER_TIME,
                Order.ORDER_STATUS, Order.ORDER_REMARK, Order.STUDENT_TYPE, Order.TEACHER_ID
        ).eq(Student.ID, id));
        if (order == null) {
            return RespUtils.warning(RespMetaEnum.PARAM_ERROR);
        }
        DetailResp resp = new DetailResp();
        resp.setOrderStatus(order.getOrderStatus());
        resp.setCreatedTime(order.getOrderTime());
        resp.setStudentType(order.getStudentType());
        User user = userMapper.selectOne(new QueryWrapper<User>().select(
                User.REAL_NAME, User.PHONE_NUM
        ).eq(User.ID, order.getUserId()));
        resp.setRealName(StringUtils.hasLength(order.getOrderRemark()) ? order.getOrderRemark() : user.getRealName());
        resp.setPhoneNum(user.getPhoneNum());
        Student student = studentMapper.selectOne(new QueryWrapper<Student>().select(
                Student.CARD_TYPE, Student.CARD_NUM, Student.REAL_NAME,
                Student.NEW_OUTER_ID, Student.OLD_OUTER_ID
        ).eq(Student.ID, order.getStudentId()));
        if (student != null) {
            resp.setCardNum(student.getCardNum());
            resp.setStudentName(student.getRealName());
            OrderFxy orderFxy = orderFxyMapper.selectOne(new QueryWrapper<OrderFxy>().select(
                    OrderFxy.FXY_ID
            ).eq(OrderFxy.ORDER_ID, order.getId()));
            if (orderFxy != null) {
                Fxy fxy = fxyMapper.selectOne(new QueryWrapper<Fxy>().select(
                        Fxy.OPEN_ID, Fxy.NICK_NAME, Fxy.ACCOUNT_INFO
                ).eq(Fxy.ID, orderFxy.getFxyId()));
                resp.setFxyName(fxy.getOpenId());
                resp.setFxyOpenId(fxy.getNickName());
                resp.setFxyAccount(fxy.getAccountInfo());
            }
        }
        Camp camp = campMapper.selectOne(new QueryWrapper<Camp>().select(
                Camp.CAMP_NAME
        ).eq(Camp.ID, order.getCampId()));
        resp.setCampName(camp.getCampName());
        if (order.getScheduleId() != null) {
            Schedule schedule = scheduleMapper.selectOne(new QueryWrapper<Schedule>().select(
                    Schedule.SCHEDULE_NAME, Schedule.START_TIME, Schedule.END_TIME
            ).eq(Schedule.ID, order.getScheduleId()));
            resp.setScheduleName(schedule.getScheduleName());
            LocalDateTime now = scheduleMapper.queryNow();
            resp.setScheduleStatus(ScheduleStatus.convert(schedule.getStartTime(), schedule.getEndTime(), now).getCode());
        }
        if (order.getTeacherId() != null) {
            Teacher teacher = teacherMapper.selectOne(new QueryWrapper<Teacher>().select(
                    Teacher.USER_ID
            ).eq(Teacher.ID, order.getTeacherId()));
            User teacherUser = userMapper.selectOne(new QueryWrapper<User>().select(
                    User.REAL_NAME,
                    User.PHONE_NUM
            ).eq(User.ID, teacher.getUserId()));
            resp.setTeacherName(teacherUser.getRealName());
            resp.setTeacherPhoneNum(teacherUser.getPhoneNum());
        }
        if (order.getClazzId() != null) {
            Clazz clazz = classMapper.selectOne(new QueryWrapper<Clazz>().select(
                    Clazz.CLASS_NAME, Clazz.ASSISTANT_ID
            ).eq(Clazz.ID, order.getClazzId()));
            resp.setClassName(clazz.getClassName());
            Teacher assistant = teacherMapper.selectOne(new QueryWrapper<Teacher>().select(
                    Teacher.USER_ID
            ).eq(Teacher.ID, clazz.getAssistantId()));
            User teacherUser = userMapper.selectOne(new QueryWrapper<User>().select(
                    User.REAL_NAME, User.PHONE_NUM
            ).eq(User.ID, assistant.getUserId()));
            resp.setAssistantName(teacherUser.getRealName());
            resp.setAssistantPhoneNum(teacherUser.getPhoneNum());
        }
        if (order.getGroupId() != null) {
            Group group = groupMapper.selectOne(new QueryWrapper<Group>().select(
                    Group.GROUP_NAME
            ).eq(Group.ID, order.getGroupId()));
            resp.setGroupName(group.getGroupName());
        }
        return RespUtils.success(resp);
    }

    public CommResp<CountResp> count(Long scheduleId) {
        CountResp countResp = new CountResp();
        countResp.setAllNum(orderMapper.selectCount(new QueryWrapper<Order>()
                .eq(Order.SCHEDULE_ID, scheduleId)
                .in(Order.ORDER_STATUS, OrderStatus.NORMAL.getCode(), OrderStatus.MAY_REFUND.getCode())
        ));
        countResp.setNotAllocNum(orderMapper.selectCount(new QueryWrapper<Order>()
                .eq(Order.SCHEDULE_ID, scheduleId)
                .in(Order.ORDER_STATUS, OrderStatus.NORMAL.getCode(), OrderStatus.MAY_REFUND.getCode())
                .isNull(Order.TEACHER_ID)
        ));
        countResp.setTeacherNum(scheduleTeacherMapper.selectCount(new QueryWrapper<ScheduleTeacher>()
                .eq(ScheduleTeacher.SCHEDULE_ID, scheduleId).eq(ScheduleTeacher.TEACHER_TYPE, TeacherType.TEACHER.getCode())));
        return RespUtils.success(countResp);
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateSchedule(Long userId, UpdateScheduleReq updateScheduleReq) {
        Order order = frontScheduleService.updateSchedule(userId, updateScheduleReq);
        userRemarkService.addRemark(userId, order.getScheduleId(), order.getUserId(), updateScheduleReq.getRemark());
    }

    private void resetGroupMonitor(Byte studentType, Long groupId) {
        if (StudentType.VOLUNTEER.getCode().equals(studentType)) {
            groupMapper.update(new UpdateWrapper<Group>().setSql("monitor_id = null").eq(Group.ID, groupId));
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateClazz(Long userId, UpdateClazzReq updateClazzReq) {
        List<Order> orderList = orderMapper.selectList(new QueryWrapper<Order>().select(
                                Order.ID, Order.STUDENT_TYPE, Order.TEACHER_ID,
                                Order.CLAZZ_ID, Order.GROUP_ID, Order.STUDENT_FLAG, Order.SCHEDULE_ID, Order.USER_ID
                        ).in(Order.ID, updateClazzReq.getOrderIdList())
                .in(Order.ORDER_STATUS, OrderStatus.NORMAL.getCode(), OrderStatus.MAY_REFUND.getCode()).isNotNull(Order.TEACHER_ID)
        );
        if (CollectionUtils.isEmpty(orderList)) {
            throw new WebBaseException(RespMetaEnum.PARAM_ERROR);
        }
        Set<Long> scheduleIdSet = orderList.stream().map(Order::getScheduleId).collect(Collectors.toSet());
        if (scheduleIdSet.size() > 1) {
            throw new WebBaseException(500, "只能修改同一排期的学员");
        }
        Set<Long> teacherIdSet = orderList.stream().map(Order::getTeacherId).collect(Collectors.toSet());
        if (teacherIdSet.size() > 1) {
            throw new WebBaseException(500, "只能修改同一辅导老师下的学员");
        }
        Clazz clazz = classMapper.selectOne(new QueryWrapper<Clazz>().select(
                        Clazz.ID, Clazz.CLASS_NAME
                ).eq(Clazz.ID, updateClazzReq.getClazzId())
        );
        if (clazz == null) {
            throw new WebBaseException(RespMetaEnum.PARAM_ERROR);
        }
        if (orderList.size() == 1 && !StringUtils.hasLength(updateClazzReq.getRemark())) {
            Teacher teacher = teacherMapper.selectOne(new QueryWrapper<Teacher>().select(
                    Teacher.ID, Teacher.REAL_NAME
            ).eq(Teacher.USER_ID, userId));
            String remark;
            Order order = orderList.get(0);
            if (order.getTeacherId().equals(teacher.getId())) {
                remark = "辅导老师【" + teacher.getRealName() + "】改班为：" + clazz.getClassName();
            } else {
                remark = "助教【" + teacher.getRealName() + "】改班为：" + clazz.getClassName();
            }
            updateClazzReq.setRemark(remark);
        }
        List<Order> updateOrderList = new ArrayList<>();
        for (Order order : orderList) {
            if (updateClazzReq.getClazzId().equals(order.getClazzId())) {
                return;
            }
            orderMapper.update(new UpdateWrapper<Order>()
                    .set(Order.CLAZZ_ID, updateClazzReq.getClazzId())
                    .setSql("group_id = null")
                    .set(Order.STUDENT_TYPE, StudentType.NORMAL.getCode())
                    .eq(Order.ID, order.getId()));
            resetGroupMonitor(order.getStudentType(), order.getGroupId());
            userRemarkService.addRemark(userId, order.getScheduleId(), order.getUserId(), updateClazzReq.getRemark());
            Order updateOrder = new Order();
            updateOrder.setId(order.getId());
            updateOrder.setClazzId(updateClazzReq.getClazzId());
            updateOrderList.add(updateOrder);
        }
        if (!updateOrderList.isEmpty()) {
            operationManager.allocAssistant(userId, updateOrderList, orderList);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateGroup(Long userId, UpdateGroupReq updateGroupReq) {
        List<Order> orderList = orderMapper.selectList(new QueryWrapper<Order>().select(
                                Order.ID,
                                Order.GROUP_ID,
                                Order.USER_ID, Order.STUDENT_TYPE,
                                Order.STUDENT_FLAG, Order.SCHEDULE_ID
                        ).in(Order.ID, updateGroupReq.getOrderIdList())
                .in(Order.ORDER_STATUS, OrderStatus.NORMAL.getCode(), OrderStatus.MAY_REFUND.getCode()).isNotNull(Order.CLAZZ_ID)
        );
        if (CollectionUtils.isEmpty(orderList)) {
            throw new WebBaseException(RespMetaEnum.PARAM_ERROR);
        }
        Group group = groupMapper.selectOne(new QueryWrapper<Group>().select(
                Group.ID
        ).eq(Group.ID, updateGroupReq.getGroupId()));
        if (group == null) {
            throw new WebBaseException(RespMetaEnum.PARAM_ERROR);
        }
        List<Order> updateOrderList = new ArrayList<>();
        for (Order order : orderList) {
            if (updateGroupReq.getGroupId().equals(order.getGroupId())) {
                return;
            }
            orderMapper.update(new UpdateWrapper<Order>()
                    .set(Order.GROUP_ID, updateGroupReq.getGroupId())
                    .set(Order.STUDENT_TYPE, StudentType.NORMAL.getCode())
                    .eq(Order.ID, order.getId()));
            resetGroupMonitor(order.getStudentType(), order.getGroupId());
            userRemarkService.addRemark(userId, order.getScheduleId(), order.getUserId(), updateGroupReq.getRemark());
            Order updateOrder = new Order();
            updateOrder.setId(order.getId());
            updateOrder.setGroupId(updateGroupReq.getGroupId());
            updateOrderList.add(updateOrder);
        }
        if (!updateOrderList.isEmpty()) {
            operationManager.allocGroup(userId, updateOrderList, orderList);
        }
    }

    public CommResp<List<ContactItemResp>> contactList(Long userId, ContactListParam listReq) {
        if (listReq.getScheduleId() == null) {
            throw new WebBaseException(4000, "scheduleId不能为空");
        }
        Teacher teacher = teacherMapper.selectOne(new QueryWrapper<Teacher>().select(
                Teacher.ID
        ).eq(Teacher.USER_ID, userId));
        if (teacher == null) {
            return RespUtils.success(Collections.emptyList());
        }
        ScheduleTeacher scheduleTeacher = scheduleTeacherMapper.selectOne(new QueryWrapper<ScheduleTeacher>().select(
                ScheduleTeacher.TEACHER_ID, ScheduleTeacher.TEACHER_TYPE
        ).eq(ScheduleTeacher.SCHEDULE_ID, listReq.getScheduleId()).eq(ScheduleTeacher.TEACHER_ID, teacher.getId()));
        Long assistantId = null;
        if (TeacherType.TEACHER.getCode().equals(scheduleTeacher.getTeacherType())) {
            listReq.setTeacherId(teacher.getId());
            if (listReq.getAssistantId() != null) {
                assistantId = listReq.getAssistantId();
            }
        }
        if (TeacherType.ASSISTANT.getCode().equals(scheduleTeacher.getTeacherType())) {
            assistantId = teacher.getId();
        }
        if (assistantId != null) {
            List<Long> clazzIds = classMapper.selectList(new QueryWrapper<Clazz>().select(
                    Clazz.ID
            ).eq(Clazz.ASSISTANT_ID, assistantId)).stream().map(Clazz::getId).toList();
            if (clazzIds.isEmpty()) {
                return RespUtils.success(Collections.emptyList());
            }
            listReq.setClazzIds(clazzIds);
        }
        IPage<ContactItemRet> page = new Page<>(listReq.getPageNum(), listReq.getPageSize());
        page = orderMapper.pageContactList(page, listReq);
        if (page.getRecords().isEmpty()) {
            return RespUtils.success(Collections.emptyList());
        }
        List<Long> classIds = page.getRecords().stream().map(ContactItemRet::getClazzId).filter(Objects::nonNull).toList();
        Map<Long, String> teacherNameMap = Collections.emptyMap();
        Map<Long, Clazz> classMap = Collections.emptyMap();
        if (!classIds.isEmpty()) {
            classMap = classMapper.selectList(new QueryWrapper<Clazz>().select(
                            Clazz.ID, Clazz.CLASS_NAME,
                            Clazz.ASSISTANT_ID
                    ).in(Clazz.ID, classIds))
                    .stream().collect(Collectors.toMap(Clazz::getId, Function.identity()));
            if (!classMap.isEmpty()) {
                List<Long> teacherIds = classMap.values().stream().map(Clazz::getAssistantId).toList();
                teacherNameMap = teacherMapper.selectList(new QueryWrapper<Teacher>().select(
                                Teacher.ID,
                                Teacher.REAL_NAME
                        ).in(Teacher.ID, teacherIds))
                        .stream().collect(Collectors.toMap(Teacher::getId, Teacher::getRealName));
            }
        }
        Map<Long, Group> groupMap = Collections.emptyMap();
        Set<Long> groupIds = page.getRecords().stream().map(ContactItemRet::getGroupId).filter(Objects::nonNull).collect(Collectors.toSet());
        if (!groupIds.isEmpty()) {
            groupMap = groupMapper.selectList(new QueryWrapper<Group>().select(
                            Group.ID, Group.GROUP_NAME
                    ).in(Group.ID, groupIds))
                    .stream().collect(Collectors.toMap(Group::getId, v -> v));
        }
        List<Long> orderIds = page.getRecords().stream().map(ContactItemRet::getId).toList();
        Map<Long, Long> bindOrderIds = orderFxyMapper.selectList(new QueryWrapper<OrderFxy>().select(
                OrderFxy.ORDER_ID, OrderFxy.FXY_ID
        ).in(OrderFxy.ORDER_ID, orderIds)).stream().collect(Collectors.toMap(OrderFxy::getOrderId, OrderFxy::getFxyId));
        Map<Long, Fxy> fxyMap = Collections.emptyMap();
        if (!bindOrderIds.isEmpty()) {
            fxyMap = fxyMapper.selectList(new QueryWrapper<Fxy>().select(
                    Fxy.ID, Fxy.ACCOUNT_INFO, Fxy.NICK_NAME
            ).in(Fxy.ID, bindOrderIds.values())).stream().collect(Collectors.toMap(Fxy::getId, Function.identity()));
        }
        List<ContactItemResp> respList = new ArrayList<>(page.getRecords().size());
        for (ContactItemRet ret : page.getRecords()) {
            Clazz clazz = classMap.get(ret.getClazzId());
            ContactItemResp resp = OrderConvertor.toContactItemResp(ret);
            if (clazz != null) {
                resp.setClazzId(ret.getClazzId());
                resp.setClazzName(clazz.getClassName());
                resp.setAssistantName(teacherNameMap.get(clazz.getAssistantId()));
            }
            Group group = groupMap.get(ret.getGroupId());
            if (group != null) {
                resp.setGroupId(ret.getGroupId());
                resp.setGroupName(group.getGroupName());
            }
            if (ret.getSignTime() != null) {
                resp.setSignStatus((byte) 2);
            } else {
                resp.setSignStatus((byte) 1);
            }
            resp.setSignTime(ret.getSignTime());
            Long fxyId = bindOrderIds.get(ret.getId());
            resp.setBindStatus(fxyId != null ? (byte) 2 : (byte) 1);
            if (fxyId != null) {
                Fxy fxy = fxyMap.get(fxyId);
                resp.setFxyAccountInfo(fxy.getAccountInfo());
                resp.setFxyNickName(fxy.getNickName());
            }
            respList.add(resp);
        }
        return RespUtils.success(page.getCurrent(), page.getSize(), page.getTotal(), respList);
    }

    public CommResp<List<RecordItemResp>> recordList(RecordListReq listReq) {
        Clazz clazz = classMapper.selectOne(new QueryWrapper<Clazz>().select(
                Clazz.TEACHER_ID, Clazz.ASSISTANT_ID, Clazz.CLASS_NAME
        ).eq(Clazz.ID, listReq.getClazzId()));
        if (clazz == null) {
            throw new WebBaseException(4000, "班级不存在");
        }
        IPage<RecordItemRet> page = new Page<>(listReq.getPageNum(), listReq.getPageSize());
        page = orderMapper.pageRecordList(page, OrderConvertor.toRecordListParam(listReq));
        if (page.getRecords().isEmpty()) {
            return RespUtils.success(page.getCurrent(), page.getSize(), page.getTotal(), Collections.emptyList());
        }
        Set<Long> orderIds = page.getRecords().stream().map(RecordItemRet::getId).collect(Collectors.toSet());
        Map<Long, Integer> liveSignMap = liveSignRecordMapper.groupByOrderIds(orderIds)
                .stream().collect(Collectors.toMap(OrderGroupRet::getOrderId, OrderGroupRet::getNum));
        Map<Long, Integer> liveAnswerMap = liveAnswerRecordMapper.groupByOrderIds(orderIds)
                .stream().collect(Collectors.toMap(OrderGroupRet::getOrderId, OrderGroupRet::getNum));
        Map<Long, Integer> wordRewardMap = wordRewardMapper.groupRewardByOrderIds(orderIds)
                .stream().collect(Collectors.toMap(OrderGroupRet::getOrderId, OrderGroupRet::getNum));
        HttpHeaders headers = new HttpHeaders();
        headers.set("Access-Key", "3447CB6560E99791");
        HttpEntity<Object> httpEntity = new HttpEntity<>(headers);
        StringBuilder orderIdsStr = new StringBuilder();
        for (Long orderId : orderIds) {
            orderIdsStr.append(orderId).append(",");
        }
        String url = String.format("/traApi/admin/campStudy/studyInfo?trainingOrderIds=%s", orderIdsStr.subSequence(0, orderIdsStr.length() - 1));
        ResponseEntity<String> resp = restTemplate.exchange(baseUrl + url, HttpMethod.GET, httpEntity, String.class);
        JsonNode jsonNode = JsonUtils.parseJsonToJsonNode(resp.getBody());
        Map<Long, JsonNode> wordMap = new HashMap<>();
        if ("200".equals(jsonNode.get("code").asText())) {
            JsonNode dataList = jsonNode.get("data");
            for (JsonNode node : dataList) {
                wordMap.put(node.get("trainingOrderId").asLong(), node);
            }
        } else {
            log.error("请求url=[{}]错误", url);
        }
        List<Long> groupIds = page.getRecords().stream().map(RecordItemRet::getGroupId).filter(Objects::nonNull).toList();
        Map<Long, Group> groupMap = Collections.emptyMap();
        if (!groupIds.isEmpty()) {
            groupMap = groupMapper.selectList(new QueryWrapper<Group>().select(
                            Group.ID,
                            Group.GROUP_NAME
                    ).in(Group.ID, groupIds))
                    .stream().collect(Collectors.toMap(Group::getId, Function.identity()));
        }
        Map<Long, String> teacherNameMap = teacherMapper.selectList(new QueryWrapper<Teacher>().select(
                        Teacher.ID,
                        Teacher.REAL_NAME
                ).in(Teacher.ID, clazz.getAssistantId(), clazz.getTeacherId()))
                .stream().collect(Collectors.toMap(Teacher::getId, Teacher::getRealName));
        List<RecordItemResp> respList = new ArrayList<>(page.getRecords().size());
        for (RecordItemRet ret : page.getRecords()) {
            RecordItemResp recordItemResp = OrderConvertor.toRecordItemResp(ret);
            recordItemResp.setTeacherName(teacherNameMap.get(clazz.getTeacherId()));
            recordItemResp.setAssistantName(teacherNameMap.get(clazz.getAssistantId()));
            recordItemResp.setClazzName(clazz.getClassName());
            recordItemResp.setLiveSignNum(liveSignMap.getOrDefault(ret.getId(), 0));
            recordItemResp.setLiveAnswerNum(liveAnswerMap.getOrDefault(ret.getId(), 0));
            recordItemResp.setWordNum(wordRewardMap.getOrDefault(ret.getId(), 0));
            JsonNode node = wordMap.get(ret.getId());
            if (node != null) {
                JsonNode studyCnt = node.get("studyCnt");
                if (studyCnt != null) {
                    recordItemResp.setStudentNum(studyCnt.asInt());
                } else {
                    recordItemResp.setStudentNum(0);
                }
                JsonNode reviewCnt = node.get("reviewCnt");
                if (reviewCnt == null) {
                    recordItemResp.setReviewNum(0);
                } else {
                    recordItemResp.setReviewNum(reviewCnt.asInt());
                }
            } else {
                recordItemResp.setStudentNum(0);
                recordItemResp.setReviewNum(0);
            }
            if (ret.getGroupId() != null) {
                recordItemResp.setGroupName(groupMap.get(ret.getGroupId()).getGroupName());
            }
            respList.add(recordItemResp);
        }
        return RespUtils.success(page.getCurrent(), page.getSize(), page.getTotal(), respList);
    }

    public CommResp<List<SearchItemResp>> searchUser(SearchReq searchReq) {
        if (searchReq.getRealName() == null && searchReq.getPhoneNum() == null && searchReq.getStudentName() == null && searchReq.getCartNo() == null) {
            return RespUtils.success(Collections.emptyList());
        }
        List<Long> userIds = Collections.emptyList(), studentIds = Collections.emptyList();
        if (StringUtils.hasLength(searchReq.getRealName()) || searchReq.getPhoneNum() != null) {
            userIds = userMapper.selectList(new QueryWrapper<User>().select(
                                    User.ID
                            ).eq(StringUtils.hasLength(searchReq.getRealName()), User.REAL_NAME, searchReq.getRealName())
                            .eq(searchReq.getPhoneNum() != null, User.PHONE_NUM, searchReq.getPhoneNum()))
                    .stream().map(User::getId).toList();
        }
        if (StringUtils.hasLength(searchReq.getStudentName()) || StringUtils.hasLength(searchReq.getCartNo())) {
            studentIds = studentMapper.selectList(new QueryWrapper<Student>().select(
                                    Student.ID
                            ).eq(StringUtils.hasLength(searchReq.getStudentName()), Student.REAL_NAME, searchReq.getStudentName())
                            .eq(StringUtils.hasLength(searchReq.getCartNo()), Student.CARD_NUM, searchReq.getCartNo()))
                    .stream().map(Student::getId).toList();
        }
        if (userIds.isEmpty() && studentIds.isEmpty()) {
            return RespUtils.success(Collections.emptyList());
        }
        List<Order> orders = orderMapper.selectList(new QueryWrapper<Order>().select(
                        Order.ID, Order.ORDER_NO, Order.GROUP_ID,
                        Order.CAMP_ID,
                        Order.SCHEDULE_ID,
                        Order.TEACHER_ID,
                        Order.CLAZZ_ID,
                        Order.TEACHER_ID,
                        Order.USER_ID,
                        Order.STUDENT_ID,
                        Order.STUDENT_TYPE,
                        Order.STUDENT_FLAG,
                        Order.ORDER_STATUS
                ).in(!userIds.isEmpty(), Order.USER_ID, userIds)
                .in(!studentIds.isEmpty(), Order.STUDENT_ID, studentIds).orderByDesc(Order.ID));
        if (orders.isEmpty()) {
            return RespUtils.success(Collections.emptyList());
        }
        List<Long> campIds = orders.stream().map(Order::getCampId).filter(Objects::nonNull).distinct().toList();
        Map<Long, Camp> campMap = Collections.emptyMap();
        if (!campIds.isEmpty()) {
            campMap = campMapper.selectList(new QueryWrapper<Camp>().select(
                            Camp.ID,
                            Camp.CAMP_NAME
                    ).in(Camp.ID, campIds))
                    .stream().collect(Collectors.toMap(Camp::getId, Function.identity()));
        }
        List<Long> scheduleIds = orders.stream().map(Order::getScheduleId).filter(Objects::nonNull).distinct().toList();
        Map<Long, Schedule> scheduleMap = Collections.emptyMap();
        if (!scheduleIds.isEmpty()) {
            scheduleMap = scheduleMapper.selectList(new QueryWrapper<Schedule>().select(
                            Schedule.ID,
                            Schedule.SCHEDULE_NAME
                    ).in(Schedule.ID, scheduleIds))
                    .stream().collect(Collectors.toMap(Schedule::getId, Function.identity()));
        }
        List<Long> clazzIds = orders.stream().map(Order::getClazzId).filter(Objects::nonNull).distinct().toList();
        Map<Long, Clazz> clazzMap = Collections.emptyMap();
        if (!clazzIds.isEmpty()) {
            clazzMap = classMapper.selectList(new QueryWrapper<Clazz>().select(
                            Clazz.ID,
                            Clazz.TEACHER_ID, Clazz.ASSISTANT_ID,
                            Clazz.CLASS_NAME
                    ).in(Clazz.ID, clazzIds))
                    .stream().collect(Collectors.toMap(Clazz::getId, Function.identity()));
        }
        List<Long> groupIds = orders.stream().map(Order::getGroupId).filter(Objects::nonNull).distinct().toList();
        Map<Long, Group> groupMap = Collections.emptyMap();
        if (!groupIds.isEmpty()) {
            groupMap = groupMapper.selectList(new QueryWrapper<Group>().select(
                            Group.ID,
                            Group.GROUP_NAME
                    ).in(Group.ID, groupIds))
                    .stream().collect(Collectors.toMap(Group::getId, Function.identity()));
        }
        Map<Long, User> userMap = userMapper.selectList(new QueryWrapper<User>().select(
                        User.ID,
                        User.REAL_NAME,
                        User.PHONE_NUM
                ).in(User.ID, orders.stream().map(Order::getUserId).toList()))
                .stream().collect(Collectors.toMap(User::getId, Function.identity()));
        studentIds = orders.stream().map(Order::getStudentId).filter(Objects::nonNull).distinct().toList();
        Map<Long, Student> studentMap = Collections.emptyMap();
        if (!studentIds.isEmpty()) {
            studentMap = studentMapper.selectList(new QueryWrapper<Student>().select(
                            Student.ID,
                            Student.REAL_NAME,
                            Student.CARD_NUM
                    ).in(Student.ID, studentIds))
                    .stream().collect(Collectors.toMap(Student::getId, Function.identity()));
        }
        List<Long> teacherIds = new ArrayList<>();
        teacherIds.addAll(orders.stream().map(Order::getTeacherId).filter(Objects::nonNull).toList());
        teacherIds.addAll(clazzMap.values().stream().map(Clazz::getAssistantId).toList());
        Map<Long, String> teacherNameMap = Collections.emptyMap();
        if (!teacherIds.isEmpty()) {
            teacherNameMap = teacherMapper.selectList(new QueryWrapper<Teacher>().select(
                            Teacher.ID,
                            Teacher.REAL_NAME
                    ).in(Teacher.ID, teacherIds))
                    .stream().collect(Collectors.toMap(Teacher::getId, Teacher::getRealName));
        }
        List<SearchItemResp> respList = new ArrayList<>(orders.size());
        for (Order order : orders) {
            SearchItemResp searchItemResp = OrderConvertor.toSearchItemResp(order);
            User user = userMap.get(order.getUserId());
            searchItemResp.setRealName(user.getRealName());
            searchItemResp.setPhoneNum(user.getPhoneNum());
            if (order.getStudentId() != null && order.getStudentId() > 0) {
                Student student = studentMap.get(order.getStudentId());
                searchItemResp.setStudentName(student.getRealName());
                searchItemResp.setCartNo(student.getCardNum());
            }
            searchItemResp.setTeacherName(teacherNameMap.get(order.getTeacherId()));
            Clazz clazz = clazzMap.get(order.getClazzId());
            if (clazz != null) {
                searchItemResp.setAssistantName(teacherNameMap.get(clazz.getAssistantId()));
                searchItemResp.setClazzName(clazz.getClassName());
            }
            Group group = groupMap.get(order.getGroupId());
            if (group != null) {
                searchItemResp.setGroupName(group.getGroupName());
            }
            Camp camp = campMap.get(order.getCampId());
            if (camp != null) {
                searchItemResp.setCampName(camp.getCampName());
            }
            Schedule schedule = scheduleMap.get(order.getScheduleId());
            if (schedule != null) {
                searchItemResp.setScheduleName(schedule.getScheduleName());
            }
            respList.add(searchItemResp);
        }
        return RespUtils.success(respList);
    }

    @Transactional(rollbackFor = Exception.class)
    public void close(Long userId, CloseReq closeReq) {
        List<Long> roleIds = userRoleMapper.selectList(new QueryWrapper<UserRole>().select(
                UserRole.ROLE_ID
        ).eq(UserRole.USER_ID, userId)).stream().map(UserRole::getRoleId).toList();
        if (roleIds.isEmpty()) {
            throw new WebBaseException(RespMetaEnum.NO_AUTH);
        }
        Order order = orderMapper.selectOne(new QueryWrapper<Order>()
                .select(Order.ID, Order.SCHEDULE_ID, Order.TEACHER_ID, Order.CLAZZ_ID).eq(Order.ID, closeReq.getOrderId()));
        if (!roleIds.contains(RoleType.ADMIN.getCode())) {
            Teacher teacher = teacherMapper.selectOne(new QueryWrapper<Teacher>().select(Teacher.ID).eq(Teacher.ID, userId));
            if (teacher == null) {
                throw new WebBaseException(RespMetaEnum.NO_AUTH);
            }
            if (!teacher.getId().equals(order.getTeacherId())) {
                if (order.getClazzId() == null) {
                    throw new WebBaseException(RespMetaEnum.NO_AUTH);
                }
                Clazz clazz = clazzMapper.selectOne(new QueryWrapper<Clazz>().select(Clazz.ID, Clazz.ASSISTANT_ID).eq(Clazz.ID, order.getClazzId()));
                if (clazz == null || clazz.getAssistantId() == null) {
                    throw new WebBaseException(RespMetaEnum.NO_AUTH);
                }
                if (!clazz.getAssistantId().equals(userId)) {
                    throw new WebBaseException(RespMetaEnum.NO_AUTH);
                }
            }
        }
        orderMapper.update(new UpdateWrapper<Order>().set(Order.ORDER_STATUS, OrderStatus.CLOSED.getCode())
                .eq(Order.ID, closeReq.getOrderId()));
        operationManager.cancelOrder(userId, order);
    }

    public void addUserRemark(RemarkReq remarkReq) {
        orderMapper.update(new LambdaUpdateWrapper<Order>().set(
                Order::getOrderRemark, remarkReq.getRemark()
        ).eq(Order::getId, remarkReq.getOrderId()));
    }

    public CommResp<?> sign(Long userId, List<Long> orderIds) {
        boolean isAdmin = authorizeManager.isAdmin(userId);
        List<Order> orderList = orderMapper.selectList(new QueryWrapper<Order>().select(
                Order.ID, Order.SIGN_TIME, Order.SCHEDULE_ID, Order.TEACHER_ID, Order.ORDER_NO
        ).in(Order.ID, orderIds));
        if (orderList.isEmpty()) {
            return RespUtils.success();
        }
        Set<Long> scheduleIds = orderList.stream().map(Order::getScheduleId).collect(Collectors.toSet());
        Set<Long> teacherIds = orderList.stream().map(Order::getTeacherId).collect(Collectors.toSet());
        if (scheduleIds.size() != 1) {
            return RespUtils.warning(500, "排期不同，无法核销");
        }
        if (teacherIds.size() != 1) {
            return RespUtils.warning(500, "老师不同，无法核销");
        }
        Teacher teacher = teacherMapper.selectOne(new QueryWrapper<Teacher>().select(
                Teacher.ID, Teacher.USER_ID).in(Teacher.ID, teacherIds));
        if (!isAdmin) {
            if (!teacher.getUserId().equals(userId)) {
                return RespUtils.warning(500, "您不是辅导老师无法核销");
            }
        }
        Long scheduleId = orderList.getFirst().getScheduleId();
        Schedule schedule = scheduleMapper.selectOne(new QueryWrapper<Schedule>().select(
                Schedule.ID, Schedule.SCHEDULE_ID
        ).eq(Schedule.ID, scheduleId));
        int begin = 0;
        do {
            List<Order> subList = orderList.subList(begin, Math.min(begin + 20, orderList.size()));
            List<Map<String, Object>> orderParams = new ArrayList<>(subList.size());
            Map<String, Long> orderIdMap = new HashMap<>(20);
            for (Order order : subList) {
                orderParams.add(Map.of("no", order.getOrderNo(), "signUid", teacher.getUserId()));
                orderIdMap.put(order.getOrderNo(), order.getId());
            }
            Map<String, Object> params = Map.of("courseScheduleId", schedule.getScheduleId(), "details", orderParams);
            log.info("开始核销订单：{}", subList.stream().map(Order::getId).toList());
            String respStr = fuyingCourseFeign.sign(params);
            log.info("核销结果：{}", respStr);
            JsonNode jsonNode = JsonUtils.parseJsonToJsonNode(respStr);
            if (!jsonNode.get("code").asText().equals("200")) {
                return RespUtils.warning(500, jsonNode.get("message").asText());
            }
            Set<Long> successOrderIds = new HashSet<>();
            JsonNode dataList = jsonNode.get("data").get("list");
            for (JsonNode node : dataList) {
                Long orderId = orderIdMap.get(node.asText());
                successOrderIds.add(orderId);
            }
            if (!successOrderIds.isEmpty()) {
                orderMapper.update(new UpdateWrapper<Order>().set(Order.SIGN_TIME, LocalDateTime.now())
                        .in(Order.ID, successOrderIds));
                operationManager.saveLog(userId, successOrderIds, 4);
            }
            begin += 20;
        } while (begin < orderList.size());
        return RespUtils.success();
    }

    public void updateStatus(Long userId, UpdateStatusReq req) {
        Order order = orderMapper.selectOne(new QueryWrapper<Order>().select(
                Order.ID, Order.ORDER_STATUS
        ).eq(Order.ID, req.getOrderId()));
        if (order.getOrderStatus() == 1 && req.getStatus() == 4) {
            orderMapper.update(new UpdateWrapper<Order>().set(Order.ORDER_STATUS, req.getStatus())
                    .eq(Order.ID, req.getOrderId()));
            // 修改为可能退单
            operationManager.saveLog(userId, req.getOrderId(), 12);
        }
        if (order.getOrderStatus() == 4 && req.getStatus() == 1) {
            orderMapper.update(new UpdateWrapper<Order>().set(Order.ORDER_STATUS, req.getStatus())
                    .eq(Order.ID, req.getOrderId()));
            operationManager.saveLog(userId, req.getOrderId(), 13);
        }
    }
}
