package com.fuyingedu.training.admin.model.camp;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class ItemResp {

    private Long id;
    /**
     * 关联名称
     */
    private String relationName;
    /**
     * 训练营名称
     */
    private String campName;

    /**
     * 1-普通 2-扶小鹰陪跑 3-单词训练
     */
    private Byte campType;

    /**
     * 是否需要绑定学员 0-不需要 1-需要
     */
    private Byte needBinding;

    /**
     * 是否需要绑定扶小鹰 0-不需要 1-需要
     */
    private Byte needFxy;

    /**
     * 1-辅导老师 2-助教 3-都不
     */
    private Byte wxPriority;

    /**
     * 训练营图片
     */
    private String mainMediaUrl;

    /**
     * 排期数量
     */
    private Integer scheduleNum;
}
