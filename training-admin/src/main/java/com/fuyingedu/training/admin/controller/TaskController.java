package com.fuyingedu.training.admin.controller;

import com.alibaba.excel.EasyExcel;
import com.fuyingedu.training.admin.model.task.*;
import com.fuyingedu.training.admin.service.TaskService;
import com.fuyingedu.training.common.annotation.Login;
import com.fuyingedu.training.common.annotation.PreAuthorize;
import com.fuyingedu.training.common.model.CommResp;
import com.fuyingedu.training.common.util.RespUtils;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.util.List;

/**
 * 任务管理
 */
@RestController
@RequestMapping("admin/task")
@Slf4j
public class TaskController {

    @Autowired
    private TaskService taskService;

    /**
     * 保存任务
     * @param userId 前端不需要传
     */
    @PreAuthorize({"管理员", "普通管理员"})
    @PostMapping("save")
    public CommResp<?> addOrEdit(@Login Long userId, @Valid @RequestBody SaveReq saveReq) {
        taskService.addOrEdit(userId, saveReq);
        return RespUtils.success();
    }

    /**
     * 导师端-完成/未完成的任务学员列表-管理端
     * @param userId 前端不需要传
     */
    @PreAuthorize({"管理员", "普通管理员"})
    @GetMapping("admin/list")
    public CommResp<StudentResp> studentListM(@Login Long userId,
                                             @RequestParam("taskId") Long taskId,
                                             @RequestParam(required = false, value = "recordDate")LocalDate recordDate) {
        return taskService.studentList(userId, taskId, recordDate);
    }

    /**
     * 导师端-任务提交详情列表-管理端
     * @param userId 前端不需要传
     */
    @PreAuthorize({"管理员", "普通管理员"})
    @GetMapping("admin/committed/list")
    public CommResp<CommittedResp> committedListAdmin(@Login Long userId,
                                                 @RequestParam("taskId") Long taskId,
                                                 @RequestParam(required = false, value = "recordDate")LocalDate recordDate) {
        return taskService.committedList(userId, taskId, recordDate);
    }

    /**
     * 任务列表
     */
    @GetMapping("list")
    public CommResp<List<ItemResp>> list(ListReq listReq) {
        return taskService.list(listReq);
    }

    /**
     * 任务详情
     */
    @GetMapping("detail")
    public CommResp<DetailResp> detail(@RequestParam("id") Long id) {
        return taskService.detail(id);
    }

    /**
     * 导师端-保存任务
     */
    @PostMapping("save/teacher")
    public CommResp<?> saveByTeacher(@Login Long userId, @RequestBody SaveReq saveReq) {
        taskService.saveByTeacher(userId, saveReq);
        return RespUtils.success();
    }

    /**
     * 导师端-任务记录
     * @param userId 前端不需要传
     * @param scheduleId 排期ID
     */
    @GetMapping("record/list")
    public CommResp<List<RecordItemResp>> recordList(@Login Long userId, @RequestParam("scheduleId") Long scheduleId) {
        return taskService.recordList(userId, scheduleId);
    }

    /**
     * 导师端-日历列表
     * @param userId 前端不需要传
     */
    @GetMapping("date/list")
    public CommResp<List<DateItemResp>> dateList(@Login Long userId,
                                                 @RequestParam("taskId") Long taskId) {
        return taskService.dateList(userId, taskId);
    }

    /**
     * 导师端-完成/未完成的任务学员列表
     * @param userId 前端不需要传
     */
    @GetMapping("student/list")
    public CommResp<StudentResp> studentList(@Login Long userId,
                                             @RequestParam(value = "teacherId", required = false) Long teacherId,
                                             @RequestParam("taskId") Long taskId,
                                             @RequestParam(required = false, value = "recordDate")LocalDate recordDate) {
        return taskService.studentList(userId, teacherId, taskId, recordDate);
    }

    /**
     * 错题统计下载
     */
    @GetMapping("wrongWord/download")
    public void downloadWrongWordList(@Login Long userId,
                                      @RequestParam("taskId") Long taskId, HttpServletResponse response) throws IOException {
        List<WrongDownloadResp> respList = taskService.downloadWrongWordList(userId, taskId);
        response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
        response.setHeader("Content-disposition", "attachment;filename=" + URLEncoder.encode("错题统计", StandardCharsets.UTF_8) +".xlsx");
        EasyExcel.write(response.getOutputStream(), WrongDownloadResp.class).sheet("错题统计").doWrite(respList);
    }

    /**
     * 导师端-任务提交详情列表
     * @param userId 前端不需要传
     */
    @GetMapping("committed/list")
    public CommResp<CommittedResp> committedList(@Login Long userId,
                                                 @RequestParam(value = "teacherId", required = false) Long teacherId,
                                                        @RequestParam("taskId") Long taskId,
                                                        @RequestParam(required = false, value = "recordDate")LocalDate recordDate) {
        return taskService.committedList(userId, teacherId, taskId, recordDate);
    }

    /**
     * 学练测学习报告
     */
    @GetMapping("studyReport")
    public CommResp<?> studyReport(@RequestParam("orderId") Long orderId,
                                   @RequestParam("reportDate") LocalDate reportDate) {
        return taskService.studyReport(orderId, reportDate);
    }

    /**
     * 导出学习报告-导师端
     */
    @GetMapping("studyReport/download")
    public void studyReportDownload(
            @Login Long userId,
            @RequestParam(value = "scheduleId",required = false) Long scheduleId,
            @RequestParam(value = "orderId", required = false) Long orderId,
            @RequestParam(value = "date", required = false) LocalDate date,
            HttpServletResponse response
    ) throws IOException {
        taskService.downloadStudyReport(userId, scheduleId, orderId, date, response);
    }


    /**
     * 导出学习报告-管理端
     */
    @PreAuthorize({"管理员", "普通管理员"})
    @GetMapping("studyReport/schedule/download")
    public void studyReportDownload1(
            @Login Long userId,
            @RequestParam("scheduleId") Long scheduleId,
            @RequestParam(value = "date") LocalDate date,
            HttpServletResponse response
    ) throws IOException {
        taskService.downloadStudyReport1(scheduleId, date, response);
    }
}
