package com.fuyingedu.training.admin.config;

import com.aliyun.openservices.ons.api.PropertyKeyConst;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Properties;

@Getter
@Setter
@Configuration
@ConfigurationProperties(prefix = "rocketmq")
public class MqConfig {

    private String accessKey;
    private String secretKey;
    private String groupId;
    private String nameSrvAddr;

    public Properties getProperties() {
        Properties properties = new Properties();
        properties.setProperty(PropertyKeyConst.GROUP_ID, this.getGroupId());
        properties.setProperty(PropertyKeyConst.AccessKey, this.getAccessKey());
        properties.setProperty(PropertyKeyConst.SecretKey, this.getSecretKey());
        properties.setProperty(PropertyKeyConst.NAMESRV_ADDR, this.getNameSrvAddr());
        return properties;
    }
}
