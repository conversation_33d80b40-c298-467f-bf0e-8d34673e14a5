package com.fuyingedu.training.admin.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fuyingedu.training.admin.model.teacher.ListReq;
import com.fuyingedu.training.admin.model.user.AdminResp;
import com.fuyingedu.training.admin.model.user.SaveReq;
import com.fuyingedu.training.admin.model.user.UserConvertor;
import com.fuyingedu.training.common.model.CommResp;
import com.fuyingedu.training.common.util.RespUtils;
import com.fuyingedu.training.entity.AvailableCamp;
import com.fuyingedu.training.entity.User;
import com.fuyingedu.training.entity.UserRole;
import com.fuyingedu.training.mapper.AvailableCampMapper;
import com.fuyingedu.training.mapper.UserMapper;
import com.fuyingedu.training.mapper.UserRoleMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class UserService {

    @Autowired
    private UserMapper userMapper;
    @Autowired
    private UserRoleMapper userRoleMapper;
    @Autowired
    private AvailableCampMapper availableCampMapper;

    public CommResp<List<AdminResp>> list(ListReq req) {
        IPage<User> page = new Page<>(req.getPageNum(), req.getPageSize());
        page = userMapper.pageAdmin(page, req.getTeacherInfo(), req.getPhoneNum());
        List<AdminResp> respList = page.getRecords().stream().map(UserConvertor::toAdminResp).toList();
        return RespUtils.success(page.getCurrent(), page.getSize(), page.getTotal(), respList);
    }

    public CommResp<AdminResp> detail(Long id) {
        User user = userMapper.selectOne(new QueryWrapper<User>().select(
                User.ID, User.REAL_NAME, User.PHONE_NUM, User.UNION_ID, User.USER_ICON
        ).eq(User.ID, id));
        AdminResp adminResp = UserConvertor.toAdminResp(user);
        List<Long> roleIds = userRoleMapper.selectList(new QueryWrapper<UserRole>().select(UserRole.ROLE_ID).eq(UserRole.USER_ID, id))
                .stream().map(UserRole::getRoleId).toList();
        if (roleIds.contains(2L)) {
            adminResp.setRoleType(2);
        } else {
            adminResp.setRoleType(3);
            List<Long> campIds = availableCampMapper.selectList(new QueryWrapper<AvailableCamp>().select(AvailableCamp.CAMP_ID)
                    .eq(AvailableCamp.AVAILABLE_ID, id).eq(AvailableCamp.AVAILABLE_TYPE, 4).eq(AvailableCamp.CAMP_STATUS, 1))
                    .stream().map(AvailableCamp::getCampId).toList();
            adminResp.setCampIds(campIds);
        }
        return RespUtils.success(adminResp);
    }

    public CommResp<?> addOrEdit(SaveReq req) {
        User user = userMapper.selectOne(new QueryWrapper<User>().select(
                User.ID).eq(User.ID, req.getUid()));
        if (user == null) {
            user = UserConvertor.toUser(req);
            userMapper.insert(user);
        }
        List<UserRole> userRoleList = userRoleMapper.selectList(new QueryWrapper<UserRole>().select(UserRole.ID, UserRole.ROLE_ID)
                .eq(UserRole.USER_ID, user.getId()).in(UserRole.ROLE_ID, 2L, 3L));
        if (!userRoleList.isEmpty()) {
            userRoleMapper.deleteByIds(userRoleList.stream().map(UserRole::getId).toList());
        }
        UserRole userRole = new UserRole();
        userRole.setUserId(user.getId());
        userRole.setRoleId(req.getRoleType().longValue());
        userRoleMapper.insert(userRole);
        if (req.getRoleType() == 3) {
            List<AvailableCamp> availableCamps = availableCampMapper.selectList(new QueryWrapper<AvailableCamp>().select(AvailableCamp.ID)
                    .eq(AvailableCamp.AVAILABLE_ID, user.getId()).eq(AvailableCamp.AVAILABLE_TYPE, 4));
            if (!availableCamps.isEmpty()) {
                availableCampMapper.deleteByIds(availableCamps.stream().map(AvailableCamp::getId).toList());
            }
            Long userId = user.getId();
            availableCamps = req.getCampIdList().stream().map(campId -> {
                AvailableCamp availableCamp = new AvailableCamp();
                availableCamp.setAvailableId(userId);
                availableCamp.setCampId(campId);
                availableCamp.setAvailableType((byte) 4);
                return availableCamp;
            }).toList();
            if (!availableCamps.isEmpty()) {
                availableCampMapper.insert(availableCamps);
            }
        }
        return RespUtils.success();
    }
}
