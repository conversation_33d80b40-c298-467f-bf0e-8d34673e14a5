package com.fuyingedu.training.admin.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fuyingedu.training.common.enums.Status;
import com.fuyingedu.training.entity.AvailableCamp;
import com.fuyingedu.training.mapper.AvailableCampMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;

@Service
public class AvailableCampService {

    @Autowired
    private AvailableCampMapper availableCampMapper;

    @Transactional(rollbackFor = Exception.class)
    public void add(Long availableId, Byte availableType, @Nullable List<Long> campIds) {
        if (CollectionUtils.isEmpty(campIds)) {
            campIds = Collections.emptyList();
        }
        List<AvailableCamp> availableCampList = availableCampMapper.selectList(new QueryWrapper<AvailableCamp>().select(
                AvailableCamp.CAMP_STATUS,
                AvailableCamp.CAMP_ID
        ).eq(AvailableCamp.AVAILABLE_ID, availableId).eq(AvailableCamp.AVAILABLE_TYPE, availableType));
        List<AvailableCamp> updateList = new ArrayList<>();
        Set<Long> oldCampSet = new HashSet<>();
        for (AvailableCamp availableCamp : availableCampList) {
            if (campIds.contains(availableCamp.getCampId())) {
                if (availableCamp.getCampStatus().equals(Status.DELETE.getCode())) {
                    AvailableCamp updateEntity = new AvailableCamp();
                    updateEntity.setId(availableCamp.getId());
                    updateEntity.setCampStatus(Status.NORMAL.getCode());
                    updateList.add(updateEntity);
                }
            } else {
                if (availableCamp.getCampStatus().equals(Status.NORMAL.getCode())) {
                    AvailableCamp updateEntity = new AvailableCamp();
                    updateEntity.setId(availableCamp.getId());
                    updateEntity.setCampStatus(Status.DELETE.getCode());
                    updateList.add(updateEntity);
                }
            }
            oldCampSet.add(availableCamp.getCampId());
        }
        List<AvailableCamp> insertList = campIds.stream().filter(campId -> !oldCampSet.contains(campId))
                .map(campId -> toAvailableCamp(availableId, availableType, campId)).toList();
        if (!updateList.isEmpty()) {
            availableCampMapper.updateById(updateList);
        }
        if (!insertList.isEmpty()) {
            availableCampMapper.insert(insertList);
        }
    }

    public List<Long> getCampIds(Long availableId, Byte availableType) {
        return availableCampMapper.selectList(new QueryWrapper<AvailableCamp>().select(
                                AvailableCamp.CAMP_ID
                        ).eq(AvailableCamp.CAMP_STATUS, Status.NORMAL.getCode())
                        .eq(AvailableCamp.AVAILABLE_ID, availableId)
                        .eq(AvailableCamp.AVAILABLE_TYPE, availableType)
        ).stream().map(AvailableCamp::getCampId).toList();
    }

    private AvailableCamp toAvailableCamp(Long availableId, Byte availableType, Long campId) {
        AvailableCamp teacherCamp = new AvailableCamp();
        teacherCamp.setAvailableId(availableId);
        teacherCamp.setAvailableType(availableType);
        teacherCamp.setCampId(campId);
        return teacherCamp;
    }
}
