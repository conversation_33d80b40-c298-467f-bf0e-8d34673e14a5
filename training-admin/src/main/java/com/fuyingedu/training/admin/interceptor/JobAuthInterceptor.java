package com.fuyingedu.training.admin.interceptor;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import java.io.IOException;

@Component
public class JobAuthInterceptor implements HandlerInterceptor {


    private static final String TOKEN = "a080e25a4b4c4c309df636e5184a3286";

    @Override
    public boolean preHandle(HttpServletRequest request, @NotNull HttpServletResponse response, @NotNull Object handler) throws IOException {
        String token = request.getParameter("token");
        return TOKEN.equals(token);
    }
}
