package com.fuyingedu.training.admin.interceptor;

import com.fuyingedu.training.common.util.RedisKey;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class FxyAuthInterceptor extends CommonAuthInterceptor {

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    @Override
    public boolean checkAuth(HttpServletRequest request, @NotNull HttpServletResponse response, @NotNull Object handler) throws IOException {
        String accessToken = request.getHeader("access_token");
        if (accessToken == null) {
            log.info("access-token为空");
            respNotLogin(response);
            return false;
        }
        request.setAttribute("token", accessToken);
        String openId = redisTemplate.opsForValue().get(String.format(RedisKey.FXY_TOKEN, accessToken));
        if (openId == null) {
            log.info("[{}]openId未注册", accessToken);
            respNotLogin(response);
            return false;
        }
        redisTemplate.expire(String.format(RedisKey.FXY_TOKEN, accessToken), 30, TimeUnit.DAYS);
        setUserInfo(request, openId);
        return true;
    }

}
