package com.fuyingedu.training.admin.excel;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;

public class WxStatusConverter implements Converter<Byte> {

    public WriteCellData<?> convertToExcelData(Byte value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration)
    {
        if (value == null) {
            return new WriteCellData<>("");
        }
        if (value == 1) {
            return new WriteCellData<>("未加微信");
        }
        return new WriteCellData<>("已加微信");
    }
}
