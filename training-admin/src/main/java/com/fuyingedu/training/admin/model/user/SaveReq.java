package com.fuyingedu.training.admin.model.user;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class SaveReq {

    /**
     * 用户头像
     */
    private String userIcon;

    /**
     * 用户名
     */
    private String realName;

    /**
     * 手机号
     */
    @NotNull
    private Long phoneNum;

    /**
     * 微信unionid
     */
    private String unionId;

    /**
     * 傲爸妈uid
     */
    @NotNull
    private Long uid;

    /**
     * 角色类型 2 超级管理员 3普通管理
     */
    private Integer roleType;

    /**
     * 可服务训练营
     */
    @NotEmpty
    private List<Long> campIdList;
}
