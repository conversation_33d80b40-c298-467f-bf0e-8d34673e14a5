package com.fuyingedu.training.admin.model.schedule;

import com.fuyingedu.training.common.model.PageReq;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;

@Getter
@Setter
@ToString
public class ListReq extends PageReq {

    /**
     * 训练营ID
     */
    @NotNull
    private Long campId;

    /**
     * 排期名称
     */
    private String scheduleName;

    /**
     * 排期开始时间的开始时间
     */
    private LocalDateTime startTimeBegin;

    /**
     * 排期开始时间的截止时间
     */
    private LocalDateTime startTimeEnd;

    /**
     * 排期结束时间的开始时间
     */
    private LocalDateTime endTimeBegin;

    /**
     * 排期结束时间的截止时间
     */
    private LocalDateTime endTimeEnd;
}
