package com.fuyingedu.training.admin.config;

import com.fuyingedu.training.common.util.DateUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.convert.converter.Converter;
import org.springframework.util.StringUtils;

import java.time.*;

@Configuration
public class ParamConverterConfig {

    @Bean
    public Converter<String, LocalDateTime> localDateTimeConverter() {
        return new Converter<String, LocalDateTime>() {
            @Override
            public LocalDateTime convert(@NotNull String source) {
                if (!StringUtils.hasLength(source)) {
                    return null;
                }
                return DateUtils.parseDateTime(source);
            }
        };
    }

    @Bean
    public Converter<String, LocalDate> localDateConverter() {
        return new Converter<String, LocalDate>() {
            @Override
            public LocalDate convert(@NotNull String source) {
                if (!StringUtils.hasLength(source)) {
                    return null;
                }
                return DateUtils.parseDate(source);
            }
        };
    }

    @Bean
    public Converter<String, LocalTime> localTimeConverter() {
        return new Converter<String, LocalTime>() {
            @Override
            public LocalTime convert(@NotNull String source) {
                if (!StringUtils.hasLength(source)) {
                    return null;
                }
                return DateUtils.parseTime(source);
            }
        };
    }
}
