package com.fuyingedu.training.admin.model.task;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDate;

@Setter
@Getter
@ToString
public class DateItemResp {

    /**
     * 日期
     */
    private LocalDate recordDate;

    /**
     * 任务完成率
     */
    private String doneRate;

    /**
     * 状态 1-进行中 2-未开始 3-已完成
     */
    private Byte taskStatus;

    /**
     * 1 - 学练用  3 - 直播
     */
    private Byte taskType;

    public void setTaskStatus(LocalDate startDate, LocalDate endDate) {
        LocalDate now = LocalDate.now();
        if (now.isBefore(startDate)) {
            this.taskStatus = 2;
        } else if (now.isAfter(endDate)) {
            this.taskStatus = 3;
        } else {
            this.taskStatus = 1;
        }
    }

    public void setDoneRate(int doneNum, int totalNum) {
        if (totalNum <= 0) {
            this.doneRate = "0%";
        } else if (doneNum > totalNum) {
            this.doneRate = "100%";
        } else {
            this.doneRate = String.format("%.2f", doneNum * 100.0 / totalNum) + "%";
        }
    }
}
