package com.fuyingedu.training.admin.mq;

import com.fuyingedu.training.admin.manager.SyncOrderManager;
import com.fuyingedu.training.common.util.AsyncUtils;
import com.fuyingedu.training.common.util.JsonUtils;
import com.fuyingedu.training.front.manager.OperationManager;
import com.fuyingedu.training.front.model.feign.OrderItem;
import com.fuyingedu.training.front.model.feign.SignItem;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.*;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

import java.io.IOException;


@Component
@Slf4j
public class OrderMessageListener {

    @Autowired
    private SyncOrderManager syncOrderManager;
    @Autowired
    private OperationManager operationManager;

    @RabbitListener(queues = "abm.training.status.abm-course-service#biz", ackMode = "MANUAL")
    public void receive(@Payload String payload, @Header(AmqpHeaders.CHANNEL) Channel channel,
                        @Header(AmqpHeaders.DELIVERY_TAG) Long deliveryTag) {
        AsyncUtils.execute(() -> {
            try {
                OrderItem orderItem = JsonUtils.parseJsonToObj(payload, OrderItem.class);
                syncOrderManager.syncOrder(orderItem);
                channel.basicAck(deliveryTag, false);
            } catch (Exception e) {
                log.error("订单消息确认失败: {}", payload, e);
                try {
                    channel.basicNack(deliveryTag, false, true);
                } catch (IOException ex) {
                    log.error("订单消息确认失败: {}", payload, ex);
                }
            }
        }, "服务单消息消费");
    }

    @RabbitListener(queues = "abm.course.info.abm-activity-service#biz", ackMode = "MANUAL")
    public void receiveSign(@Payload String payload, @Header(AmqpHeaders.CHANNEL) Channel channel,
                        @Header(AmqpHeaders.DELIVERY_TAG) Long deliveryTag) {
        AsyncUtils.execute(() -> {
            try {
                log.info("收到签到信息: {}", payload);
                SignItem signItem = JsonUtils.parseJsonToObj(payload, SignItem.class);
                syncOrderManager.signOrder(signItem);
                channel.basicAck(deliveryTag, false);
            } catch (Exception e) {
                log.error("签到信息确认失败: {}", payload, e);
                try {
                    channel.basicNack(deliveryTag, false, true);
                } catch (IOException ex) {
                    log.error("签到信息确认失败: {}", payload, ex);
                }
            }
        }, "服务单签到信息消费");
    }
}
