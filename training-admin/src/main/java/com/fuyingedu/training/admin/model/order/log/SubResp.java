package com.fuyingedu.training.admin.model.order.log;

import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
public class SubResp {

    private Long id;

    private LocalDateTime createdTime;

    /**
     * 学员名
     */
    private String username;

    private String studentName;

    /**
     * 手机号
     */
    private Long phoneNum;

    /**
     * 用户头像
     */
    private String userIcon;

    /**
     * 扶小鹰账号信息
     */
    private String fxyAccountInfo;

    /**
     * 扶小鹰用户名
     */
    private String fxyNickName;

    /**
     * 1-改期 2-分配辅导老师 3-关闭订单 4-核销订单 5-删除备注 6-修改辅导老师 7-备注 8-分班 9-分组 12-修改订单状态-可能退单 13-修改订单状态-正常
     */
    private Integer operationType;

    /**
     * 操作人
     */
    private String operatorName;
    /**
     * 操作之前的值
     */
    private String prevContent;

    /**
     * 操作之后的值
     */
    private String nextContent;
}
