package com.fuyingedu.training.admin.model.camp;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class Detail {

    @NotNull
    private Long id;
    /**
     * 训练营名称
     */
    @NotBlank
    private String campName;

    /**
     * 1-普通 2-扶小鹰陪跑 3-单词训练
     */
    @NotNull
    private Byte campType;

    /**
     * 是否需要绑定学员 0-不需要 1-需要
     */
    @NotNull
    private Byte needBinding;

    /**
     * 是否需要绑定扶小鹰 0-不需要 1-需要
     */
    @NotNull
    private Byte needFxy;

    /**
     * 1-辅导老师 2-助教 3-都不
     */
    @NotNull
    private Byte wxPriority;

    /**
     * 训练营图片
     */
    @NotNull
    private String mainMediaUrl;

    /**
     * 辅导老师要求
     */
    private String campContent;

    private String relationName;
    /**
     * 自动核销天数 0表示不自动核销
     */
    private Integer signDays;
}
