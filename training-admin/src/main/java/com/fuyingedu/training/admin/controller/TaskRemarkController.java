package com.fuyingedu.training.admin.controller;

import com.fuyingedu.training.front.model.task.remark.SaveReq;
import com.fuyingedu.training.front.service.TaskRemarkService;
import com.fuyingedu.training.common.annotation.Login;
import com.fuyingedu.training.common.model.CommResp;
import com.fuyingedu.training.common.util.RespUtils;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 点评管理
 */
@RequestMapping("admin/task/remark")
@RestController
public class TaskRemarkController {

    @Autowired
    private TaskRemarkService taskRemarkService;

    /**
     * 添加点评
     * @param userId 不传
     */
    @PostMapping("save")
    public CommResp<?> save(@Login Long userId, @Valid @RequestBody SaveReq saveReq) {
        taskRemarkService.save(userId, saveReq);
        return RespUtils.success();
    }

}
