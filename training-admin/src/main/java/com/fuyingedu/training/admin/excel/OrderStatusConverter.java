package com.fuyingedu.training.admin.excel;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;

public class OrderStatusConverter implements Converter<Byte> {

    @Override
    public WriteCellData<?> convertToExcelData(Byte value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) {
        if (value == null) {
            return null;
        }
        return switch (value) {
            case 1 -> new WriteCellData<>("正常");
            case 2 -> new WriteCellData<>("已关闭");
            case 3 -> new WriteCellData<>("已退款");
            case 4 -> new WriteCellData<>("预计退款");
            default -> new WriteCellData<>("未知状态");
        };
    }
}
