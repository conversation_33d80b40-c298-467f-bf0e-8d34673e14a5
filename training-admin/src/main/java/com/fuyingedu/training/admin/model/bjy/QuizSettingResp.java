package com.fuyingedu.training.admin.model.bjy;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

@Getter
@Setter
@Accessors(chain = true)
public class QuizSettingResp {


    /**
     * 测试开关 1 关闭 2 开启
     */
    private byte quizStatus = 1;

    /**
     * 任务积分
     */
    private Integer taskReward;

    /**
     * 扶小鹰小太阳
     */
    private Integer fxyReward;

    /**
     * Word鹰积分值
     */
    private Integer wordReward;

    /**
     * 测试题列表
     */
    private List<Quiz> quizList;

    @Getter
    @Setter
    public static class Quiz {
        private Long quizId;

        private String quizName;
    }
}
