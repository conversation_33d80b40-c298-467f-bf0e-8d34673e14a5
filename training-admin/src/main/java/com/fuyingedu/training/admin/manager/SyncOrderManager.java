package com.fuyingedu.training.admin.manager;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.fuyingedu.training.admin.model.order.OrderReq;
import com.fuyingedu.training.common.enums.CardType;
import com.fuyingedu.training.common.enums.StudentType;
import com.fuyingedu.training.common.util.TransactionUtils;
import com.fuyingedu.training.entity.*;
import com.fuyingedu.training.front.feign.FuyingCourseFeign;
import com.fuyingedu.training.front.feign.FuyingUserFeign;
import com.fuyingedu.training.front.manager.OperationManager;
import com.fuyingedu.training.front.model.feign.*;
import com.fuyingedu.training.mapper.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

@Slf4j
@Component
public class SyncOrderManager {

    @Autowired
    private FuyingUserFeign fuyingUserFeign;
    @Autowired
    private FuyingCourseFeign fuyingCourseFeign;
    @Autowired
    private UserMapper userMapper;
    @Autowired
    private StudentMapper studentMapper;
    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private OrderStatisticMapper orderStatisticMapper;
    @Autowired
    private ScheduleMapper scheduleMapper;
    @Autowired
    private GroupMapper groupMapper;
    @Autowired
    private OperationManager operationManager;

    public void syncOrder(OrderItem orderItem) {
        Schedule schedule = scheduleMapper.selectOne(new QueryWrapper<Schedule>().select(
                        Schedule.ID, Schedule.SCHEDULE_ID, Schedule.CLAZZ_ID
                ).eq(Schedule.CLAZZ_ID, orderItem.getScheduleClassId())
                .eq(Schedule.SCHEDULE_ID, orderItem.getScheduleId()));
        if (schedule == null) {
            return;
        }
        syncOrder(schedule, orderItem);
    }

    public void syncOrder(Schedule schedule, OrderItem orderItem) {
        Long uid = orderItem.getUid();
        Long studentId = orderItem.getTraineeId();
        UserItem userItem = fuyingUserFeign.getUserByUid(uid);
        UnionItem unionId = fuyingUserFeign.getUnionId(uid);
        User user = toUser(userItem);
        if (unionId != null && unionId.getThirdAuthId() != null) {
            User oldUser = userMapper.selectOne(new LambdaQueryWrapper<User>().select(User::getId).eq(User::getUnionId, unionId.getThirdAuthId()));
            if (oldUser != null && !oldUser.getId().equals(uid)) {
                userMapper.update(new UpdateWrapper<User>().setSql("union_id = null")
                        .eq(User.ID, oldUser.getId()));
            }
            user.setUnionId(unionId.getThirdAuthId());
        }
        userMapper.insertOrUpdate(user);
        Order newOrder = toOrder(orderItem);
        newOrder.setScheduleId(schedule.getId());
        if (studentId != null && studentId != 0) {
            TraineeItem trainee = fuyingCourseFeign.getTrainee(studentId);
            Student student = studentMapper.selectOne(new QueryWrapper<Student>().select(Student.ID)
                    .eq(Student.CARD_NUM, trainee.getIdCard()));
            if (student == null) {
                student = toStudent(trainee);
                studentMapper.insert(student);
            }
            newOrder.setStudentId(student.getId());
        }
        Order oldOrder = orderMapper.selectOne(new QueryWrapper<Order>().select(
                Order.ID, Order.SCHEDULE_ID, Order.TEACHER_ID, Order.CLAZZ_ID, Order.STUDENT_TYPE, Order.GROUP_ID,
                Order.ORDER_STATUS, Order.USER_ID
        ).eq(Order.ORDER_NO, orderItem.getNo()));
        if (oldOrder == null) {
            newOrder.setOrderRemark(user.getRealName());
            orderMapper.insert(newOrder);
            OrderStatistic statistic = new OrderStatistic();
            statistic.setOrderId(newOrder.getId());
            orderStatisticMapper.insert(statistic);
        } else {
            newOrder.setId(oldOrder.getId());
            TransactionUtils.execute(() -> {
                UpdateWrapper<Order> updateWrapper = new UpdateWrapper<>();
                if (oldOrder.getScheduleId() == null || !oldOrder.getScheduleId().equals(newOrder.getScheduleId())) {
                    updateWrapper.set(Order.SCHEDULE_ID, newOrder.getScheduleId()).setSql("teacher_id = null")
                            .setSql("clazz_id = null").setSql("group_id = null").setSql("sign_time = null");
                    operationManager.modifySchedule(-1L, oldOrder, newOrder.getScheduleId());
                    if (StudentType.VOLUNTEER.getCode().equals(oldOrder.getStudentType())) {
                        groupMapper.update(new UpdateWrapper<Group>().setSql("group_id = null").eq(Group.ID, oldOrder.getGroupId()));
                    }
                }
                updateWrapper.set(newOrder.getStudentId() != null, Order.STUDENT_ID, newOrder.getStudentId())
                        .set(newOrder.getOrderTime() != null, Order.ORDER_TIME, newOrder.getOrderTime())
                        .set(Order.STUDENT_FLAG, newOrder.getStudentFlag())
                        .set(Order.ORDER_STATUS, newOrder.getOrderStatus())
                        .set(Order.REAL_ORDER_NO, newOrder.getRealOrderNo())
                        .eq(Order.ID, newOrder.getId());
                orderMapper.update(updateWrapper);
                if (oldOrder.getOrderStatus() == 1 || oldOrder.getOrderStatus() == 4) {
                    if (newOrder.getOrderStatus() == 3) {
                        operationManager.cancelOrder(-1L, oldOrder);
                    }
                }
            });
        }
    }

    public void generateOrder(List<OrderReq> orderList) {
        if (orderList == null || orderList.isEmpty()) {
            return;
        }
        Map<Long, Long> phoneNumMap = userMapper.selectList(new QueryWrapper<User>().select(User.ID, User.PHONE_NUM)
                .in(User.PHONE_NUM, orderList.stream().map(OrderReq::getPhoneNum).toList())).stream()
                .collect(Collectors.toMap(User::getPhoneNum, User::getId));
        for (OrderReq orderReq : orderList) {
            Long uid = phoneNumMap.get(orderReq.getPhoneNum());
            if (uid == null) {
                UserReq userReq = new UserReq();
                userReq.setPhone(String.valueOf(orderReq.getPhoneNum()));
                userReq.setCountryCode("86");
                UserItem userItem = fuyingUserFeign.getUserByPhone(userReq);
                User user = toUser(userItem);
                UnionItem unionId = fuyingUserFeign.getUnionId(uid);
                if (unionId != null) {
                    user.setUnionId(unionId.getThirdAuthId());
                }
                userMapper.insertOrUpdate(user);
                uid = userItem.getUid();
            }
            if (uid == null) {
                continue;
            }
            Order order = new Order();
            order.setUserId(uid);
            order.setCampId(orderReq.getCampId());
            order.setOrderNo(UUID.randomUUID().toString().replaceAll("-", ""));
            order.setOrderTime(LocalDateTime.now());
            orderMapper.insert(order);
            OrderStatistic statistic = new OrderStatistic();
            statistic.setOrderId(order.getId());
            orderStatisticMapper.insert(statistic);
        }
    }

    public void signOrder(SignItem signItem) {
        if (Integer.valueOf(0).equals(signItem.getIsSign()) || signItem.getSignTime() == null) {
            return;
        }
        Order order = orderMapper.selectOne(new QueryWrapper<Order>().select(Order.ID, Order.SIGN_TIME)
                .eq(Order.ORDER_NO, signItem.getNo()));
        if (order == null || order.getSignTime() != null) {
            return;
        }
        orderMapper.update(new UpdateWrapper<Order>().set(Order.SIGN_TIME, signItem.getSignTime())
                .eq(Order.ID, order.getId()));
        operationManager.saveLog(-1L, order.getId(), 4);
    }

    private User toUser(UserItem userItem) {
        User user = new User();
        user.setId(userItem.getUid());
        user.setUserIcon(userItem.getAvatarUrl());
        user.setRealName(userItem.getNickname());
        user.setNickName(userItem.getNickname());
        user.setPhoneNum(Long.valueOf(userItem.getPhone()));
        return user;
    }

    private Student toStudent(TraineeItem trainee) {
        Student student = new Student();
        student.setRealName(trainee.getName());
        student.setCardType(CardType.ID_CARD.getCode());
        student.setCardNum(trainee.getIdCard());
        return student;
    }

    private Order toOrder(OrderItem orderItem) {
        Order order = new Order();
        order.setUserId(orderItem.getUid());
        order.setStudentId(orderItem.getTraineeId());
        order.setCampId(orderItem.getCourseId());
        order.setOrderNo(orderItem.getNo());
        order.setOrderTime(orderItem.getCreateTime());
        order.setStudentFlag((byte) (orderItem.getServiceType() + 1));
        order.setOrderStatus(Integer.valueOf(7).equals(orderItem.getStatus()) ? (byte) 3 : 1);
        order.setRealOrderNo(orderItem.getOrderNo());
        return order;
    }
}
