package com.fuyingedu.training.admin.job;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.fasterxml.jackson.databind.JsonNode;
import com.fuyingedu.training.common.model.CommResp;
import com.fuyingedu.training.common.util.AsyncUtils;
import com.fuyingedu.training.common.util.JsonUtils;
import com.fuyingedu.training.common.util.RespUtils;
import com.fuyingedu.training.entity.Camp;
import com.fuyingedu.training.entity.Order;
import com.fuyingedu.training.entity.Schedule;
import com.fuyingedu.training.entity.Teacher;
import com.fuyingedu.training.front.feign.FuyingCourseFeign;
import com.fuyingedu.training.front.manager.OperationManager;
import com.fuyingedu.training.mapper.CampMapper;
import com.fuyingedu.training.mapper.OrderMapper;
import com.fuyingedu.training.mapper.ScheduleMapper;
import com.fuyingedu.training.mapper.TeacherMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("job")
public class OrderSignJob {

    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private ScheduleMapper scheduleMapper;
    @Autowired
    private CampMapper campMapper;
    @Autowired
    private FuyingCourseFeign fuyingCourseFeign;
    @Autowired
    private TeacherMapper teacherMapper;
    @Autowired
    private OperationManager operationManager;

    @GetMapping("order/sign")
    public CommResp<?> orderSign() {
        List<Camp> campList = campMapper.selectList(new QueryWrapper<Camp>().select(Camp.ID, Camp.SIGN_DAYS).gt(Camp.SIGN_DAYS, 0));
        if (campList.isEmpty()) {
            log.info("没有需要自动核销的训练营");
            return RespUtils.success();
        }
        AsyncUtils.execute(() -> orderSign(campList), "核销服务单脚本");
        return RespUtils.success();
    }

    @GetMapping("order/sync/record")
    public CommResp<?> syncRecord(@RequestParam(value = "startTime", required = false) LocalDateTime startTime,
                                  @RequestParam(value = "endTime", required = false) LocalDateTime endTime) {
        if (startTime == null) {
            startTime = LocalDate.now().minusDays(1).atStartOfDay();
        }
        if (endTime == null) {
            endTime = LocalDate.now().atStartOfDay();
        }
        List<Order> orderList = orderMapper.selectList(new LambdaQueryWrapper<Order>().select(Order::getOrderNo)
                .between(Order::getSignTime, startTime, endTime));
        if (orderList.isEmpty()) {
            log.info("没有需要同步的订单");
            return RespUtils.success();
        }
        int begin = 0;
        do {
            List<Order> subList = orderList.subList(begin, Math.min(begin + 20, orderList.size()));
            StringBuilder sb = new StringBuilder();
            for (Order order : subList) {
                sb.append(order.getOrderNo()).append(",");
            }
            String respStr = fuyingCourseFeign.syncRecord(sb.substring(0, sb.length() - 1));
            log.info("校验服务单核销结果：{}", respStr);
            begin += 20;
        } while (begin < orderList.size());
        return RespUtils.success();
    }

    private void orderSign(List<Camp> campList) {
        long startTime = System.currentTimeMillis();
        log.info("开始执行自动核销服务单");
        Map<Long, Camp> campMap = campList.stream().collect(Collectors.toMap(Camp::getId, Function.identity()));
        int maxSignDays = campList.stream().mapToInt(Camp::getSignDays).max().orElse(0);
        List<Schedule> scheduleList = scheduleMapper.selectList(new QueryWrapper<Schedule>().select(
                        Schedule.ID, Schedule.CAMP_ID, Schedule.SCHEDULE_ID
                ).gt(Schedule.START_TIME, LocalDateTime.now())
                .le(Schedule.START_TIME, LocalDateTime.now().plusDays(maxSignDays + 10))
                .in(Schedule.CAMP_ID, campMap.keySet()));
        for (Schedule schedule : scheduleList) {
            Camp camp = campMap.get(schedule.getCampId());
            LocalDateTime signTime = LocalDateTime.now().minusDays(camp.getSignDays());
            if (signTime.isBefore(schedule.getStartTime())) {
                List<Order> orderList = orderMapper.selectList(new QueryWrapper<Order>().select(
                        Order.ID, Order.ORDER_NO, Order.TEACHER_ID
                ).eq(Order.SCHEDULE_ID, schedule.getId()).isNull(Order.SIGN_TIME));
                if (orderList.isEmpty()) {
                    continue;
                }
                Set<Long> teacherIds = orderList.stream().map(Order::getTeacherId).filter(Objects::nonNull).collect(Collectors.toSet());
                Map<Long, Teacher> teacherMap = Collections.emptyMap();
                if (teacherIds.isEmpty()) {
                    teacherMap = teacherMapper.selectList(new QueryWrapper<Teacher>().select(
                                    Teacher.ID, Teacher.USER_ID
                            ).in(Teacher.ID, teacherIds)
                    ).stream().collect(Collectors.toMap(Teacher::getId, Function.identity()));
                }
                Map<String, Long> orderIdMap = orderList.stream().collect(Collectors.toMap(Order::getOrderNo, Order::getId));
                int begin = 0;
                do {
                    List<Order> subList = orderList.subList(begin, Math.min(begin + 20, orderList.size()));
                    List<Map<String, Object>> orderParams = new ArrayList<>(subList.size());
                    for (Order order : subList) {
                        Long teacherUid = 0L;
                        Teacher teacher = teacherMap.get(order.getTeacherId());
                        if (teacher != null) {
                            teacherUid = teacher.getUserId();
                        }
                        orderParams.add(Map.of("no", order.getOrderNo(), "signUid", teacherUid));
                    }
                    Map<String, Object> params = Map.of("courseScheduleId", schedule.getScheduleId(), "details", orderParams);
                    log.info("核销订单：{}", subList.stream().map(Order::getId).toList());
                    String sign = fuyingCourseFeign.sign(params);
                    log.info("核销结果：{}", sign);
                    JsonNode jsonNode = JsonUtils.parseJsonToJsonNode(sign);
                    if (!jsonNode.get("code").asText().equals("200")) {
                        log.error("核销失败");
                        return;
                    }
                    Set<Long> successOrderIds = new HashSet<>();
                    JsonNode dataList = jsonNode.get("data").get("list");
                    for (JsonNode node : dataList) {
                        Long orderId = orderIdMap.get(node.asText());
                        successOrderIds.add(orderId);
                    }
                    if (!successOrderIds.isEmpty()) {
                        orderMapper.update(new UpdateWrapper<Order>().set(Order.SIGN_TIME, LocalDateTime.now())
                                .in(Order.ID, successOrderIds));
                        operationManager.saveLog(-2L, successOrderIds, 4);
                    }
                    begin += 20;
                } while (begin < orderList.size());
            }
        }
        log.info("结束执行自动核销服务单[{}]", System.currentTimeMillis() - startTime);
    }
}
