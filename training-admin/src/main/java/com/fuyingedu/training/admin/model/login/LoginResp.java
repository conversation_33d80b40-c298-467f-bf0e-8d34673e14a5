package com.fuyingedu.training.admin.model.login;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Getter
@Setter
@ToString
public class LoginResp {

    private Long id;

    /**
     * 用户头像
     */
    private String userIcon;

    /**
     * 姓名
     */
    private String realName;

    private String token;

    /**
     * 角色
     */
    private List<Role> roleList;

    @Getter
    @Setter
    public static class Role {
        /**
         * 角色 ADMIN-管理员 TEACHER-老师
         * 在HTTP请求头中，使用X-Role-Name返回给我
         */
        private String role;
        private String desc;
    }
}
