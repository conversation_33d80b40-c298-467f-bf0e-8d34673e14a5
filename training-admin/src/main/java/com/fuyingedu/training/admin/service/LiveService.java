package com.fuyingedu.training.admin.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.fuyingedu.training.admin.model.live.Item;
import com.fuyingedu.training.admin.model.live.ItemResp;
import com.fuyingedu.training.admin.model.live.SaveReq;
import com.fuyingedu.training.admin.model.live.UpdateReq;
import com.fuyingedu.training.common.enums.CampType;
import com.fuyingedu.training.common.enums.RespMetaEnum;
import com.fuyingedu.training.common.enums.TeacherType;
import com.fuyingedu.training.common.exception.WebBaseException;
import com.fuyingedu.training.common.model.CommResp;
import com.fuyingedu.training.common.util.RespUtils;
import com.fuyingedu.training.entity.*;
import com.fuyingedu.training.front.manager.BaiJiaYunManager;
import com.fuyingedu.training.mapper.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class LiveService {

    @Autowired
    private LiveMapper liveMapper;
    @Autowired
    private ScheduleMapper scheduleMapper;
    @Autowired
    private CampMapper campMapper;
    @Autowired
    private TeacherMapper teacherMapper;
    @Autowired
    private ScheduleTeacherMapper scheduleTeacherMapper;
    @Autowired
    private ClazzMapper clazzMapper;
    @Autowired
    private ClazzRoomMapper clazzRoomMapper;
    @Autowired
    private BaiJiaYunManager baiJiaYunManager;
    @Autowired
    private UserMapper userMapper;

    public CommResp<List<ItemResp>> list(Long userId, Long scheduleId) {
        Schedule schedule = scheduleMapper.selectOne(new QueryWrapper<Schedule>()
                .select(Schedule.ID, Schedule.LIVE_PORT, Schedule.LIVE_TYPE, Schedule.LIVE_ROOM)
                .eq(Schedule.ID, scheduleId));
        boolean isBjy = Byte.valueOf((byte) 3).equals(schedule.getLivePort()) && Byte.valueOf((byte) 1).equals(schedule.getLiveType());
        String liveInfo = null;
        if (isBjy && userId != null) {
            Teacher teacher = teacherMapper.selectOne(new QueryWrapper<Teacher>().select(
                    Teacher.ID, Teacher.REAL_NAME
            ).eq(Teacher.USER_ID, userId));
            if (teacher == null) {
                return RespUtils.success(Collections.emptyList());
            }
            ScheduleTeacher scheduleTeacher = scheduleTeacherMapper.selectOne(new QueryWrapper<ScheduleTeacher>().select(
                    ScheduleTeacher.ID, ScheduleTeacher.TEACHER_TYPE
            ).eq(ScheduleTeacher.SCHEDULE_ID, scheduleId).eq(ScheduleTeacher.TEACHER_ID, teacher.getId()));
            if (scheduleTeacher == null) {
                return RespUtils.success(Collections.emptyList());
            }
            int role = 1;
            Clazz clazz;
            if (TeacherType.TEACHER.getCode().equals(scheduleTeacher.getTeacherType())) {
                clazz = clazzMapper.selectOne(new QueryWrapper<Clazz>().select(Clazz.ID).eq(Clazz.SCHEDULE_ID, scheduleId)
                        .eq(Clazz.TEACHER_ID, teacher.getId()).last("limit 1"));
            } else {
                clazz = clazzMapper.selectOne(new QueryWrapper<Clazz>().select(Clazz.ID).eq(Clazz.ASSISTANT_ID, teacher.getId())
                        .eq(Clazz.SCHEDULE_ID, scheduleId).last("limit 1"));
                role = 2;
            }
            Long roomId = schedule.getLiveRoom();
            if (clazz != null) {
                ClazzRoom clazzRoom = clazzRoomMapper.selectOne(new QueryWrapper<ClazzRoom>().select(
                        ClazzRoom.ID, ClazzRoom.ROOM_ID
                ).eq(ClazzRoom.CLAZZ_ID, clazz.getId()));
                roomId = clazzRoom.getRoomId();
            } else {
                role = 2;
            }
            User user = userMapper.selectOne(new QueryWrapper<User>().select(
                    User.ID, User.REAL_NAME, User.USER_ICON
            ).eq(User.ID, userId));
            liveInfo = baiJiaYunManager.getRoomUrl(roomId, user.getId(), role,
                    user.getRealName(), user.getUserIcon());
        }
        List<Live> liveList = liveMapper.selectList(new QueryWrapper<Live>()
                .select(
                        Live.SCHEDULE_ID, Live.ID, Live.LIVE_NAME, Live.START_TIME,
                        Live.LIVE_ROOM, Live.LIVE_INFO,
                        Live.LIVE_DURATION, Live.REPEAT_START_TIME, Live.REPEAT_END_TIME,
                        Live.REPEAT_URL, Live.LIVE_PASSWORD
                )
                .eq(Live.SCHEDULE_ID, scheduleId).orderByAsc(Live.START_TIME)
        );
        String finalLiveInfo = liveInfo;
        List<ItemResp> respList = liveList.stream().map(item -> {
            ItemResp resp = toItemResp(item);
            resp.setLiveType(schedule.getLiveType());
            resp.setLivePort(schedule.getLivePort());
            if (isBjy) {
                resp.setLiveInfo(finalLiveInfo);
            }
            return resp;
        }).toList();
        return RespUtils.success(respList);
    }

    @Transactional(rollbackFor = Exception.class)
    public void save(SaveReq saveReq) {
        Long scheduleId = saveReq.getScheduleId();
        Schedule schedule = scheduleMapper.selectOne(new QueryWrapper<Schedule>()
                .select(Schedule.ID, Schedule.CAMP_ID).eq(Schedule.ID, scheduleId));
        if (schedule == null) {
            throw new WebBaseException(RespMetaEnum.PARAM_ERROR);
        }
        Camp camp = campMapper.selectOne(new QueryWrapper<Camp>().select(Camp.ID, Camp.CAMP_TYPE).eq(Camp.ID, schedule.getCampId()));
        if (CampType.WORD.getCode().equals(camp.getCampType())) {
            updateWordLive(saveReq);
            return;
        }
        List<Long> ids = saveReq.getLiveList().stream().map(Item::getId).filter(Objects::nonNull).toList();
        Set<Long> liveSet = liveMapper.selectList(new QueryWrapper<Live>()
                .select(
                        Live.ID
                ).eq(Live.SCHEDULE_ID, scheduleId)
        ).stream().map(Live::getId).collect(Collectors.toSet());

        List<Live> addLiveList = new ArrayList<>(), updateLiveList = new ArrayList<>();
        List<Long> deteleteLiveList = new ArrayList<>();
        for (Item item : saveReq.getLiveList()) {
            if (item.getId() != null && liveSet.contains(item.getId())) {
                updateLiveList.add(toLive(item, scheduleId));
            } else {
                item.setId(null);
                addLiveList.add(toLive(item, scheduleId));
            }
        }
        for (Long id : liveSet) {
            if (!ids.contains(id)) {
                deteleteLiveList.add(id);
            }
        }
        scheduleMapper.update(new UpdateWrapper<Schedule>().set(Schedule.LIVE_NUM, saveReq.getLiveList().size())
                .eq(Schedule.ID, scheduleId));
        if (!addLiveList.isEmpty()) {
            liveMapper.insert(addLiveList);
        }
        if (!updateLiveList.isEmpty()) {
            liveMapper.updateById(updateLiveList);
        }
        if (!deteleteLiveList.isEmpty()) {
            liveMapper.deleteByIds(deteleteLiveList);
        }
    }

    private void updateWordLive(SaveReq saveReq) {
        Map<Long, Live> liveMap = liveMapper.selectList(new QueryWrapper<Live>()
                        .select(Live.ID, Live.START_TIME, Live.LIVE_NAME).eq(Live.SCHEDULE_ID, saveReq.getScheduleId()))
                .stream().collect(Collectors.toMap(Live::getId, v -> v));
        List<Live> updateLiveList = new ArrayList<>(liveMap.size());
        for (Item item : saveReq.getLiveList()) {
            if (item.getId() == null) {
                throw new WebBaseException(4000, "单词训练营不能新增直播");
            }
            Live live = liveMap.get(item.getId());
            if (!live.getStartTime().toLocalDate().equals(item.getStartTime().toLocalDate())) {
                throw new WebBaseException(4000, "单词训练营直播不能修改直播日期");
            }
            if (!live.getLiveName().equals(item.getLiveName())) {
                continue;
            }
            updateLiveList.add(toLive(item, saveReq.getScheduleId()));
        }
        if (!updateLiveList.isEmpty()) {
            liveMapper.updateById(updateLiveList);
        }
    }

    private Live toLive(Item item, Long scheduleId) {
        Live live = new Live();
        live.setId(item.getId());
        live.setScheduleId(scheduleId);
        live.setLiveName(item.getLiveName());
        live.setStartTime(item.getStartTime());
        live.setLiveRoom(item.getLiveRoom());
        live.setLiveDuration(item.getLiveDuration());
        live.setRepeatStartTime(item.getRepeatStartTime());
        live.setRepeatEndTime(item.getRepeatEndTime());
        return live;

    }

    private ItemResp toItemResp(Live live) {
        ItemResp itemResp = new ItemResp();
        itemResp.setScheduleId(live.getScheduleId());
        itemResp.setId(live.getId());
        itemResp.setLiveName(live.getLiveName());
        itemResp.setStartTime(live.getStartTime());
        itemResp.setLiveRoom(live.getLiveRoom());
        itemResp.setLiveDuration(live.getLiveDuration());
        itemResp.setRepeatStartTime(live.getRepeatStartTime());
        itemResp.setRepeatEndTime(live.getRepeatEndTime());
        itemResp.setLiveInfo(live.getLiveInfo());
        itemResp.setLivePassword(live.getLivePassword());
        itemResp.setRepeatUrl(live.getRepeatUrl());
        return itemResp;
    }

    public void update(UpdateReq updateReq) {
        liveMapper.update(new UpdateWrapper<Live>()
                .set(Live.LIVE_INFO, updateReq.getLiveInfo())
                .set(updateReq.getLiveRoom() != null, Live.LIVE_ROOM, updateReq.getLiveRoom())
                .set(updateReq.getLivePassword() != null, Live.LIVE_PASSWORD, updateReq.getLivePassword())
                .set(updateReq.getRepeatUrl() != null, Live.REPEAT_URL, updateReq.getRepeatUrl())
                .eq(Live.ID, updateReq.getId()));
    }
}
