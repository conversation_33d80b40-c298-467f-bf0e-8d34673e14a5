package com.fuyingedu.training.admin.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fuyingedu.training.common.enums.OrderStatus;
import com.fuyingedu.training.common.enums.ScheduleStatus;
import com.fuyingedu.training.common.enums.TeacherType;
import com.fuyingedu.training.entity.Clazz;
import com.fuyingedu.training.entity.Order;
import com.fuyingedu.training.entity.ScheduleTeacher;
import com.fuyingedu.training.entity.ScheduleTeacherStatistic;
import com.fuyingedu.training.mapper.OrderMapper;
import com.fuyingedu.training.mapper.ScheduleTeacherStatisticMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Service
public class ScheduleTeacherStatisticService {

    @Autowired
    private ScheduleTeacherStatisticMapper scheduleTeacherStatisticMapper;
    @Autowired
    private OrderMapper orderMapper;

    public Integer getOrderNum(Long teacherId, ScheduleTeacher scheduleTeacher, List<Clazz> clazzList, Byte scheduleStatus) {
        ScheduleTeacherStatistic statistic = scheduleTeacherStatisticMapper.selectOne(new QueryWrapper<ScheduleTeacherStatistic>().select(
                ScheduleTeacherStatistic.ID, ScheduleTeacherStatistic.SCHEDULE_STATUS, ScheduleTeacherStatistic.ORDER_NUM
        ).eq(ScheduleTeacherStatistic.TEACHER_ID, teacherId).eq(ScheduleTeacherStatistic.SCHEDULE_ID, scheduleTeacher.getScheduleId()));
        if (statistic == null || !ScheduleStatus.END.getCode().equals(statistic.getScheduleStatus())) {
            Long count = null;
            if (TeacherType.TEACHER.getCode().equals(scheduleTeacher.getTeacherType())) {
                count = orderMapper.selectCount(new QueryWrapper<Order>()
                        .eq(Order.SCHEDULE_ID, scheduleTeacher.getScheduleId())
                        .eq(Order.TEACHER_ID, teacherId)
                        .in(Order.ORDER_STATUS, OrderStatus.NORMAL.getCode(), OrderStatus.MAY_REFUND.getCode()));
            } else if (!CollectionUtils.isEmpty(clazzList)) {
                count = orderMapper.selectCount(new QueryWrapper<Order>()
                        .eq(Order.SCHEDULE_ID, scheduleTeacher.getScheduleId())
                        .in(Order.CLAZZ_ID, clazzList.stream().map(Clazz::getId).toList())
                        .in(Order.ORDER_STATUS, OrderStatus.NORMAL.getCode(), OrderStatus.MAY_REFUND.getCode()));
            }
            if (statistic == null) {
                statistic = new ScheduleTeacherStatistic();
                statistic.setScheduleId(scheduleTeacher.getScheduleId());
                statistic.setTeacherId(teacherId);
            }
            statistic.setScheduleStatus(scheduleStatus);
            if (count != null) {
                statistic.setOrderNum(count.intValue());
            } else {
                statistic.setOrderNum(0);
            }
            scheduleTeacherStatisticMapper.insertOrUpdate(statistic);
        }
        return statistic.getOrderNum();
    }
}
