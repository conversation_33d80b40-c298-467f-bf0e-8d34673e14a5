package com.fuyingedu.training.admin.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.fuyingedu.training.admin.model.order.remark.ItemResp;
import com.fuyingedu.training.admin.model.order.remark.SaveReq;
import com.fuyingedu.training.common.enums.ConfirmStatus;
import com.fuyingedu.training.common.enums.RespMetaEnum;
import com.fuyingedu.training.common.model.CommResp;
import com.fuyingedu.training.common.util.RespUtils;
import com.fuyingedu.training.entity.Order;
import com.fuyingedu.training.entity.OrderRemark;
import com.fuyingedu.training.entity.ScheduleTeacher;
import com.fuyingedu.training.entity.Teacher;
import com.fuyingedu.training.mapper.OrderMapper;
import com.fuyingedu.training.mapper.OrderRemarkMapper;
import com.fuyingedu.training.mapper.ScheduleTeacherMapper;
import com.fuyingedu.training.mapper.TeacherMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class OrderRemarkService {

    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private OrderRemarkMapper orderRemarkMapper;
    @Autowired
    private TeacherMapper teacherMapper;
    @Autowired
    private ScheduleTeacherMapper scheduleTeacherMapper;

    public CommResp<List<ItemResp>> list(Long orderId) {
        Order order = orderMapper.selectOne(new QueryWrapper<Order>().select(
                Order.ID,
                Order.SCHEDULE_ID
        ).eq(Order.ID, orderId));
        if (order == null) {
            return RespUtils.warning(RespMetaEnum.PARAM_ERROR);
        }
        List<OrderRemark> orderRemarks = orderRemarkMapper.selectList(new QueryWrapper<OrderRemark>().select(
                OrderRemark.RECORD_USER_ID,
                OrderRemark.REMARK_TYPE,
                OrderRemark.REMARK_CONTENT,
                OrderRemark.CREATED_TIME
        ).eq(OrderRemark.ORDER_ID, orderId));
        if (orderRemarks.isEmpty()) {
            return RespUtils.success(Collections.emptyList());
        }
        List<Long> userIds = orderRemarks.stream().map(OrderRemark::getRecordUserId).distinct().toList();
        Map<Long, Teacher> teacherMap = teacherMapper.selectList(new QueryWrapper<Teacher>().select(
                Teacher.USER_ID,
                Teacher.ID,
                Teacher.REAL_NAME
        ).in(Teacher.USER_ID, userIds)).stream().collect(
                Collectors.toMap(Teacher::getUserId, teacher -> teacher)
        );
        return RespUtils.success(orderRemarks.stream().map(orderRemark -> {
            ItemResp itemResp = new ItemResp();
            Teacher teacher = teacherMap.get(orderRemark.getRecordUserId());
            itemResp.setRealName(teacher.getRealName());
            itemResp.setRemarkContent(orderRemark.getRemarkContent());
            itemResp.setRemarkType(orderRemark.getRemarkType());
            itemResp.setCreatedTime(orderRemark.getCreatedTime());
            return itemResp;
        }).toList());
    }

    @Transactional(rollbackFor = Exception.class)
    public void save(Long userId, SaveReq saveReq) {
        OrderRemark orderRemark = toOrderRemark(userId, saveReq);
        orderRemarkMapper.insert(orderRemark);
        orderMapper.update(new UpdateWrapper<Order>().set(Order.CONFIRM_STATUS, saveReq.getRemarkType())
                .set(ConfirmStatus.TEACHER_CONFIRM.getCode().equals(saveReq.getRemarkType()), Order.CONFIRM_TIME, LocalDateTime.now())
                .eq(Order.ID, saveReq.getOrderId()));
    }

    private OrderRemark toOrderRemark(Long userId, SaveReq saveReq) {
        OrderRemark orderRemark = new OrderRemark();
        orderRemark.setRecordUserId(userId);
        orderRemark.setOrderId(saveReq.getOrderId());
        orderRemark.setRemarkType(saveReq.getRemarkType());
        orderRemark.setRemarkContent(saveReq.getRemarkContent());
        return orderRemark;
    }
}
