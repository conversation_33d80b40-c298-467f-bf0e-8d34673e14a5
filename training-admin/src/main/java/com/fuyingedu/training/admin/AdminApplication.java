package com.fuyingedu.training.admin;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;


@EnableCaching
@EnableDiscoveryClient
@EnableFeignClients(basePackages = "com.fuyingedu.training.front.feign")
@MapperScan(basePackages = "com.fuyingedu.training.mapper")
@SpringBootApplication(scanBasePackages = "com.fuyingedu.training")
public class AdminApplication {

    public static void main(String[] args) {
        System.setProperty("rocketmq.client.log.additive", "true");
        System.setProperty("rocketmq.client.logUseSlf4j", "true");
        SpringApplication.run(AdminApplication.class, args);
    }
}
