package com.fuyingedu.training.admin.model.word;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class ItemResp {

    /**
     * 单词书主键
     */
    private Long trainingCampPlanId;
    /**
     * 课程天数
     */
    private Integer days;

    /**
     * 直播的天数集合
     */
    private List<Integer> liveDayList;

    /**
     * 直播的名称集合
     */
    private List<String> liveNameList;

    /**
     * 词书名称
     */
    private String name;

    /**
     * 词书描述
     */
    private String planDesc;

    /**
     * 类型 1：1/3/5学练测计划 9：自定义计划
     */
    private Byte planType;
}
