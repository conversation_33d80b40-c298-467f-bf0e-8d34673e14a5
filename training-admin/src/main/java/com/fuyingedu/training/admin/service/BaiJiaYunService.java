package com.fuyingedu.training.admin.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.databind.JsonNode;
import com.fuyingedu.training.admin.model.bjy.*;
import com.fuyingedu.training.common.annotation.UserInfo;
import com.fuyingedu.training.common.enums.RespMetaEnum;
import com.fuyingedu.training.common.enums.Status;
import com.fuyingedu.training.common.model.CommResp;
import com.fuyingedu.training.common.util.DateUtils;
import com.fuyingedu.training.common.util.RespUtils;
import com.fuyingedu.training.entity.*;
import com.fuyingedu.training.front.manager.BaiJiaYunManager;
import com.fuyingedu.training.mapper.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class BaiJiaYunService {

    private static final String BJY_BASE_URL = "https://e49679728.at.baijiacloud.com/web/room/prepare?room_id=%s&code=%s";

    @Autowired
    private BaiJiaYunManager baiJiaYunManager;
    @Autowired
    private ScheduleMapper scheduleMapper;
    @Autowired
    private ClazzMapper clazzMapper;
    @Autowired
    private ClazzRoomMapper clazzRoomMapper;
    @Autowired
    private LiveMapper liveMapper;
    @Autowired
    private ScheduleRoomMapper scheduleRoomMapper;
    @Autowired
    private CampMapper campMapper;
    @Autowired
    private LiveQuizRelationMapper liveQuizRelationMapper;
    @Autowired
    private LiveSignMapper liveSignMapper;
    @Autowired
    private LiveQuizConfigMapper liveQuizConfigMapper;
    @Autowired
    private UserMapper userMapper;
    @Autowired
    private TeacherMapper teacherMapper;
    @Autowired
    private LiveAnswerMapper liveAnswerMapper;
    @Autowired
    private AvailableCampMapper availableCampMapper;

    public CommResp<List<RoomResp>> roomList(UserInfo userInfo, RoomReq req) {
        Long userId = userInfo.getUserId();
        List<Long> authScheduleIds = null;
        if (userInfo.getRoleIds() != null && userInfo.getRoleIds().contains(3L)) {
            List<Long> campIds = availableCampMapper.selectList(new QueryWrapper<AvailableCamp>().select(
                            AvailableCamp.CAMP_ID
                    ).eq(AvailableCamp.AVAILABLE_ID, userInfo.getUserId()).eq(AvailableCamp.AVAILABLE_TYPE, 4).eq(AvailableCamp.CAMP_STATUS, 1))
                    .stream().map(AvailableCamp::getAvailableId).toList();
            if (campIds.isEmpty()) {
                return RespUtils.warning(RespMetaEnum.NO_AUTH);
            }
            authScheduleIds = scheduleMapper.selectList(new QueryWrapper<Schedule>().select(Schedule.ID)
                            .in(Schedule.CAMP_ID, campIds))
                    .stream().map(Schedule::getId).toList();
        }
        List<Long> scheduleIds = Collections.emptyList();
        if (StringUtils.hasLength(req.getCampName())) {
            List<Camp> campList = campMapper.selectList(new QueryWrapper<Camp>().select(Camp.ID)
                    .like(Camp.CAMP_NAME, req.getCampName()));
            if (!campList.isEmpty()) {
                scheduleIds = scheduleMapper.selectList(new QueryWrapper<Schedule>().select(Schedule.ID)
                                .in(Schedule.ID, campList.stream().map(Camp::getId).toList()))
                        .stream().map(Schedule::getId).toList();
            }
            if (scheduleIds.isEmpty()) {
                return RespUtils.success(Collections.emptyList());
            }
        }
        if (StringUtils.hasLength(req.getScheduleName())) {
            scheduleIds = scheduleMapper.selectList(new QueryWrapper<Schedule>().select(Schedule.ID)
                            .like(Schedule.SCHEDULE_NAME, req.getScheduleName())
                            .in(!scheduleIds.isEmpty(), Schedule.ID, scheduleIds))
                    .stream().map(Schedule::getId).toList();
            if (scheduleIds.isEmpty()) {
                return RespUtils.success(Collections.emptyList());
            }
        }
        List<Long> finalAuthScheduleIds = authScheduleIds;
        IPage<ScheduleRoom> page = new Page<>(req.getPageNum(), req.getPageSize());
        page = scheduleRoomMapper.selectPage(page, new QueryWrapper<ScheduleRoom>().select(
                                ScheduleRoom.ID, ScheduleRoom.ROOM_ID, ScheduleRoom.ROOM_NAME,
                                ScheduleRoom.ROOM_TIME, ScheduleRoom.TEACHER_CODE, ScheduleRoom.SCHEDULE_ID,
                                ScheduleRoom.ADMIN_CODE, ScheduleRoom.STUDENT_CODE, ScheduleRoom.REPEAT_NUM,
                ScheduleRoom.ROOM_NUM, ScheduleRoom.QUIZ_NUM
                        ).eq(req.getRoomId() != null, ScheduleRoom.ROOM_ID, req.getRoomId())
                        .in(!scheduleIds.isEmpty(), ScheduleRoom.SCHEDULE_ID, scheduleIds)
                        .isNotNull(Status.NORMAL.getCode().equals(req.getRoomStatus()), ScheduleRoom.SCHEDULE_ID)
                        .isNull(Status.DELETE.getCode().equals(req.getRoomStatus()), ScheduleRoom.SCHEDULE_ID)
                        .and(authScheduleIds != null, queryWrapper -> queryWrapper
                                .in(ScheduleRoom.SCHEDULE_ID, finalAuthScheduleIds).or().isNull(ScheduleRoom.SCHEDULE_ID))
                        .orderByDesc(ScheduleRoom.ID)
        );
        scheduleIds = page.getRecords().stream().map(ScheduleRoom::getScheduleId).filter(Objects::nonNull).toList();
        Map<Long, Schedule> scheduleMap = Collections.emptyMap();
        Map<Long, Camp> campMap = Collections.emptyMap();
        if (!scheduleIds.isEmpty()) {
            List<Schedule> scheduleList = scheduleMapper.selectList(new QueryWrapper<Schedule>().select(
                    Schedule.ID, Schedule.CAMP_ID, Schedule.SCHEDULE_NAME
            ).in(Schedule.ID, scheduleIds));
            scheduleMap = scheduleList.stream().collect(Collectors.toMap(Schedule::getId, v -> v));
            List<Long> campIds = scheduleList.stream().map(Schedule::getCampId).distinct().toList();
            campMap = campMapper.selectList(new QueryWrapper<Camp>().select(Camp.ID, Camp.CAMP_NAME).in(Camp.ID, campIds))
                    .stream().collect(Collectors.toMap(Camp::getId, v -> v));
        }
        User user = userMapper.selectOne(new QueryWrapper<User>().select(
                User.ID, User.REAL_NAME, User.USER_ICON
        ).eq(User.ID, userId));
        List<RoomResp> respList = new ArrayList<>(page.getRecords().size());
        for (ScheduleRoom scheduleRoom : page.getRecords()) {
            RoomResp resp = new RoomResp();
            resp.setRoomId(scheduleRoom.getRoomId());
            resp.setRoomTime(scheduleRoom.getRoomTime());
            resp.setRoomName(scheduleRoom.getRoomName());
            resp.setRepeatNum(scheduleRoom.getRepeatNum());
            resp.setGroupNum(scheduleRoom.getRoomNum());
            resp.setQuizNum(scheduleRoom.getQuizNum());
            Schedule schedule = scheduleMap.get(scheduleRoom.getScheduleId());
            resp.setScheduleId(scheduleRoom.getScheduleId());
            if (schedule != null) {
                resp.setScheduleName(schedule.getScheduleName());
                Camp camp = campMap.get(schedule.getCampId());
                resp.setCampId(camp.getId());
                resp.setCampName(camp.getCampName());
            }
            resp.setTeacherCode(scheduleRoom.getTeacherCode()).setAdminCode(scheduleRoom.getAdminCode())
                    .setStudentCode(scheduleRoom.getStudentCode());
            resp.setTeacherUrl(baiJiaYunManager.getRoomUrl(scheduleRoom.getRoomId(), user.getId(),
                    1, user.getRealName(), user.getUserIcon()));
            resp.setAdminUrl(baiJiaYunManager.getRoomUrl(scheduleRoom.getRoomId(), user.getId(),
                    2, user.getRealName(), user.getUserIcon()));
            respList.add(resp);
        }
        return RespUtils.success(page.getCurrent(), page.getSize(), page.getTotal(), respList);
    }

    public CommResp<List<UnbindRoomResp>> unbindRoomList(Long scheduleId) {
        List<ScheduleRoom> scheduleRoomList = scheduleRoomMapper.selectList(new QueryWrapper<ScheduleRoom>()
                .select(ScheduleRoom.ROOM_ID, ScheduleRoom.ROOM_NAME)
                .isNull(ScheduleRoom.SCHEDULE_ID).or().eq(ScheduleRoom.SCHEDULE_ID, scheduleId).orderByDesc(ScheduleRoom.ID));
        return RespUtils.success(scheduleRoomList.stream().map(scheduleRoom -> {
            UnbindRoomResp resp = new UnbindRoomResp();
            resp.setRoomId(scheduleRoom.getRoomId());
            resp.setRoomName(scheduleRoom.getRoomName());
            return resp;
        }).toList());
    }

    public CommResp<?> flushRoom() {
        JsonNode jsonNode = baiJiaYunManager.getRoomList();
        JsonNode list = jsonNode.get("list");
        if (list == null || list.isEmpty()) {
            return RespUtils.success();
        }
        List<ScheduleRoom> scheduleRoomList = new ArrayList<>();
        for (JsonNode node : list) {
            if (node.get("new_group_live").asInt(0) != 2) {
                continue;
            }
            ScheduleRoom scheduleRoom = new ScheduleRoom();
            scheduleRoom.setRoomId(node.get("room_id").asLong());
            scheduleRoom.setRoomName(node.get("title").asText());
            scheduleRoom.setRoomTime(DateUtils.secondToLocalDateTime(node.get("create_time").asLong()));
            scheduleRoom.setTeacherCode(node.get("teacher_code").asText());
            scheduleRoom.setAdminCode(node.get("admin_code").asText());
            scheduleRoom.setStudentCode(node.get("student_code").asText());
            scheduleRoomList.add(scheduleRoom);
        }
        if (CollectionUtils.isEmpty(scheduleRoomList)) {
            return RespUtils.success();
        }
        Map<Long, Long> roomIdMap = scheduleRoomMapper.selectList(new QueryWrapper<ScheduleRoom>().select(
                        ScheduleRoom.ID, ScheduleRoom.ROOM_ID
                ).in(ScheduleRoom.ROOM_ID, scheduleRoomList.stream().map(ScheduleRoom::getRoomId).toList()))
                .stream().collect(Collectors.toMap(ScheduleRoom::getRoomId, ScheduleRoom::getId));
        for (ScheduleRoom scheduleRoom : scheduleRoomList) {
            scheduleRoom.setId(roomIdMap.get(scheduleRoom.getRoomId()));
        }
        scheduleRoomMapper.insertOrUpdate(scheduleRoomList);
        return RespUtils.success();
    }

    public CommResp<List<GroupItemResp>> groupList(Long roomId) {
        Schedule schedule = scheduleMapper.selectOne(new QueryWrapper<Schedule>().select(Schedule.ID)
                .eq(Schedule.LIVE_ROOM, roomId));
        if (schedule == null) {
            return RespUtils.success(Collections.emptyList());
        }
        List<ClazzRoom> clazzRooms = clazzRoomMapper.selectList(new QueryWrapper<ClazzRoom>().select(
                ClazzRoom.ID, ClazzRoom.ROOM_ID, ClazzRoom.ROOM_NAME, ClazzRoom.CLAZZ_ID,
                ClazzRoom.TEACHER_CODE, ClazzRoom.ADMIN_CODE, ClazzRoom.STUDENT_CODE, ClazzRoom.CREATED_TIME
        ).eq(ClazzRoom.SCHEDULE_ID, schedule.getId()));
        if (clazzRooms.isEmpty()) {
            return RespUtils.success(Collections.emptyList());
        }
        Map<Long, Clazz> clazzMap = clazzMapper.selectList(new QueryWrapper<Clazz>()
                        .select(Clazz.ID, Clazz.CLASS_NAME, Clazz.TEACHER_ID)
                        .in(Clazz.ID, clazzRooms.stream().map(ClazzRoom::getClazzId).distinct().toList()))
                .stream().collect(Collectors.toMap(Clazz::getId, clazz -> clazz));
        Map<Long, Teacher> teacherMap = teacherMapper.selectList(new QueryWrapper<Teacher>().select(
                Teacher.ID, Teacher.REAL_NAME
        ).in(Teacher.ID, clazzMap.values().stream().map(Clazz::getTeacherId).distinct().toList()))
                .stream().collect(Collectors.toMap(Teacher::getId, teacher -> teacher));
        List<GroupItemResp> respList = new ArrayList<>();
        for (ClazzRoom clazzRoom : clazzRooms) {
            GroupItemResp item = new GroupItemResp();
            item.setGroupId(clazzRoom.getId());
            item.setRoomId(clazzRoom.getRoomId());
            item.setRoomName(clazzRoom.getRoomName());
            item.setClazzId(clazzRoom.getClazzId());
            Clazz clazz = clazzMap.get(clazzRoom.getClazzId());
            Teacher teacher = teacherMap.get(clazz.getTeacherId());
            item.setTeacherName(teacher.getRealName());
            item.setClazzName(clazz.getClassName());
            item.setTeacherCode(clazzRoom.getTeacherCode());
            item.setAssistantCode(clazzRoom.getAdminCode());
            item.setStudentCode(clazzRoom.getStudentCode());
            item.setTeacherUrl(String.format(BJY_BASE_URL, clazzRoom.getRoomId(), clazzRoom.getTeacherCode()));
            item.setAssistantUrl(String.format(BJY_BASE_URL, clazzRoom.getRoomId(), clazzRoom.getAdminCode()));
            item.setStudentUrl(String.format(BJY_BASE_URL, clazzRoom.getRoomId(), clazzRoom.getStudentCode()));
            item.setCreatedTime(clazzRoom.getCreatedTime());
            respList.add(item);
        }
        return RespUtils.success(respList);
    }

    public CommResp<?> updateGroup(GroupUpdateReq req) {
        ClazzRoom clazzRoom = clazzRoomMapper.selectOne(new QueryWrapper<ClazzRoom>().select(
                ClazzRoom.ID, ClazzRoom.SCHEDULE_ID, ClazzRoom.GROUP_ID
        ).eq(ClazzRoom.ID, req.getGroupId()));
        if (clazzRoom == null) {
            return RespUtils.warning(4000, "该教室不存在");
        }
        Schedule schedule = scheduleMapper.selectOne(new QueryWrapper<Schedule>().select(Schedule.LIVE_ROOM)
                .eq(Schedule.ID, clazzRoom.getScheduleId()));
        baiJiaYunManager.updateGroupName(schedule.getLiveRoom(), clazzRoom.getGroupId(), req.getGroupName());
        clazzRoomMapper.update(new UpdateWrapper<ClazzRoom>()
                .set(ClazzRoom.ROOM_NAME, req.getGroupName())
                .eq(ClazzRoom.ID, clazzRoom.getId())
        );
        return RespUtils.success();
    }

    public CommResp<List<PlaybackResp>> playbackList(Long roomId) {
        Schedule schedule = scheduleMapper.selectOne(new QueryWrapper<Schedule>().select(
                Schedule.ID, Schedule.SCHEDULE_NAME
        ).eq(Schedule.LIVE_ROOM, roomId));
        if (schedule == null) {
            return RespUtils.success(Collections.emptyList());
        }
        Map<Long, Live> liveMap = liveMapper.selectList(new QueryWrapper<Live>().select(
                        Live.ID, Live.LIVE_NAME, Live.REPEAT_VIDEO_ID
                ).eq(Live.SCHEDULE_ID, schedule.getId()).isNotNull(Live.REPEAT_VIDEO_ID))
                .stream().collect(Collectors.toMap(Live::getRepeatVideoId, live -> live));
        JsonNode jsonNode = baiJiaYunManager.playbackList(roomId);
        int total = jsonNode.get("total").asInt();
        scheduleRoomMapper.update(new UpdateWrapper<ScheduleRoom>().set(ScheduleRoom.REPEAT_NUM, total)
                .eq(ScheduleRoom.ROOM_ID, roomId));
        JsonNode list = jsonNode.get("list");
        if (list == null || list.isEmpty()) {
            return RespUtils.success(Collections.emptyList());
        }
        List<PlaybackResp> respList = new ArrayList<>();
        for (JsonNode node : list) {
            PlaybackResp resp = new PlaybackResp();
            resp.setVideoId(node.get("video_id").asLong());
            LocalDateTime createdTime = DateUtils.parseDateTime(node.get("create_time").asText());
            resp.setStartTime(createdTime);
            int length = node.get("length").asInt();
            resp.setEndTime(createdTime.plusSeconds(length)).setLength(length);
            resp.setScheduleId(schedule.getId()).setScheduleName(schedule.getScheduleName());
            resp.setPlayUrl(node.get("play_url").asText());
            Live live = liveMap.get(resp.getVideoId());
            if (live != null) {
                resp.setLiveId(live.getId()).setLiveName(live.getLiveName());
            }
            respList.add(resp);
        }
        return RespUtils.success(respList);
    }

    public CommResp<List<LiveResp>> liveList(Long roomId) {
        Schedule schedule = scheduleMapper.selectOne(new QueryWrapper<Schedule>().select(
                Schedule.ID
        ).eq(Schedule.LIVE_ROOM, roomId));
        if (schedule == null) {
            return RespUtils.success(Collections.emptyList());
        }
        List<Live> liveList = liveMapper.selectList(new QueryWrapper<Live>().select(
                Live.ID, Live.LIVE_NAME
        ).eq(Live.SCHEDULE_ID, schedule.getId()).isNull(Live.REPEAT_URL));
        return RespUtils.success(liveList.stream().map(live -> new LiveResp().setLiveId(live.getId()).setLiveName(live.getLiveName())).toList());
    }

    public CommResp<?> bind(BindReq req) {
        Live live = liveMapper.selectOne(new QueryWrapper<Live>().select(
                Live.ID, Live.SCHEDULE_ID
        ).eq(Live.ID, req.getLiveId()));
        long count = liveMapper.selectCount(new QueryWrapper<Live>()
                .eq(Live.SCHEDULE_ID, live.getScheduleId()).eq(Live.REPEAT_VIDEO_ID, req.getVideoId()));
        if (count > 0) {
            return RespUtils.warning(4000, "该视频已绑定");
        }
        liveMapper.update(new UpdateWrapper<Live>().set(Live.REPEAT_URL, req.getPlayUrl())
                        .set(Live.REPEAT_VIDEO_ID, req.getVideoId())
                .eq(Live.ID, req.getLiveId()).isNull(Live.REPEAT_URL));
        return RespUtils.success();
    }

    public CommResp<?> unbind(Long liveId) {
        liveMapper.update(new UpdateWrapper<Live>().set(Live.REPEAT_VIDEO_ID, null).set(Live.REPEAT_URL, null).eq(Live.ID, liveId));
        return RespUtils.success();
    }

    public CommResp<List<QuizResp>> quizList(Long roomId) {
        Schedule schedule = scheduleMapper.selectOne(new QueryWrapper<Schedule>().select(
                Schedule.ID, Schedule.SCHEDULE_NAME
        ).eq(Schedule.LIVE_ROOM, roomId));
        JsonNode jsonNode = baiJiaYunManager.roomQuiz(roomId);
        JsonNode quizList = jsonNode.get("quiz_list");
        if (quizList == null || quizList.isEmpty()) {
            return RespUtils.success(Collections.emptyList());
        }
        List<QuizResp> respList = new ArrayList<>();
        Set<Long> quizIds = new HashSet<>();
        for (JsonNode node : quizList) {
            Long quizId = node.get("quiz_id").asLong();
            quizIds.add(quizId);
            QuizResp resp = new QuizResp();
            resp.setQuizId(quizId).setQuizName(node.get("title").asText()).setScheduleId(schedule.getId());
            resp.setScheduleName(schedule.getScheduleName());
            respList.add(resp);
        }
        Map<Long, Long> liveQuizMap = liveQuizRelationMapper.selectList(new QueryWrapper<LiveQuizRelation>().select(
                LiveQuizRelation.LIVE_ID, LiveQuizRelation.QUIZ_ID
        ).in(LiveQuizRelation.QUIZ_ID, quizIds)).stream().collect(Collectors.toMap(
                LiveQuizRelation::getQuizId, LiveQuizRelation::getLiveId));
        Map<Long, Live> liveMap = Collections.emptyMap();
        if (!liveQuizMap.isEmpty()) {
            liveMap = liveMapper.selectList(new QueryWrapper<Live>().select(
                    Live.ID, Live.LIVE_NAME
            ).in(Live.ID, liveQuizMap.values())).stream().collect(Collectors.toMap(Live::getId, live -> live));
        }
        for (QuizResp resp : respList) {
            Long liveId = liveQuizMap.get(resp.getQuizId());
            if (liveId != null) {
                Live live = liveMap.get(liveId);
                resp.setLiveId(liveId).setLiveName(live.getLiveName());
            }
        }
        scheduleRoomMapper.update(new UpdateWrapper<ScheduleRoom>().set(ScheduleRoom.QUIZ_NUM, quizList.size())
                .eq(ScheduleRoom.ROOM_ID, roomId));
        return RespUtils.success(respList);
    }

    public CommResp<SignResp> signSetting(Long liveId) {
        LiveSign liveSign = liveSignMapper.selectOne(new QueryWrapper<LiveSign>().select(
                LiveSign.ID, LiveSign.TASK_REWARD, LiveSign.FXY_REWARD, LiveSign.WORD_REWARD, LiveSign.SIGN_STATUS
        ).eq(LiveSign.LIVE_ID, liveId));
        if (liveSign == null) {
            return RespUtils.success(new SignResp());
        }
        SignResp resp = new SignResp();
        resp.setSignStatus(liveSign.getSignStatus()).setTaskReward(liveSign.getTaskReward())
                .setFxyReward(liveSign.getFxyReward()).setWordReward(liveSign.getWordReward());
        return RespUtils.success(resp);
    }

    public CommResp<?> updateSign(SignUpdateReq req) {
        LiveSign liveSign = liveSignMapper.selectOne(new QueryWrapper<LiveSign>().select(
                LiveSign.ID
        ).eq(LiveSign.LIVE_ID, req.getLiveId()));
        if (liveSign == null) {
            liveSign = new LiveSign();
            liveSign.setLiveId(req.getLiveId());
        }
        liveSign.setTaskReward(req.getTaskReward()).setFxyReward(req.getFxyReward())
                .setWordReward(req.getWordReward()).setSignStatus(req.getSignStatus());
        liveSignMapper.insertOrUpdate(liveSign);
        return RespUtils.success();
    }

    public CommResp<QuizSettingResp> quizSetting(Long roomId, Long liveId) {
        LiveQuizConfig quizConfig = liveQuizConfigMapper.selectOne(new QueryWrapper<LiveQuizConfig>().select(
                LiveQuizConfig.ID, LiveQuizConfig.TASK_REWARD, LiveQuizConfig.FXY_REWARD, LiveQuizConfig.WORD_REWARD
        ).eq(LiveQuizConfig.LIVE_ID, liveId));
        if (quizConfig == null) {
            return RespUtils.success(new QuizSettingResp());
        }
        QuizSettingResp resp = new QuizSettingResp();
        resp.setTaskReward(quizConfig.getTaskReward()).setFxyReward(quizConfig.getFxyReward())
                .setWordReward(quizConfig.getWordReward()).setQuizStatus((byte) 2);
        JsonNode jsonNode = baiJiaYunManager.roomQuiz(roomId);
        JsonNode quizList = jsonNode.get("quiz_list");
        if (quizList == null || quizList.isEmpty()) {
            return RespUtils.success(resp);
        }
        List<Long> quizIds = new ArrayList<>();
        for (JsonNode node : quizList) {
            quizIds.add(node.get("quiz_id").asLong());
        }
        quizIds = liveQuizRelationMapper.selectList(new QueryWrapper<LiveQuizRelation>().select(
                LiveQuizRelation.QUIZ_ID
        ).in(LiveQuizRelation.QUIZ_ID, quizIds))
                .stream().map(LiveQuizRelation::getQuizId).toList();
        List<QuizSettingResp.Quiz> quizListResp = new ArrayList<>();
        for (JsonNode node : quizList) {
            Long quizId = node.get("quiz_id").asLong();
            if (quizIds.contains(quizId)) {
                QuizSettingResp.Quiz quiz = new QuizSettingResp.Quiz();
                quiz.setQuizId(quizId).setQuizName(node.get("title").asText());
                quizListResp.add(quiz);
            }
        }
        resp.setQuizList(quizListResp);
        return RespUtils.success(resp);
    }

    public CommResp<?> updateQuiz(QuizSettingReq req) {
        LiveQuizConfig quizConfig = liveQuizConfigMapper.selectOne(new QueryWrapper<LiveQuizConfig>().select(
                LiveQuizConfig.ID
        ).eq(LiveQuizConfig.LIVE_ID, req.getLiveId()));
        if (quizConfig == null) {
            quizConfig = new LiveQuizConfig();
            quizConfig.setLiveId(req.getLiveId());
        }
        quizConfig.setTaskReward(req.getTaskReward()).setFxyReward(req.getFxyReward()).setWordReward(req.getWordReward());
        liveQuizConfigMapper.insertOrUpdate(quizConfig);
        return RespUtils.success();
    }

    public CommResp<?> addQuiz(QuizLiveReq req) {
        Live live = liveMapper.selectOne(new QueryWrapper<Live>().select(
                Live.ID, Live.SCHEDULE_ID
        ).eq(Live.ID, req.getLiveId()));
        Schedule schedule = scheduleMapper.selectOne(new QueryWrapper<Schedule>().select(
                Schedule.ID, Schedule.LIVE_ROOM
        ).eq(Schedule.ID, live.getScheduleId()));
        List<Long> oldQuizIds = liveQuizRelationMapper.selectList(new QueryWrapper<LiveQuizRelation>().select(
                LiveQuizRelation.QUIZ_ID
        ).in(LiveQuizRelation.QUIZ_ID, req.getQuizIds()))
                .stream().map(LiveQuizRelation::getQuizId).toList();
        List<Long> newQuizIds = req.getQuizIds().stream().filter(quizId -> !oldQuizIds.contains(quizId)).toList();
        if (newQuizIds.isEmpty()) {
            return RespUtils.success();
        }
        List<LiveQuizRelation> relationList = new ArrayList<>(newQuizIds.size());
        for (Long quizId : newQuizIds) {
            LiveQuizRelation relation = new LiveQuizRelation();
            relation.setQuizId(quizId).setLiveId(req.getLiveId()).setRoomId(schedule.getLiveRoom());
            relationList.add(relation);
        }
        liveQuizRelationMapper.insert(relationList);
        return RespUtils.success();
    }

    public CommResp<?> deleteQuiz(Long liveId, Long quizId) {
        liveQuizRelationMapper.delete(new QueryWrapper<LiveQuizRelation>().eq(LiveQuizRelation.QUIZ_ID, quizId)
                .eq(LiveQuizRelation.LIVE_ID, liveId));
        return RespUtils.success();
    }

    public CommResp<SignResp> answerSetting(Long liveId) {
        LiveAnswer liveSign = liveAnswerMapper.selectOne(new QueryWrapper<LiveAnswer>().select(
                LiveAnswer.ID, LiveAnswer.TASK_REWARD, LiveAnswer.FXY_REWARD, LiveAnswer.WORD_REWARD, LiveAnswer.ANSWER_STATUS
        ).eq(LiveSign.LIVE_ID, liveId));
        if (liveSign == null) {
            return RespUtils.success(new SignResp());
        }
        SignResp resp = new SignResp();
        resp.setSignStatus(liveSign.getAnswerStatus()).setTaskReward(liveSign.getTaskReward())
                .setFxyReward(liveSign.getFxyReward()).setWordReward(liveSign.getWordReward());
        return RespUtils.success(resp);
    }

    public CommResp<?> updateAnswer(SignUpdateReq req) {
        LiveAnswer liveSign = liveAnswerMapper.selectOne(new QueryWrapper<LiveAnswer>().select(
                LiveAnswer.ID
        ).eq(LiveAnswer.LIVE_ID, req.getLiveId()));
        if (liveSign == null) {
            liveSign = new LiveAnswer();
            liveSign.setLiveId(req.getLiveId());
        }
        liveSign.setTaskReward(req.getTaskReward()).setFxyReward(req.getFxyReward())
                .setWordReward(req.getWordReward()).setSyncStatus((byte) 1).setAnswerStatus(req.getSignStatus());
        liveAnswerMapper.insertOrUpdate(liveSign);
        return RespUtils.success();
    }
}
