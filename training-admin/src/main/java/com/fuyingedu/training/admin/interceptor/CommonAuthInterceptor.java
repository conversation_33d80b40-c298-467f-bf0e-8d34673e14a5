package com.fuyingedu.training.admin.interceptor;

import com.auth0.jwt.exceptions.TokenExpiredException;
import com.fuyingedu.training.common.enums.RespMetaEnum;
import com.fuyingedu.training.common.util.JsonUtils;
import com.fuyingedu.training.common.util.JwtUtils;
import com.fuyingedu.training.common.util.RespUtils;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.util.StringUtils;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Map;

@Slf4j
public abstract class CommonAuthInterceptor implements HandlerInterceptor {
    @Override
    public boolean preHandle(HttpServletRequest request, @NotNull HttpServletResponse response, @NotNull Object handler) throws IOException {
        if (HttpMethod.OPTIONS.name().equals(request.getMethod())) {
            return true;
        }
        if (!(handler instanceof HandlerMethod)) {
            return true;
        }
        return checkAuth(request, response, handler);
    }

    protected abstract boolean checkAuth(HttpServletRequest request, HttpServletResponse response, Object handler) throws IOException;

    protected void respNotLogin(HttpServletResponse response) throws IOException {
        resp(response, RespMetaEnum.NOT_LOGIN);
    }

    protected void resp(HttpServletResponse response, RespMetaEnum metaEnum) throws IOException {
        response.setStatus(HttpStatus.OK.value());
        response.setContentType(MediaType.APPLICATION_JSON_VALUE);
        response.setCharacterEncoding(StandardCharsets.UTF_8.name());
        response.getWriter().write(JsonUtils.formatObjToJson(RespUtils.warning(metaEnum)));
    }

    protected void setUserInfo(HttpServletRequest request, Object userInfo) {
        request.setAttribute("userInfo", userInfo);
    }

    protected Object getUserInfo(HttpServletRequest request) {
        return request.getAttribute("userInfo");
    }

    protected Map<String, Object> parseToken(HttpServletRequest request, HttpServletResponse response) throws IOException {
        String token = request.getHeader(HttpHeaders.AUTHORIZATION);
        if (!StringUtils.hasLength(token)) {
            respNotLogin(response);
            return null;
        }
        token = token.trim();
        Map<String, Object> tokenParams = null;
        try {
            tokenParams = JwtUtils.verifyToken(token);
        } catch (TokenExpiredException e) {
            log.info("Token过期", e);
        } catch (NumberFormatException e) {
            log.error("解析用户ID错误", e);
        } catch (Exception e) {
            log.error("解析Token错误", e);
        }
        if (tokenParams == null) {
            respNotLogin(response);
            return null;
        }
        return tokenParams;
    }
}
