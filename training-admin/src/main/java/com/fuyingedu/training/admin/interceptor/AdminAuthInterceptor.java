package com.fuyingedu.training.admin.interceptor;

import com.fuyingedu.training.admin.interceptor.auth.AuthorizeManager;
import com.fuyingedu.training.common.annotation.PreAuthorize;
import com.fuyingedu.training.common.enums.RespMetaEnum;
import com.fuyingedu.training.common.util.JwtUtils;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.util.ContentCachingRequestWrapper;

import java.io.IOException;
import java.util.Map;
import java.util.Set;

@Slf4j
@Component
public class AdminAuthInterceptor extends CommonAuthInterceptor {

    @Autowired
    private AuthorizeManager authorizeManager;

    @Override
    public boolean checkAuth(HttpServletRequest request, @NotNull HttpServletResponse response, @NotNull Object handler) throws IOException {
        Map<String, Object> tokenParams = parseToken(request, response);
        if (tokenParams == null) {
            return false;
        }
        Object userId = tokenParams.get("userId");
        setUserInfo(request, userId);
        Object type = tokenParams.get("type");
        if (!"Admin".equals(type)) {
            respNotLogin(response);
            return false;
        }
        long expiredTime = (Long) tokenParams.get("expiredTime");
        long hourMillis = 60 * 60 * 1000;
        if (expiredTime - System.currentTimeMillis() < hourMillis) {
            String newToken = JwtUtils.createAdminToken(userId);
            response.setHeader(HttpHeaders.AUTHORIZATION, newToken);
        }
        if (handler instanceof HandlerMethod handlerMethod) {
            PreAuthorize authorize = handlerMethod.getMethodAnnotation(PreAuthorize.class);
            if (authorize == null) {
                return true;
            }
            boolean auth = authorizeManager.initAuth(userId, authorize.value(), request);
            if (!auth) {
                resp(response, RespMetaEnum.NO_AUTH);
                return false;
            }
        }
        return true;
    }

    @Override
    public void postHandle(@NotNull HttpServletRequest request,
                           @NotNull HttpServletResponse response,
                           @NotNull Object handler,
                           @Nullable ModelAndView modelAndView) throws Exception {
        if (!HttpMethod.POST.name().equals(request.getMethod())) {
            return;
        }
        if (log.isInfoEnabled() && request instanceof ContentCachingRequestWrapper requestToUse) {
            String uri = request.getRequestURI();
            if (uri != null && uri.length() > 20) {
                uri = uri.substring(20);
            }
            log.info("[{}][{}][{}]", getUserInfo(request), uri, requestToUse.getContentAsString());
        }
    }
}
