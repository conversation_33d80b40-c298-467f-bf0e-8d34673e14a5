package com.fuyingedu.training.admin.controller;

import com.alibaba.excel.EasyExcel;
import com.fuyingedu.training.admin.model.teacher.*;
import com.fuyingedu.training.admin.service.TeacherService;
import com.fuyingedu.training.common.annotation.Login;
import com.fuyingedu.training.common.annotation.PreAuthorize;
import com.fuyingedu.training.common.model.CommResp;
import com.fuyingedu.training.common.util.RespUtils;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * 讲师管理
 */
@RestController
@RequestMapping("admin/teacher")
@Slf4j
public class TeacherController {

    @Autowired
    private TeacherService teacherService;

    /**
     * 查询讲师列表
     */
    @PreAuthorize("管理员")
    @GetMapping("list")
    public CommResp<List<ItemResp>> list(@Valid ListReq listReq) {
        return teacherService.list(listReq);
    }

    /**
     * 添加/编辑讲师
     */
    @PreAuthorize("管理员")
    @PostMapping("save")
    public CommResp<?> addOrEdit(@RequestBody @Valid SaveReq saveReq) {
        teacherService.addOrEdit(saveReq);
        return RespUtils.success();
    }

    /**
     * 修改导师备注
     */
    @PostMapping("remark")
    public CommResp<?> remark(@Login Long userId, @RequestBody @Valid RemarkReq req) {
        return teacherService.remark(userId, req);
    }

    /**
     * 有效的教师列表
     */
    @GetMapping("valid/list")
    public CommResp<List<ValidItemResp>> validList(@RequestParam("scheduleId") Long scheduleId) {
        return teacherService.validList(scheduleId);
    }

    /**
     * 讲师详情
     */
    @GetMapping("detail")
    public CommResp<DetailResp> detail(@RequestParam("id") Long id) {
        return teacherService.detail(id);
    }

    /**
     * 讲师搜索:根据手机号搜索老师
     */
    @GetMapping("search")
    public CommResp<SearchResp> search(@RequestParam("phoneNum") Long phoneNum) {
        return teacherService.search(phoneNum);
    }

    /**
     * 导师端-我的信息
     * @param userId 前端不需要传
     */
    @GetMapping("info")
    public CommResp<InfoResp> info(@Login Long userId) {
        return teacherService.info(userId);
    }

    /**
     * 导师端-我的带导资格
     * @param userId 前端不需要传
     */
    @GetMapping("camp/list")
    public CommResp<List<CampItemResp>> campList(@Login Long userId) {
        return teacherService.campList(userId);
    }

    /**
     * 导师端-我的课程与学员
     * @param userId 前端不需要传
     */
    @GetMapping("course/list")
    public CommResp<List<CourseItemResp>> courseList(@Login Long userId) {
        return teacherService.courseList(userId);
    }

    /**
     * 导师端-我的直播列表
     * @param userId 前端不需要传
     */
    @GetMapping("live/list")
    public CommResp<List<LiveItemResp>> liveList(@Login Long userId) {
        return teacherService.liveList(userId);
    }

    /**
     * 导师端-我的直播列表下载
     * @param userId 前端不需要传
     */
    @GetMapping("live/download")
    public void liveDownload(@Login Long userId, HttpServletResponse response) throws IOException {
        // 设置文本内省
        response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
        // 设置响应头
        response.setHeader("Content-disposition", "attachment;filename=" + URLEncoder.encode("我的直播列表", StandardCharsets.UTF_8) +".xlsx");
        List<LiveItemResp> liveList = teacherService.liveList(userId).getData();
        EasyExcel.write(response.getOutputStream(), LiveItemResp.class).sheet("我的直播列表").doWrite(liveList);
    }

    /**
     * 导师端-我的训练营列表
     * @param userId 前端不需要传
     */
    @GetMapping("schedule/list")
    public CommResp<List<ScheduleItemResp>> scheduleList(@Login Long userId) {
        return teacherService.scheduleList(userId);
    }

    /**
     * 导师端-我的训练营详情
     * @param userId 前端不需要传
     */
    @GetMapping("schedule/info")
    public CommResp<ScheduleDetailResp> scheduleDetail(@Login Long userId, @RequestParam("scheduleId") Long scheduleId) {
        return teacherService.scheduleDetail(userId, scheduleId);
    }

    /**
     * 导师端-助教列表
     * @param userId 前端不需要传
     */
    @GetMapping("assistant/list")
    public CommResp<List<AssistantItemResp>> assistantList(@Login Long userId, @RequestParam("scheduleId") Long scheduleId) {
        return teacherService.assistantList(userId, scheduleId);
    }

}
