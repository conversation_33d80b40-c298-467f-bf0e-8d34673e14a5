package com.fuyingedu.training.admin.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fuyingedu.training.admin.model.student.*;
import com.fuyingedu.training.common.enums.RespMetaEnum;
import com.fuyingedu.training.common.enums.ScheduleStatus;
import com.fuyingedu.training.common.enums.Status;
import com.fuyingedu.training.common.enums.TaskType;
import com.fuyingedu.training.common.exception.WebBaseException;
import com.fuyingedu.training.common.model.CommResp;
import com.fuyingedu.training.common.util.RespUtils;
import com.fuyingedu.training.dto.order.StudentNumRet;
import com.fuyingedu.training.entity.*;
import com.fuyingedu.training.front.manager.OperationManager;
import com.fuyingedu.training.front.model.media.MediaConvertor;
import com.fuyingedu.training.front.service.FrontOrderService;
import com.fuyingedu.training.mapper.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class StudentService {

    @Autowired
    private StudentMapper studentMapper;
    @Autowired
    private CampMapper campMapper;
    @Autowired
    private ScheduleMapper scheduleMapper;
    @Autowired
    private UserMapper userMapper;
    @Autowired
    private UserRemarkMapper userRemarkMapper;
    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private OrderStatisticMapper orderStatisticMapper;
    @Autowired
    private ClazzMapper clazzMapper;
    @Autowired
    private GroupMapper groupMapper;
    @Autowired
    private LabelMapper labelMapper;
    @Autowired
    private TaskRecordLabelMapper taskRecordLabelMapper;
    @Autowired
    private MedalRecordMapper medalRecordMapper;
    @Autowired
    private OrderMedalMapper orderMedalMapper;
    @Autowired
    private OrderAssistantMapper orderAssistantMapper;
    @Autowired
    private TeacherMapper teacherMapper;
    @Autowired
    private TaskMapper taskMapper;
    @Autowired
    private TaskSubmitRecordMapper taskSubmitRecordMapper;
    @Autowired
    private TaskRecordRemarkMapper taskRecordRemarkMapper;
    @Autowired
    private FrontOrderService frontOrderService;
    @Autowired
    private LiveMapper liveMapper;
    @Autowired
    private WordRewardMapper wordRewardMapper;
    @Autowired
    private OrderFxyMapper orderFxyMapper;
    @Autowired
    private FxyMapper fxyMapper;
    @Autowired
    private OperationManager operationManager;

    public CommResp<List<ItemResp>> list(ListReq listReq) {
        IPage<Student> page = new Page<>(listReq.getPageNum(), listReq.getPageSize());
        page = studentMapper.selectPage(page, new QueryWrapper<Student>().select(
                        Student.ID,
                        Student.REAL_NAME,
                        Student.OLD_OUTER_ID,
                        Student.NEW_OUTER_ID,
                        Student.CARD_TYPE,
                        Student.CARD_NUM
            ).like(StringUtils.hasLength(listReq.getRealName()), Student.REAL_NAME, listReq.getRealName())
                .like(StringUtils.hasLength(listReq.getCartNum()), Student.CARD_NUM, listReq.getCartNum())
                .orderByDesc(Student.ID)
        );
        List<Long> studentIds = page.getRecords().stream().map(Student::getId).filter(Objects::nonNull).distinct().toList();
        Map<Long, Integer> studentNumMap = Collections.emptyMap();
        if (!studentIds.isEmpty()) {
            studentNumMap = orderMapper.groupStudentNum(studentIds).stream()
                    .collect(Collectors.toMap(StudentNumRet::getStudentId, StudentNumRet::getStudentNum));
        }
        List<ItemResp> respList = new ArrayList<>(page.getRecords().size());
        for (Student student : page.getRecords()) {
            ItemResp itemResp = toItemResp(student);
            itemResp.setCampNum(studentNumMap.getOrDefault(student.getId(), 0));
            respList.add(itemResp);
        }
        return RespUtils.success(page.getCurrent(), page.getSize(), page.getTotal(), respList);
    }

    public CommResp<DetailResp> detail(Long id) {
        Student student = studentMapper.selectOne(new QueryWrapper<Student>().select(
                Student.ID, Student.REAL_NAME,
                Student.CARD_TYPE,
                Student.CARD_NUM
        ).eq(Student.ID, id));
        if (student == null) {
            throw new WebBaseException(RespMetaEnum.PARAM_ERROR);
        }
        List<Long> userIds = orderMapper.selectList(new QueryWrapper<Order>().select(
                Order.USER_ID
        ).eq(Order.STUDENT_ID, id)).stream().map(Order::getUserId).distinct().toList();
        DetailResp detailResp = toDetailResp(student);
        List<UserRemark> remarkList;
        if (userIds.isEmpty()) {
            remarkList = Collections.emptyList();
        } else {
            remarkList = userRemarkMapper.selectList(new QueryWrapper<UserRemark>().select(
                    UserRemark.CREATED_TIME,
                    UserRemark.REMARK_CONTENT,
                    UserRemark.REMARK_USER_ID,
                    UserRemark.ID,
                    UserRemark.IMAGE_URLS, UserRemark.VIDEO_URLS
            ).in(UserRemark.USER_ID, userIds).eq(UserRemark.REMARK_STATUS, Status.NORMAL.getCode()).orderByDesc(UserRemark.ID));
        }
        if (!remarkList.isEmpty()) {
            Map<Long, String> remarkUserMap = userMapper.selectList(new QueryWrapper<User>().select(
                            User.ID,
                            User.REAL_NAME
                    ).in(User.ID, remarkList.stream().map(UserRemark::getRemarkUserId).toList()))
                    .stream().collect(Collectors.toMap(User::getId, User::getRealName));
            detailResp.setRemarkList(remarkList.stream().map(remark -> {
                String username = remarkUserMap.get(remark.getRemarkUserId());
                return toRemark(remark, username);
            }).toList());
        }
        List<Order> orderList = orderMapper.selectList(new QueryWrapper<Order>().select(
                Order.CAMP_ID,
                Order.SCHEDULE_ID
        ).eq(Order.STUDENT_ID, student.getId()).orderByDesc(Order.ID));
        if (orderList.isEmpty()) {
            return RespUtils.success(detailResp);
        }
        List<Long> campIds = orderList.stream().map(Order::getCampId).toList();
        List<Long> scheduleIds = orderList.stream().map(Order::getScheduleId).toList();
        Map<Long, Camp> campMap = campMapper.selectList(new QueryWrapper<Camp>().select(
                Camp.ID,
                Camp.CAMP_NAME
        ).in(Camp.ID, campIds)).stream().collect(Collectors.toMap(Camp::getId, v -> v));
        Map<Long, Schedule> schduleMap = scheduleMapper.selectList(new QueryWrapper<Schedule>().select(
                Schedule.ID,
                Schedule.START_TIME,
                Schedule.END_TIME,
                Schedule.SCHEDULE_NAME
        ).in(Schedule.ID, scheduleIds)).stream().collect(Collectors.toMap(Schedule::getId, v -> v));
        List<DetailResp.Camp> campList = new ArrayList<>(orderList.size());
        LocalDateTime now = scheduleMapper.queryNow();
        for (Order order : orderList) {
            DetailResp.Camp respCamp = new DetailResp.Camp();
            Camp camp = campMap.get(order.getCampId());
            respCamp.setId(camp.getId());
            respCamp.setCampName(camp.getCampName());
            Schedule schedule = schduleMap.get(order.getScheduleId());
            respCamp.setScheduleName(schedule.getScheduleName());
            respCamp.setScheduleStatus(ScheduleStatus.convert(schedule.getStartTime(), schedule.getEndTime(), now).getCode());
            campList.add(respCamp);
        }
        detailResp.setCampList(campList);
        return RespUtils.success(detailResp);
    }

    private ItemResp toItemResp(Student userFy) {
        ItemResp itemResp = new ItemResp();
        itemResp.setId(userFy.getId());
        itemResp.setRealName(userFy.getRealName());
        itemResp.setNewOuterId(userFy.getNewOuterId());
        itemResp.setOldOuterId(userFy.getOldOuterId());
        itemResp.setCardType(userFy.getCardType());
        itemResp.setCardNum(userFy.getCardNum());
        return itemResp;
    }

    private DetailResp toDetailResp(Student userFy) {
        DetailResp detailResp = new DetailResp();
        detailResp.setRealName(userFy.getRealName());
        detailResp.setCardType(userFy.getCardType());
        detailResp.setCardNum(userFy.getCardNum());
        return detailResp;
    }

    private DetailResp.Remark toRemark(UserRemark userRemark, String remarkUserName) {
        DetailResp.Remark remark = new DetailResp.Remark();
        remark.setId(userRemark.getId());
        remark.setContent(userRemark.getRemarkContent());
        remark.setRealName(remarkUserName);
        remark.setRemarkTime(userRemark.getCreatedTime());
        if (StringUtils.hasLength(userRemark.getImageUrls())) {
            remark.setImageUrlList(MediaConvertor.getUrlList(userRemark.getImageUrls()));
        }
        if (StringUtils.hasLength(userRemark.getVideoUrls())) {
            remark.setVideoUrlList(MediaConvertor.getUrlList(userRemark.getVideoUrls()));
        }
        return remark;

    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteRemark(Long userId, Long id) {
        int update = userRemarkMapper.update(new UpdateWrapper<UserRemark>()
                .set(UserRemark.REMARK_STATUS, Status.DELETE.getCode()).eq(UserRemark.ID, id));
        if (update > 0) {
            operationManager.saveLog(userId, id, 5, id);
        }
    }

    public CommResp<InfoResp> info(Long orderId) {
        Order order = orderMapper.selectOne(new QueryWrapper<Order>().select(
                Order.SCHEDULE_ID, Order.TEACHER_ID, Order.GROUP_ID, Order.CLAZZ_ID,
                Order.USER_ID, Order.STUDENT_TYPE, Order.STUDENT_ID
        ).eq(Order.ID, orderId));
        InfoResp infoResp = new InfoResp();
        infoResp.setStudentType(order.getStudentType());
        Set<Long> userIds = new HashSet<>();
        userIds.add(order.getUserId());
        OrderStatistic orderRecord = orderStatisticMapper.selectOne(new QueryWrapper<OrderStatistic>().select(
                OrderStatistic.TASK_REWARD, OrderStatistic.GOOD_HOMEWORK_NUM
        ).eq(OrderStatistic.ORDER_ID, orderId));
        infoResp.setTaskReward(orderRecord.getTaskReward());
        WordReward wordReward = wordRewardMapper.selectOne(new QueryWrapper<WordReward>().select(
                WordReward.USER_ID, WordReward.REWARD_NUM
        ).eq(WordReward.ORDER_ID, order.getId()));
        if (wordReward != null) {
            infoResp.setWordReward(wordReward.getRewardNum());
        } else {
            infoResp.setWordReward(0);
        }
        List<TaskSubmitRecord> taskSubmitRecords = taskSubmitRecordMapper.selectList(new QueryWrapper<TaskSubmitRecord>().select(
                TaskSubmitRecord.ORDER_ID, TaskSubmitRecord.RECORD_TYPE
        ).eq(TaskSubmitRecord.ORDER_ID, orderId).eq(TaskSubmitRecord.TASK_TYPE, TaskType.HOMEWORK.getCode()).eq(TaskSubmitRecord.RECORD_TYPE, 2));
        infoResp.setRecommendNum(taskSubmitRecords.size());
        List<Long> assistantUserList = orderAssistantMapper.selectList(new QueryWrapper<OrderAssistant>().select(
                OrderAssistant.USER_ID
        ).eq(OrderAssistant.ORDER_ID, orderId)).stream().map(OrderAssistant::getUserId).toList();
        userIds.addAll(assistantUserList);

        List<TaskRecordLabel> taskRecordLabels = taskRecordLabelMapper.selectList(new QueryWrapper<TaskRecordLabel>().select(
                TaskRecordLabel.CREATE_USER_ID, TaskRecordLabel.LABEL_ID
        ).eq(TaskRecordLabel.ORDER_ID, orderId));
        Map<Long, List<TaskRecordLabel>> recordLabelMap = taskRecordLabels.stream().collect(Collectors.groupingBy(TaskRecordLabel::getLabelId));
        userIds.addAll(taskRecordLabels.stream().map(TaskRecordLabel::getCreateUserId).toList());
        List<Label> labelList = Collections.emptyList();
        if (!recordLabelMap.isEmpty()) {
            labelList = labelMapper.selectList(new QueryWrapper<Label>().select(
                    Label.ID, Label.LABEL_NAME, Label.MEDIA_URL, Label.LABEL_TYPE
            ).in(Label.ID, recordLabelMap.keySet()).eq(Label.LABEL_TYPE, 1L).orderByDesc(Label.ID));
        }
        List<Long> mediaIds = orderMedalMapper.selectList(new QueryWrapper<OrderMedal>().select(
                OrderMedal.MEDAL_ID
        ).eq(OrderMedal.ORDER_ID, orderId)).stream().map(OrderMedal::getMedalId).distinct().toList();
        List<MedalRecord> medalRecordList = Collections.emptyList();
        if (!mediaIds.isEmpty()) {
            medalRecordList = medalRecordMapper.selectList(new QueryWrapper<MedalRecord>().select(
                    MedalRecord.MEDAL_NAME, MedalRecord.MEDAL_ICON, MedalRecord.MEDAL_CONTENT, MedalRecord.CREATED_TIME,
                    MedalRecord.CREATED_USER_ID
            ).in(MedalRecord.ID, mediaIds).orderByDesc(MedalRecord.ID));
            userIds.addAll(medalRecordList.stream().map(MedalRecord::getCreatedUserId).toList());
        }
        Map<Long, User> userMap = userMapper.selectList(new QueryWrapper<User>().select(
                User.ID, User.REAL_NAME, User.USER_ICON, User.PHONE_NUM
        ).in(User.ID, userIds)).stream().collect(Collectors.toMap(User::getId, v -> v));

        User currUser = userMap.get(order.getUserId());
        infoResp.setId(currUser.getId());
        infoResp.setUserIcon(currUser.getUserIcon());
        infoResp.setRealName(currUser.getRealName());
        if (order.getStudentId() != null) {
            Student student = studentMapper.selectOne(new QueryWrapper<Student>().select(Student.REAL_NAME).eq(Student.ID, order.getStudentId()));
            infoResp.setRealName(student.getRealName());
        }
        List<InfoResp.User> userList = new ArrayList<>(3);
        userList.add(toUser(currUser, (byte) 1));
        for (Long assistantUserId : assistantUserList) {
            userList.add(toUser(userMap.get(assistantUserId), (byte) 2));
        }
        infoResp.setUserList(userList);

        Clazz clazz = clazzMapper.selectOne(new QueryWrapper<Clazz>().select(
                Clazz.CLASS_NAME, Clazz.ASSISTANT_ID
        ).eq(Clazz.ID, order.getClazzId()));
        if (clazz == null) {
            return RespUtils.success(infoResp);
        }
        infoResp.setClassName(clazz.getClassName());
        Group group = groupMapper.selectOne(new QueryWrapper<Group>().select(
                Group.GROUP_NAME
        ).eq(Group.ID, order.getGroupId()));
        if (group != null) {
            infoResp.setGroupName(group.getGroupName());
        }
        Long clazzTeacherId = teacherMapper.selectOne(new QueryWrapper<Teacher>()
                .select(Teacher.USER_ID).eq(Teacher.ID, clazz.getAssistantId())).getUserId();
        infoResp.setMedalList(medalRecordList.stream().map(medalRecord -> {
            InfoResp.Medal medal = toMedal(medalRecord, userMap.get(order.getUserId()));
            User teacher = userMap.get(medalRecord.getCreatedUserId());
            medal.setTeacherName(teacher.getRealName());
            medal.setTeacherType((byte) (clazzTeacherId.equals(teacher.getId()) ? 2 : 1));
            return medal;
        }).toList());

        infoResp.setLabelList(labelList.stream().map(label -> {
            InfoResp.Label labelResp = toLabel(label, userMap.get(order.getUserId()));
            List<TaskRecordLabel> taskRecordLabelList = recordLabelMap.get(label.getId());
            if (taskRecordLabelList != null) {
                labelResp.setLabelNum(taskRecordLabelList.size());
                labelResp.setTeacherList(taskRecordLabelList.stream().map(taskRecordLabel -> {
                    InfoResp.LabelTeacher labelTeacher = new InfoResp.LabelTeacher();
                    User teacher = userMap.get(taskRecordLabel.getCreateUserId());
                    labelTeacher.setTeacherName(teacher.getRealName());
                    labelTeacher.setTeacherType((byte) (clazzTeacherId.equals(teacher.getId()) ? 2 : 1));
                    labelTeacher.setLabelNum(labelTeacher.getLabelNum() == null ? 1 : labelTeacher.getLabelNum() + 1);
                    return labelTeacher;
                }).toList());
            }
            return labelResp;
        }).toList());
        Live live = liveMapper.selectOne(new QueryWrapper<Live>().select(
                Live.ID
        ).eq(Live.SCHEDULE_ID, order.getScheduleId()).last("limit 1"));
        List<InfoResp.TaskType> taskTypeList = new ArrayList<>();
        if (live != null) {
            taskTypeList.add(new InfoResp.TaskType((byte) 4, "直播"));
        }
        Set<Long> taskIds = frontOrderService.getOrderTaskIds(order);
        if (!taskIds.isEmpty()) {
            Set<Byte> taskTypeSet = taskMapper.selectList(new QueryWrapper<Task>().select(
                    Task.TASK_TYPE
            ).in(Task.ID, taskIds)).stream().map(Task::getTaskType).collect(Collectors.toSet());
            for (Byte taskType : taskTypeSet) {
                TaskType type = TaskType.getByCode(taskType);
                if (type != null) {
                    taskTypeList.add(new InfoResp.TaskType(taskType, type.getDesc()));
                }
            }
        }
        infoResp.setTaskTypeList(taskTypeList.stream().sorted(Comparator.comparing(InfoResp.TaskType::getType)).toList());

        OrderFxy orderFxy = orderFxyMapper.selectOne(new QueryWrapper<OrderFxy>().select(
                OrderFxy.FXY_ID
        ).eq(OrderFxy.ORDER_ID, orderId));
        if (orderFxy != null) {
            Fxy fxy = fxyMapper.selectOne(new QueryWrapper<Fxy>().select(
                    Fxy.OPEN_ID, Fxy.NICK_NAME, Fxy.ACCOUNT_INFO, Fxy.ICON_URL
            ).eq(Fxy.ID, orderFxy.getFxyId()));
            infoResp.setFxyAccountInfo(fxy.getAccountInfo());
            infoResp.setFxyNickName(fxy.getNickName());
        }
        return RespUtils.success(infoResp);
    }

    private InfoResp.User toUser(User user, Byte type) {
        InfoResp.User respUser = new InfoResp.User();
        respUser.setUserId(user.getId());
        respUser.setUserIcon(user.getUserIcon());
        respUser.setRealName(user.getRealName());
        respUser.setPhoneNum(user.getPhoneNum());
        respUser.setAccountType(type);
        return respUser;
    }

    private InfoResp.Medal toMedal(MedalRecord medalRecord, User user) {
        InfoResp.Medal medal = new InfoResp.Medal();
        medal.setMedalName(medalRecord.getMedalName());
        medal.setTemplateName(medalRecord.getTemplateName());
        medal.setMedalIcon(medalRecord.getMedalIcon());
        medal.setMedalContent(medalRecord.getMedalContent());
        medal.setMedalTime(medalRecord.getCreatedTime());
        medal.setRealName(user.getRealName());
        return medal;
    }

    private InfoResp.Label toLabel(Label label, User user) {
        InfoResp.Label respLabel = new InfoResp.Label();
        respLabel.setLabelName(label.getLabelName());
        respLabel.setLabelIcon(MediaConvertor.getMediaUrl(label.getMediaUrl()));
        respLabel.setLabelType(label.getLabelType());
        return respLabel;
    }

    public CommResp<List<TaskItemResp>> taskList(Long orderId, Byte type) {
        Order order = orderMapper.selectOne(new QueryWrapper<Order>().select(
                Order.ID, Order.SCHEDULE_ID, Order.TEACHER_ID, Order.CLAZZ_ID
        ).eq(Order.ID, orderId));
        Set<Long> taskIds = frontOrderService.getOrderTaskIds(order);
        if (taskIds.isEmpty()) {
            return RespUtils.success(Collections.emptyList());
        }
        List<Task> taskList = taskMapper.selectList(new QueryWrapper<Task>().select(
                                Task.ID, Task.TASK_NAME, Task.START_DATE, Task.END_DATE, Task.TASK_CONTENT
                        ).in(Task.ID, taskIds).eq(Task.TASK_TYPE, type)
        );
        List<TaskItemResp> taskItemRespList = taskList.stream().map(this::toTaskItemResp).toList();
        if (TaskType.HOMEWORK.getCode().equals(type)) {
            Map<Long, TaskSubmitRecord> submitTaskIds = taskSubmitRecordMapper.selectList(new QueryWrapper<TaskSubmitRecord>().select(
                            TaskSubmitRecord.ID, TaskSubmitRecord.TASK_ID, TaskSubmitRecord.LIKE_NUM, TaskSubmitRecord.RECORD_TYPE
                    ).eq(TaskSubmitRecord.ORDER_ID, orderId).in(TaskSubmitRecord.TASK_ID, taskList.stream().map(Task::getId).toList()))
                    .stream().collect(Collectors.toMap(TaskSubmitRecord::getTaskId, Function.identity()));
            for (TaskItemResp taskItemResp : taskItemRespList) {
                TaskSubmitRecord record = submitTaskIds.get(taskItemResp.getId());
                if (record != null) {
                    taskItemResp.setTaskStatus((byte) 3);
                    taskItemResp.setLikeNum(record.getLikeNum());
                    taskItemResp.setRecordType(record.getRecordType());
                    Long remarkNum = taskRecordRemarkMapper.selectCount(new QueryWrapper<TaskRecordRemark>().eq(TaskRecordRemark.RECORD_ID, record.getId()));
                    taskItemResp.setRemarkedNum(remarkNum == null ? 0 : remarkNum.intValue());
                }
            }
        }
        return RespUtils.success(taskItemRespList);
    }

    private TaskItemResp toTaskItemResp(Task task) {
        TaskItemResp taskItemResp = new TaskItemResp();
        taskItemResp.setId(task.getId());
        taskItemResp.setTaskName(task.getTaskName());
        taskItemResp.setStartDate(task.getStartDate());
        taskItemResp.setEndDate(task.getEndDate());
        taskItemResp.setTaskContent(task.getTaskContent());
        LocalDate now = LocalDate.now();
        if (task.getStartDate().isAfter(now)) {
            taskItemResp.setTaskStatus((byte) 1);
        } else if (task.getEndDate().isBefore(now)) {
            taskItemResp.setTaskStatus((byte) 4);
        } else {
            taskItemResp.setTaskStatus((byte) 2);
        }
        return taskItemResp;
}
}
