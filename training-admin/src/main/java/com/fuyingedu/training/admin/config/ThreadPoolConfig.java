package com.fuyingedu.training.admin.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.StringUtils;

import java.util.concurrent.SynchronousQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@Configuration
public class ThreadPoolConfig {

	@Bean
	public ThreadPoolExecutor threadPoolExecutor(){
		int coreSize = 0, maxSize = 10000, keepAliveTime = 60;
		String threadPoolNamePrefix = "training-";
		return new ThreadPoolExecutor(coreSize, maxSize,
				keepAliveTime, TimeUnit.SECONDS,
				new SynchronousQueue<>(),
				Thread.ofVirtual().name(threadPoolNamePrefix, 0).factory(),
				new ThreadPoolExecutor.CallerRunsPolicy());
	}
}
