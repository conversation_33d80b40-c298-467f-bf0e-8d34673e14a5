package com.fuyingedu.training.admin.controller;

import com.fuyingedu.training.common.annotation.Login;
import com.fuyingedu.training.common.model.CommResp;
import com.fuyingedu.training.front.model.file.PolicyResp;
import com.fuyingedu.training.front.service.OssService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 文件上传管理
 */
@RestController
@RequestMapping("admin/file")
@Slf4j
public class FileController {

    @Autowired
    private OssService ossService;

    /**
     * 获取oss上传策略
     */
    @GetMapping("policy")
    public CommResp<PolicyResp> getOssPolicy(@Login Long userId, @RequestParam("type") String type) {
        return ossService.getOssPolicy(userId, type);
    }
}
