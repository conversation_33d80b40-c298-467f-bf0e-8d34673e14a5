package com.fuyingedu.training.admin.manager.camp;

import com.fuyingedu.training.common.enums.TaskLevel;
import com.fuyingedu.training.common.enums.TaskType;
import com.fuyingedu.training.entity.Live;
import com.fuyingedu.training.entity.Task;
import com.fuyingedu.training.entity.TaskTemplate;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

public interface CampRuleManager {

    void initRule(Long userId, Long scheduleId, LocalDate startDate, LocalTime liveTime);

    default Live buildLive(Long scheduleId, String liveName, Integer liveDuration, LocalDateTime startTime) {
        Live live = new Live();
        live.setScheduleId(scheduleId);
        live.setLiveName(liveName);
        live.setStartTime(startTime);
        live.setLiveType((byte) 2);
        live.setLivePort((byte) 1);
        live.setLiveDuration(liveDuration);
        live.setRepeatStartTime(startTime.plusMinutes(180));
        live.setRepeatEndTime(startTime.plusDays(2));
        return live;
    }

    default Task buildTask(Long userId, Long scheduleId, String taskName, TaskTemplate template,
                           LocalDate startDate, LocalDate endDate) {
        Task task = new Task();
        task.setScheduleId(scheduleId);
        task.setTaskLevel(TaskLevel.SCHEDULE.getCode());
        task.setTaskType(TaskType.HOMEWORK.getCode());
        task.setCreatedUserId(userId);
        task.setStartDate(startDate);
        task.setEndDate(endDate);
        task.setStartTime(template.getStartTime());
        task.setEndTime(template.getEndTime());
        task.setTaskName(taskName);
        task.setTaskType(template.getTaskType());
        task.setTaskContent(template.getTaskContent());
        task.setTaskReward(template.getTaskReward());
        task.setFxyReward(template.getFxyReward());
        task.setWordReward(template.getWordReward());
        task.setCaseUrls(template.getCaseUrls());
        task.setUploadItems(template.getUploadItems());
        return task;
    }
}
