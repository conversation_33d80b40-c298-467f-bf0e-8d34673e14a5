package com.fuyingedu.training.admin.controller;

import com.fuyingedu.training.common.annotation.Login;
import com.fuyingedu.training.admin.model.order.remark.ItemResp;
import com.fuyingedu.training.admin.model.order.remark.SaveReq;
import com.fuyingedu.training.admin.service.OrderRemarkService;
import com.fuyingedu.training.common.model.CommResp;
import com.fuyingedu.training.common.util.RespUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 学员课前联系记录管理
 */
@RestController
@RequestMapping("admin/order/remark")
public class OrderRemarkController {

    @Autowired
    private OrderRemarkService orderRemarkService;

    /**
     * 联系记录列表
     */
    @GetMapping("list")
    public CommResp<List<ItemResp>> list(@RequestParam("orderId") Long orderId) {
        return orderRemarkService.list(orderId);
    }

    /**
     * 保持联系记录
     * @param userId 前端不需要传
     */
    @PostMapping("save")
    public CommResp<?> save(@Login Long userId, @RequestBody SaveReq saveReq) {
        orderRemarkService.save(userId, saveReq);
        return RespUtils.success();
    }
}
