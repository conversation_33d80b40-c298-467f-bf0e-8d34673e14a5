package com.fuyingedu.training.admin.model.order;

import com.fuyingedu.training.common.util.StrUtils;
import com.fuyingedu.training.dto.order.ContactItemRet;
import com.fuyingedu.training.dto.order.OrderItemRet;
import com.fuyingedu.training.dto.order.RecordItemRet;
import com.fuyingedu.training.dto.order.RecordListParam;
import com.fuyingedu.training.entity.Order;
import org.springframework.util.StringUtils;

public class OrderConvertor {

    public static SearchItemResp toSearchItemResp(Order order) {
        SearchItemResp searchItemResp = new SearchItemResp();
        searchItemResp.setId(order.getId());
        searchItemResp.setOrderNo(order.getOrderNo());
        searchItemResp.setStudentFlag(order.getStudentFlag());
        searchItemResp.setStudentType(order.getStudentType());
        searchItemResp.setOrderStatus(order.getOrderStatus());
        searchItemResp.setGradeId(order.getTeacherId());
        searchItemResp.setClazzId(order.getClazzId());
        searchItemResp.setGroupId(order.getGroupId());
        return searchItemResp;
    }

    public static RecordListParam toRecordListParam(RecordListReq listReq) {
        RecordListParam recordListParam = new RecordListParam();
        recordListParam.setScheduleId(listReq.getScheduleId());
        recordListParam.setUserInfo(listReq.getUserInfo());
        recordListParam.setStudentInfo(listReq.getStudentInfo());
        recordListParam.setClazzId(listReq.getClazzId());
        recordListParam.setGroupId(listReq.getGroupId());
        recordListParam.setStudentType(listReq.getStudentType());
        recordListParam.setSortField(listReq.getSortField());
        recordListParam.setSortOrder(listReq.getSortOrder());
        recordListParam.setPageNum(listReq.getPageNum());
        recordListParam.setPageSize(listReq.getPageSize());
        return recordListParam;
    }

    public static RecordItemResp toRecordItemResp(RecordItemRet ret) {
        RecordItemResp recordItemResp = new RecordItemResp();
        recordItemResp.setId(ret.getId());
        recordItemResp.setGradeId(ret.getTeacherId());
        recordItemResp.setClazzId(ret.getClazzId());
        recordItemResp.setGroupId(ret.getGroupId());
        recordItemResp.setRealName(StringUtils.hasLength(ret.getOrderRemark()) ? ret.getOrderRemark() : ret.getRealName());
        recordItemResp.setPhoneNum(ret.getPhoneNum());
        recordItemResp.setStudentName(ret.getStudentName());
        recordItemResp.setUserIcon(ret.getUserIcon());
        recordItemResp.setCartNo(StrUtils.codeCartNum(ret.getCartNo()));
        recordItemResp.setStudentFlag(ret.getStudentFlag());
        recordItemResp.setStudentType(ret.getStudentType());
        recordItemResp.setEnrollmentNum(ret.getEnrollmentNum());
        recordItemResp.setPunchNum(ret.getPunchNum());
        recordItemResp.setHomeworkNum(ret.getHomeworkNum());
        recordItemResp.setRemarkNum(ret.getRemarkNum());
        recordItemResp.setMedalNum(ret.getMedalNum());
        recordItemResp.setTaskReward(ret.getTaskReward());
        recordItemResp.setRemarkedNum(ret.getRemarkedNum());
        return recordItemResp;
    }

    public static ContactItemResp toContactItemResp(ContactItemRet ret) {
        ContactItemResp contactItemResp = new ContactItemResp();
        contactItemResp.setId(ret.getId());
        contactItemResp.setRealName(ret.getRealName());
        contactItemResp.setPhoneNum(ret.getPhoneNum());
        contactItemResp.setStudentName(ret.getStudentName());
        contactItemResp.setUserIcon(ret.getUserIcon());
        contactItemResp.setCartNo(ret.getCartNo());
        contactItemResp.setOrderStatus(ret.getOrderStatus());
        contactItemResp.setTeacherWxStatus(ret.getTeacherWxStatus());
        contactItemResp.setAssistantWxStatus(ret.getAssistantWxStatus());
        contactItemResp.setGroupWxStatus(ret.getGroupWxStatus());
        contactItemResp.setConfirmStatus(ret.getConfirmStatus());
        contactItemResp.setOrderRemark(ret.getOrderRemark());
        return contactItemResp;
    }

    public static ItemResp toItemResp(OrderItemRet item) {
        ItemResp itemResp = new ItemResp();
        itemResp.setId(item.getId());
        itemResp.setOrderNo(item.getOrderNo());
        itemResp.setRealOrderNo(item.getRealOrderNo());
        itemResp.setOrderStatus(item.getOrderStatus());
        itemResp.setRealName(StringUtils.hasLength(item.getOrderRemark()) ? item.getOrderRemark() : item.getRealName());
        itemResp.setPhoneNum(item.getPhoneNum());
        itemResp.setScheduleId(item.getScheduleId());
        itemResp.setScheduleName(item.getScheduleName());
        itemResp.setStudentName(item.getStudentName());
        return itemResp;
    }

    public static ExportItemResp toExportItemResp(OrderItemRet item) {
        ExportItemResp exportResp = new ExportItemResp();
        exportResp.setOrderNo(item.getOrderNo());
        exportResp.setRealOrderNo(item.getRealOrderNo());
        exportResp.setId(item.getId()); // 订单号字段
        exportResp.setRealName(StringUtils.hasLength(item.getOrderRemark()) ? item.getOrderRemark() : item.getRealName());
        exportResp.setPhoneNum(item.getPhoneNum() != null ? item.getPhoneNum().toString() : "");
        exportResp.setOrderStatus(item.getOrderStatus());
        exportResp.setSignTime(item.getSignTime());
        return exportResp;
    }
}
