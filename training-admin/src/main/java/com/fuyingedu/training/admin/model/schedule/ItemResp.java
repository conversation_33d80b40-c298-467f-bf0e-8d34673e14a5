package com.fuyingedu.training.admin.model.schedule;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;
import java.time.LocalTime;

@Getter
@Setter
@ToString
public class ItemResp {

    private Long id;

    /**
     * 排期名称
     */
    private String scheduleName;

    /**
     * 外部的排期ID
     */
    private Long scheduleId;

    private String relationName;

    /**
     * 外部的班级ID
     */
    private Long clazzId;

    /**
     * 班级名称
     */
    private String clazzName;

    /**
     * 排期开始时间
     */
    private LocalDateTime startTime;

    /**
     * 排期结束时间
     */
    private LocalDateTime endTime;

    /**
     * 直播计划数量
     */
    private Integer liveNum;

    /**
     * 排期任务数量
     */
    private Integer taskNum;

    /**
     * 大班数量
     */
    private Integer gradeNum;

    /**
     * 小班数量
     */
    private Integer classNum;

    /**
     * 词书ID
     */
    private Long wordId;

    /**
     * PK数量
     */
    private Integer pkNum;

    /**
     * 直播方式 1-公司直播 2-导师直播
     */
    private Byte liveType;

    /**
     * 直播端口 1-腾讯会议 2-保利威 3-百家云
     */
    private Byte livePort;

    /**
     * 直播房间号
     */
    private Long liveRoom;

    /**
     * 直播开始时间
     */
    private LocalTime liveTime;
}
