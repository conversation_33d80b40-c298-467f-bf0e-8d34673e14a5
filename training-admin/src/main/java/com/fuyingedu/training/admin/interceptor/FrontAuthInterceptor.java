package com.fuyingedu.training.admin.interceptor;

import com.fasterxml.jackson.databind.JsonNode;
import com.fuyingedu.training.common.util.JsonUtils;
import com.fuyingedu.training.common.util.JwtUtils;
import com.fuyingedu.training.common.util.RedisKey;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class FrontAuthInterceptor extends CommonAuthInterceptor {

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    @Override
    public boolean checkAuth(HttpServletRequest request, @NotNull HttpServletResponse response, @NotNull Object handler) throws IOException {
        String token = request.getHeader(HttpHeaders.AUTHORIZATION);
        if (!StringUtils.hasLength(token)) {
            respNotLogin(response);
            return false;
        }
        request.setAttribute("token", token);
        String userInfoStr = redisTemplate.opsForValue().get(String.format(RedisKey.FRONT_LOGIN, token));
        if (userInfoStr == null) {
            respNotLogin(response);
            return false;
        }
        JsonNode userInfo = JsonUtils.parseJsonToJsonNode(userInfoStr);
        Long userId = userInfo.get("userId").asLong();
        setUserInfo(request, userId);
        long loginTime = userInfo.get("loginTime").asLong();
        long hourMillis = 15 * 24 * 60 * 60 * 1000;
        if (System.currentTimeMillis() - loginTime > hourMillis) {
            Map<String, Object> params = Map.of("userId", userId, "loginTime", System.currentTimeMillis());
            redisTemplate.opsForValue().set(String.format(RedisKey.FRONT_LOGIN, token), JsonUtils.formatObjToJson(params), 30, TimeUnit.DAYS);
        }
        return true;
    }

}
