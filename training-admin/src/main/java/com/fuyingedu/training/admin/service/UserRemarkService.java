package com.fuyingedu.training.admin.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fuyingedu.training.admin.model.order.AddRemarkReq;
import com.fuyingedu.training.admin.model.order.RemarkResp;
import com.fuyingedu.training.common.enums.RespMetaEnum;
import com.fuyingedu.training.common.enums.Status;
import com.fuyingedu.training.common.exception.WebBaseException;
import com.fuyingedu.training.common.model.CommResp;
import com.fuyingedu.training.common.util.JsonUtils;
import com.fuyingedu.training.common.util.RespUtils;
import com.fuyingedu.training.entity.*;
import com.fuyingedu.training.front.manager.OperationManager;
import com.fuyingedu.training.front.model.media.MediaConvertor;
import com.fuyingedu.training.mapper.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class UserRemarkService {

    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private UserMapper userMapper;
    @Autowired
    private ScheduleMapper scheduleMapper;
    @Autowired
    private CampMapper campMapper;
    @Autowired
    private UserRemarkMapper userRemarkMapper;
    @Autowired
    private UserRemarkCommonMapper userRemarkCommonMapper;
    @Autowired
    private OperationManager operationManager;

    @Transactional(rollbackFor = Exception.class)
    public void addRemark(Long userId, AddRemarkReq addRemarkReq) {
        Order order = orderMapper.selectOne(new QueryWrapper<Order>().select(
                Order.ID, Order.USER_ID, Order.SCHEDULE_ID
        ).eq(Order.ID, addRemarkReq.getOrderId()));
        if (order == null) {
            throw new WebBaseException(RespMetaEnum.PARAM_ERROR);
        }
        UserRemark userRemark = toUserRemark(addRemarkReq, order, userId);
        userRemarkMapper.insert(userRemark);
        operationManager.saveLog(userId, order.getId(), 7, userRemark.getId());
    }

    private UserRemark toUserRemark(AddRemarkReq addRemarkReq, Order order, Long remarkUserId) {
        UserRemark userRemark = new UserRemark();
        userRemark.setScheduleId(order.getScheduleId());
        userRemark.setUserId(order.getUserId());
        userRemark.setRemarkUserId(remarkUserId);
        userRemark.setRemarkContent(addRemarkReq.getRemark());
        if (!CollectionUtils.isEmpty(addRemarkReq.getImageUrlList())) {
            userRemark.setImageUrls(MediaConvertor.getUrls(addRemarkReq.getImageUrlList()));
        } else {
            userRemark.setImageUrls(JsonUtils.formatObjToJson(Collections.emptyList()));
        }
        if (!CollectionUtils.isEmpty(addRemarkReq.getVideoUrlList())) {
            userRemark.setVideoUrls(MediaConvertor.getUrls(addRemarkReq.getVideoUrlList()));
        } else {
            userRemark.setVideoUrls(JsonUtils.formatObjToJson(Collections.emptyList()));
        }
        return userRemark;
    }

    public CommResp<List<RemarkResp>> remarkList(Long orderId, Long scheduleId) {
        Order order = orderMapper.selectOne(new QueryWrapper<Order>().select(Order.USER_ID).eq(Order.ID, orderId));
        if (order == null) {
            throw new WebBaseException(RespMetaEnum.PARAM_ERROR);
        }
        List<UserRemark> remarkList = userRemarkMapper.selectList(new QueryWrapper<UserRemark>().select(
                UserRemark.ID, UserRemark.REMARK_CONTENT, UserRemark.IMAGE_URLS, UserRemark.VIDEO_URLS,
                UserRemark.CREATED_TIME, UserRemark.REMARK_USER_ID, UserRemark.USER_ID, UserRemark.SCHEDULE_ID
        ).eq(UserRemark.USER_ID, order.getUserId()).eq(UserRemark.REMARK_STATUS, Status.NORMAL.getCode())
                .eq(scheduleId != null, UserRemark.SCHEDULE_ID, scheduleId));
        if (remarkList.isEmpty()) {
            return RespUtils.success(Collections.emptyList());
        }
        List<Long> userIds = remarkList.stream().map(UserRemark::getRemarkUserId).distinct().toList();
        List<Long> scheduleIds = remarkList.stream().map(UserRemark::getScheduleId).distinct().toList();
        Map<Long, User> userMap = userMapper.selectList(new QueryWrapper<User>().select(User.ID, User.REAL_NAME).in(User.ID, userIds))
                .stream().collect(Collectors.toMap(User::getId, user -> user));
        Map<Long, Schedule> scheduleMap = scheduleMapper.selectList(new QueryWrapper<Schedule>().select(
                Schedule.ID, Schedule.SCHEDULE_NAME, Schedule.CAMP_ID).in(Schedule.ID, scheduleIds))
                .stream().collect(Collectors.toMap(Schedule::getId, schedule -> schedule));
        List<Long> campIds = scheduleMap.values().stream().map(Schedule::getCampId).distinct().toList();
        Map<Long, String> campMap = campMapper.selectList(new QueryWrapper<Camp>().select(Camp.ID, Camp.CAMP_NAME).in(Camp.ID, campIds))
                .stream().collect(Collectors.toMap(Camp::getId, Camp::getCampName));
        return RespUtils.success(remarkList.stream().map(userRemark -> {
            User remarkUser = userMap.get(userRemark.getRemarkUserId());
            Schedule schedule = scheduleMap.get(userRemark.getScheduleId());
            return toRemarkResp(userRemark, remarkUser.getRealName(), schedule.getScheduleName(), campMap.get(schedule.getCampId()));
        }).toList());
    }

    public void addRemark(Long remarkUserId, Long scheduleId, Long userId, String content) {
        if (StringUtils.hasLength(content)) {
            UserRemark userRemark = new UserRemark();
            userRemark.setScheduleId(scheduleId);
            userRemark.setUserId(userId);
            userRemark.setRemarkUserId(remarkUserId);
            userRemark.setRemarkContent(content);
            userRemarkMapper.insert(userRemark);
        }
    }

    private RemarkResp toRemarkResp(UserRemark userRemark, String realName, String scheduleName, String campName) {
        RemarkResp remarkResp = new RemarkResp();
        remarkResp.setId(userRemark.getId());
        remarkResp.setCreatedTime(userRemark.getCreatedTime());
        remarkResp.setRemarkUserName(realName);
        remarkResp.setCampName(scheduleName);
        remarkResp.setScheduleName(campName);
        remarkResp.setRemarkContent(userRemark.getRemarkContent());
        if (userRemark.getImageUrls() != null) {
            remarkResp.setImageUrlList(MediaConvertor.getUrlList(userRemark.getImageUrls()));
        }
        if (userRemark.getVideoUrls() != null) {
            remarkResp.setVideoUrlList(MediaConvertor.getUrlList(userRemark.getVideoUrls()));
        }
        return remarkResp;
    }

}
