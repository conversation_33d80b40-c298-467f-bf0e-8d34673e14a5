package com.fuyingedu.training.admin.model.pk;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.Set;

@Getter
@Setter
public class SaveReq {
    private Long id;
    /**
     * 排期ID
     */
    @NotNull
    private Long scheduleId;

    @NotNull
    private LocalDateTime startTime;

    @NotNull
    private LocalDateTime stopTime;
}
