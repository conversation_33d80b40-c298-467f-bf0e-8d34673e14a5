package com.fuyingedu.training.admin.config;

import com.fuyingedu.training.admin.interceptor.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.List;

@Configuration
public class WebConfig implements WebMvcConfigurer {

    @Autowired
    private LoginResolver loginResolver;
    @Autowired
    private FxyAuthInterceptor fxyAuthInterceptor;
    @Autowired
    private AdminAuthInterceptor adminAuthInterceptor;
    @Autowired
    private FrontAuthInterceptor frontAuthInterceptor;
    @Autowired
    private JobAuthInterceptor jobAuthInterceptor;
    @Autowired
    private InnerAuthInterceptor innerAuthInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(fxyAuthInterceptor).addPathPatterns("/fxy/**").excludePathPatterns("/fxy/user/info");
        registry.addInterceptor(adminAuthInterceptor).addPathPatterns("/admin/**").excludePathPatterns(
                "/admin/login", "/admin/loginForTest", "/admin/bjy/callback");
        registry.addInterceptor(frontAuthInterceptor).addPathPatterns("/front/**").excludePathPatterns("/front/login",
                "/front/login/send/code", "/front/login/session/key");
        registry.addInterceptor(jobAuthInterceptor).addPathPatterns("/job/**");
        registry.addInterceptor(innerAuthInterceptor).addPathPatterns("/inner/**");
    }

    @Override
    public void addArgumentResolvers(List<HandlerMethodArgumentResolver> argumentResolvers) {
        argumentResolvers.add(loginResolver);
    }

    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**").allowedOrigins("*").allowedHeaders("*")
                .allowedMethods("*").allowCredentials(false);
    }
}
