package com.fuyingedu.training.admin.controller;

import com.fuyingedu.training.admin.model.live.ItemResp;
import com.fuyingedu.training.admin.model.live.SaveReq;
import com.fuyingedu.training.admin.model.live.UpdateReq;
import com.fuyingedu.training.admin.service.LiveService;
import com.fuyingedu.training.common.annotation.Login;
import com.fuyingedu.training.common.model.CommResp;
import com.fuyingedu.training.common.util.RespUtils;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 直播管理
 */
@RestController
@RequestMapping("admin/live")
public class LiveController {

    @Autowired
    private LiveService liveService;

    /**
     * 直播列表
     */
    @GetMapping("list")
    public CommResp<List<ItemResp>> list(@RequestParam("scheduleId") Long scheduleId) {
        return liveService.list(null, scheduleId);
    }

    /**
     * 教师直播列表
     */
    @GetMapping("teacher/list")
    public CommResp<List<ItemResp>> teacherList(@Login Long userId, @RequestParam("scheduleId") Long scheduleId) {
        return liveService.list(userId, scheduleId);
    }

    /**
     * 直播保存
     */
    @PostMapping("save")
    public CommResp<?> save(@RequestBody @Valid SaveReq saveReq) {
        liveService.save(saveReq);
        return RespUtils.success();
    }

    /**
     * 更新直播间信息
     */
    @PostMapping("update")
    public CommResp<?> update(@RequestBody @Valid UpdateReq updateReq) {
        liveService.update(updateReq);
        return RespUtils.success();
    }
}
