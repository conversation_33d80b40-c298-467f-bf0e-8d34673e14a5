package com.fuyingedu.training.admin.controller;

import com.fuyingedu.training.common.annotation.Login;
import com.fuyingedu.training.admin.model.grade.*;
import com.fuyingedu.training.admin.service.GradeService;
import com.fuyingedu.training.common.annotation.PreAuthorize;
import com.fuyingedu.training.common.model.CommResp;
import com.fuyingedu.training.common.util.RespUtils;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 大班和辅导老师管理
 */
@RestController
@RequestMapping("admin/grade")
public class GradeController {

    @Autowired
    private GradeService gradeService;

    /**
     * 查看排期下的辅导老师/助教
     */
    @GetMapping("teacher/list")
    public CommResp<List<TeacherResp>> teacherList(TeacherReq teacherReq) {
        return gradeService.teacherList(teacherReq);
    }

    /**
     * 新增助教/辅导老师
     */
    @PreAuthorize({"管理员", "普通管理员"})
    @PostMapping("save")
    public CommResp<?> addOrEdit(@RequestBody @Valid SaveReq saveReq) {
        gradeService.addOrEdit(saveReq);
        return RespUtils.success();
    }


    /**
     * 修改最多服务人数和权重
     */
    @PreAuthorize({"管理员", "普通管理员"})
    @PostMapping("update")
    public CommResp<?> update(@RequestBody @Valid UpdateReq updateReq) {
        gradeService.update(updateReq);
        return RespUtils.success();
    }

    /**
     * 修改老师类型
     */
    @PreAuthorize({"管理员", "普通管理员"})
    @PostMapping("type/update")
    public CommResp<?> updateType(@RequestBody @Valid UpdateTypeReq updateTypeReq) {
        gradeService.updateType(updateTypeReq);
        return RespUtils.success();
    }

    /**
     * 删除导师/助教
     */
    @PreAuthorize({"管理员", "普通管理员"})
    @PostMapping("delete")
    public CommResp<?> delete(@RequestBody @Valid DeleteReq deleteReq) {
        gradeService.delete(deleteReq);
        return RespUtils.success();
    }

    /**
     * 查询大班下的分班情况
     * @param userId 前端不需要传
     */
    @GetMapping("clazz/info")
    public CommResp<ClazzInfoResp> clazzInfo(@Login Long userId, @RequestParam("scheduleId") Long scheduleId) {
        return gradeService.clazzInfo(userId, scheduleId);
    }
}
