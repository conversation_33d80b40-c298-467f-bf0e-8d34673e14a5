package com.fuyingedu.training.admin.manager.camp;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.fuyingedu.training.entity.*;
import com.fuyingedu.training.mapper.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 顾问式父母的说话之道30天实战训练营
 */
@Component("camp_10")
public class Camp10RuleManager implements CampRuleManager {

    @Autowired
    private LiveMapper liveMapper;
    @Autowired
    private TaskMapper taskMapper;
    @Autowired
    private TaskTemplateMapper taskTemplateMapper;
    @Autowired
    private TaskRelationMapper taskRelationMapper;
    @Autowired
    private ScheduleMapper scheduleMapper;

    @Override
    public void initRule(Long userId, Long scheduleId, LocalDate startDate, LocalTime liveTime) {
        LocalDateTime startTime = LocalDateTime.of(startDate, liveTime);
        List<Live> liveList = new ArrayList<>();
        liveList.add(buildLive(scheduleId, "第一课：成为沟通高手的本质：知人心", 115, startTime));
        liveList.add(buildLive(scheduleId, "第二课：如何赢得孩子的心，让他心甘情愿合作。", 115, startTime.plusDays(2)));
        liveList.add(buildLive(scheduleId, "第三课：用说话走进孩子内心的秘诀。", 115, startTime.plusDays(4)));
        liveList.add(buildLive(scheduleId, "第四课：倾听不只是听见，要听懂话外音", 115, startTime.plusDays(7)));
        liveList.add(buildLive(scheduleId, "第五课：1、问到点子上，才能答到关键处。2、一句话点醒孩子的本领是这么来的", 115, startTime.plusDays(9)));
        liveList.add(buildLive(scheduleId, "第六课：动之以情，晓之以理。", 115, startTime.plusDays(11)));
        liveList.add(buildLive(scheduleId, "第七课：诱之以利，示之以威。", 115, startTime.plusDays(14)));
        liveList.add(buildLive(scheduleId, "第八课：1、沟通的最高境界---宠辱不惊。2、说话要懂得示弱，才是高手。", 115, startTime.plusDays(16)));
        liveList.add(buildLive(scheduleId, "第九课：1、眼光长远，学会抓大放小，你就厉害了。2、深处低谷，如何做孩子的坚强后盾？", 115, startTime.plusDays(18)));
        liveMapper.insert(liveList);
        List<Task> taskList = new ArrayList<>();
        taskList.add(buildTask(userId, scheduleId, "第一课课前思考", template(1000096L), startDate, startDate));
        taskList.add(buildTask(userId, scheduleId, "第一课课后作业", template(1000097L), startDate, startDate.plusDays(2)));
        taskList.add(buildTask(userId, scheduleId, "第二课课前思考", template(1000098L), startDate.plusDays(2), startDate.plusDays(2)));
        taskList.add(buildTask(userId, scheduleId, "第二课课后作业", template(1000099L), startDate.plusDays(2), startDate.plusDays(4)));
        taskList.add(buildTask(userId, scheduleId, "第三课课前思考", template(1000100L), startDate.plusDays(4), startDate.plusDays(4)));
        taskList.add(buildTask(userId, scheduleId, "第三课课后作业", template(1000101L), startDate.plusDays(4), startDate.plusDays(7)));
        taskList.add(buildTask(userId, scheduleId, "第四课课前思考", template(1000102L), startDate.plusDays(7), startDate.plusDays(7)));
        taskList.add(buildTask(userId, scheduleId, "第四课课后作业", template(1000103L), startDate.plusDays(7), startDate.plusDays(9)));
        taskList.add(buildTask(userId, scheduleId, "第五课课前思考", template(1000104L), startDate.plusDays(9), startDate.plusDays(9)));
        taskList.add(buildTask(userId, scheduleId, "第五课课后作业", template(1000105L), startDate.plusDays(9), startDate.plusDays(11)));
        taskList.add(buildTask(userId, scheduleId, "第六课课前思考", template(1000106L), startDate.plusDays(11), startDate.plusDays(11)));
        taskList.add(buildTask(userId, scheduleId, "第六课课后作业", template(1000107L), startDate.plusDays(11), startDate.plusDays(14)));
        taskList.add(buildTask(userId, scheduleId, "第七课课前思考", template(1000108L), startDate.plusDays(14), startDate.plusDays(14)));
        taskList.add(buildTask(userId, scheduleId, "第七课课后作业", template(1000109L), startDate.plusDays(14), startDate.plusDays(16)));
        taskList.add(buildTask(userId, scheduleId, "第八课课前思考", template(1000110L), startDate.plusDays(16), startDate.plusDays(16)));
        taskList.add(buildTask(userId, scheduleId, "第八课课后作业", template(1000111L), startDate.plusDays(16), startDate.plusDays(18)));
        taskList.add(buildTask(userId, scheduleId, "第九课课前思考", template(1000112L), startDate.plusDays(18), startDate.plusDays(18)));
        taskList.add(buildTask(userId, scheduleId, "第九课课后作业", template(1000113L), startDate.plusDays(18), startDate.plusDays(20)));
        taskMapper.insert(taskList);
        List<TaskRelation> relationList = new ArrayList<>();
        for (Task task : taskList) {
            TaskRelation relation = new TaskRelation();
            relation.setTaskId(task.getId());
            relation.setScheduleId(scheduleId);
            relation.setTeacherId(-1L);
            relation.setAssistantId(-1L);
            relation.setClazzId(-1L);
            relationList.add(relation);
        }
        taskRelationMapper.insert(relationList);
        scheduleMapper.update(new UpdateWrapper<Schedule>().setSql(String.format("%s = %s + %d", Schedule.TASK_NUM, Schedule.TASK_NUM, taskList.size()))
                .eq(Schedule.ID, scheduleId));
    }

    private TaskTemplate template(Long templateId) {
        return taskTemplateMapper.selectOne(new QueryWrapper<TaskTemplate>().eq(TaskTemplate.ID, templateId));
    }
}
