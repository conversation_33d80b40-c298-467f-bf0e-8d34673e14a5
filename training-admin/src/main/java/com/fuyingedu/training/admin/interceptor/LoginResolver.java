package com.fuyingedu.training.admin.interceptor;

import com.fuyingedu.training.common.annotation.Login;
import com.fuyingedu.training.common.annotation.UserInfo;
import com.fuyingedu.training.common.enums.RespMetaEnum;
import com.fuyingedu.training.common.exception.WebBaseException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.MethodParameter;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.method.support.ModelAndViewContainer;

import java.util.Set;

@Slf4j
@Component
public class LoginResolver implements HandlerMethodArgumentResolver {

    @Value("${spring.profiles.active}")
    private String profiles;

    @Override
    public boolean supportsParameter(MethodParameter parameter) {
        return parameter.hasParameterAnnotation(Login.class);
    }

    @Override
    public Object resolveArgument(MethodParameter parameter, ModelAndViewContainer container,
                                  NativeWebRequest request, WebDataBinderFactory factory) {
        Object userInfo = request.getAttribute("userInfo", NativeWebRequest.SCOPE_REQUEST);
        if (parameter.getParameterType().isAssignableFrom(String.class)) {
            // 扶小鹰
            return String.valueOf(userInfo);
        } else if (parameter.getParameterType().isAssignableFrom(Long.class)) {
            return Long.valueOf(String.valueOf(userInfo));
        } else if (parameter.getParameterType().isAssignableFrom(UserInfo.class)) {
            UserInfo user = new UserInfo();
            user.setUserId(Long.valueOf(String.valueOf(userInfo)));
            user.setRoleIds((Set<Long>) request.getAttribute("roleIds", NativeWebRequest.SCOPE_REQUEST));
            return user;
        }
        throw new WebBaseException(RespMetaEnum.NOT_LOGIN);
    }

}
