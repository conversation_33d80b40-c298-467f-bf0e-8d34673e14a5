package com.fuyingedu.training.admin.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.fuyingedu.training.admin.model.word.LiveUpdateReq;
import com.fuyingedu.training.admin.model.word.SaveReq;
import com.fuyingedu.training.common.enums.CampType;
import com.fuyingedu.training.common.enums.RespMetaEnum;
import com.fuyingedu.training.common.enums.TaskLevel;
import com.fuyingedu.training.common.enums.TaskType;
import com.fuyingedu.training.common.exception.WebBaseException;
import com.fuyingedu.training.common.util.JsonUtils;
import com.fuyingedu.training.entity.*;
import com.fuyingedu.training.front.model.word.TaskItem;
import com.fuyingedu.training.front.service.FrontWordService;
import com.fuyingedu.training.mapper.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class WordService {

    @Autowired
    private CampMapper campMapper;
    @Autowired
    private ScheduleMapper scheduleMapper;
    @Autowired
    private TaskMapper taskMapper;
    @Autowired
    private WordScheduleMapper wordScheduleMapper;
    @Autowired
    private LiveMapper liveMapper;
    @Autowired
    private WordPkConfigMapper wordPkConfigMapper;
    @Autowired
    private FrontWordService frontWordService;
    @Autowired
    private WordDelayMapper wordDelayMapper;
    @Autowired
    private TaskRelationMapper taskRelationMapper;
    @Autowired
    private ScheduleRoomMapper scheduleRoomMapper;

    @Transactional(rollbackFor = Exception.class)
    public Schedule save(Long userId, SaveReq saveReq) {
        int days = saveReq.getDays();
        if (days % 7 != 0) {
            throw new WebBaseException(4000, "单词书配置错误，天数必须是7的倍数");
        }
        Camp camp = campMapper.selectOne(new QueryWrapper<Camp>().select(Camp.ID, Camp.CAMP_TYPE).eq(Camp.ID, saveReq.getCampId()));
        if (camp == null || !CampType.WORD.getCode().equals(camp.getCampType())) {
            throw new WebBaseException(RespMetaEnum.PARAM_ERROR);
        }
        Schedule schedule = scheduleMapper.selectOne(new QueryWrapper<Schedule>().select(
                Schedule.ID, Schedule.START_TIME
        ).eq(Schedule.SCHEDULE_ID, saveReq.getScheduleId())
                .eq(saveReq.getClazzId() != null, Schedule.CLAZZ_ID, saveReq.getClazzId())
                        .isNull(saveReq.getClazzId() == null, Schedule.CLAZZ_ID)
        );
        Schedule saveSchedule = new Schedule();
        saveSchedule.setScheduleId(saveReq.getScheduleId());
        saveSchedule.setClazzId(saveReq.getClazzId());
        saveSchedule.setCampId(saveReq.getCampId());
        saveSchedule.setScheduleName(saveReq.getScheduleName());
        saveSchedule.setRelationName(saveReq.getRelationName());
        saveSchedule.setClazzName(saveReq.getClazzName());
        saveSchedule.setLiveType(saveReq.getLiveType());
        saveSchedule.setLivePort(saveReq.getLivePort());
        saveSchedule.setLiveRoom(saveReq.getLiveRoom());
        saveSchedule.setLiveTime(saveReq.getLiveTime());
        saveSchedule.setStartTime(saveReq.getStartTime());
        saveSchedule.setEndTime(saveReq.getStartTime().plusDays(days + 1));
        saveSchedule.setLiveNum(saveReq.getLiveDayList().size());
        saveSchedule.setTaskNum(1);
        if (schedule == null) {
            campMapper.update(new UpdateWrapper<Camp>()
                    .setSql(String.format("%s = %s + 1", Camp.SCHEDULE_NUM, Camp.SCHEDULE_NUM))
                    .eq(Camp.ID, saveReq.getCampId()));
            scheduleMapper.insert(saveSchedule);
        } else {
            if (LocalDateTime.now().isAfter(schedule.getStartTime())) {
                throw new WebBaseException(4000, "当前排期已开始，不能修改排期");
            }
            saveSchedule.setId(schedule.getId());
            scheduleMapper.updateById(saveSchedule);
            List<ScheduleRoom> scheduleRooms = scheduleRoomMapper.selectList(new QueryWrapper<ScheduleRoom>().select(ScheduleRoom.ID).eq(ScheduleRoom.SCHEDULE_ID, schedule.getId()));
            if (!CollectionUtils.isEmpty(scheduleRooms)) {
                scheduleRoomMapper.update(new UpdateWrapper<ScheduleRoom>().set(ScheduleRoom.SCHEDULE_ID, null)
                        .in(ScheduleRoom.ID, scheduleRooms.stream().map(ScheduleRoom::getId).toList()));
            }
            wordDelayMapper.delete(new QueryWrapper<WordDelay>().eq(WordDelay.SCHEDULE_ID, schedule.getId()));
        }
        Long scheduleId = saveSchedule.getId();
        // 排期和单词书关联
        WordSchedule scheduleWord = wordScheduleMapper.selectOne(new QueryWrapper<WordSchedule>().select(
                WordSchedule.SCHEDULE_ID
        ).eq(WordSchedule.SCHEDULE_ID, scheduleId));
        if (scheduleWord == null) {
            scheduleWord = new WordSchedule();
            scheduleWord.setScheduleId(scheduleId);
        }
        scheduleWord.setWordId(saveReq.getTrainingCampPlanId());
        scheduleWord.setWordDays(days);
        scheduleWord.setLiveDays(JsonUtils.formatObjToJson(saveReq.getLiveDayList()));
        wordScheduleMapper.insertOrUpdate(scheduleWord);
        // 任务生成
        Task task = taskMapper.selectOne(new QueryWrapper<Task>().select(Task.ID)
                .eq(Task.SCHEDULE_ID, scheduleId).eq(Task.TASK_TYPE, TaskType.WORD.getCode()).last("limit 1"));
        boolean isNew = task == null;
        if (task == null) {
            task = new Task();
            task.setScheduleId(scheduleId);
            task.setTaskLevel(TaskLevel.SCHEDULE.getCode());
            task.setTaskType(TaskType.WORD.getCode());
            task.setCreatedUserId(userId);
            task.setTaskName("学习计划");
        }
        task.setStartDate(saveReq.getStartTime().toLocalDate().plusDays(1));
        task.setEndDate(task.getStartDate().plusDays(days));
        task.setWordReward(45);
        taskMapper.insertOrUpdate(task);
        if (isNew) {
            taskRelationMapper.insert(toTaskRelation(task.getScheduleId(), task.getId()));
        }
        // 直播生成
        int liveNum = saveReq.getLiveDayList().size() + 1;
        List<Live> liveList = liveMapper.selectList(new QueryWrapper<Live>().select(
                Live.ID, Live.LIVE_NAME
            ).eq(Live.SCHEDULE_ID, scheduleId).orderByAsc(Live.START_TIME));
        if (liveList.isEmpty() || !"开营直播".equals(liveList.getFirst().getLiveName())) {
            Live live = new Live();
            live.setScheduleId(scheduleId);
            live.setStartTime(saveReq.getStartTime());
            live.setLiveName("开营直播");
            liveList.addFirst(live);
        } else {
            liveList.getFirst().setStartTime(saveReq.getStartTime());
        }
        List<WordPkConfig> pkConfigList = new ArrayList<>(liveNum);
        for (int i = 1; i < liveNum; i++) {
            Live live;
            if (liveList.size() > i) {
                live = liveList.get(i);
            } else {
                live = new Live();
                liveList.add(live);
            }
            live.setScheduleId(scheduleId);
            live.setStartTime(saveReq.getStartTime().plusDays(saveReq.getLiveDayList().get(i - 1)));
            String liveName = saveReq.getLiveNameList().get(i - 1);
            if ("null".equals(liveName)) {
                live.setLiveName("");
            } else {
                live.setLiveName(liveName);
            }
            WordPkConfig wordPkConfig = new WordPkConfig();
            wordPkConfig.setScheduleId(scheduleId);
            wordPkConfig.setStartTime(live.getStartTime().toLocalDate().atTime(14, 0, 0));
            wordPkConfig.setStopTime(live.getStartTime().toLocalDate().atTime(23, 0, 0));
            pkConfigList.add(wordPkConfig);
        }
        if (!liveList.isEmpty()) {
            liveMapper.insertOrUpdate(liveList.subList(0, liveNum));
        }
        if (liveList.size() > liveNum) {
            liveMapper.deleteByIds(liveList.subList(liveNum, liveList.size()).stream().map(Live::getId).toList());
        }
        List<Long> pkConfigIds = wordPkConfigMapper.selectList(new QueryWrapper<WordPkConfig>().select(
                WordPkConfig.ID
        ).eq(WordPkConfig.SCHEDULE_ID, scheduleId)).stream().map(WordPkConfig::getId).toList();
        if (!pkConfigIds.isEmpty()) {
            wordPkConfigMapper.deleteByIds(pkConfigIds);
        }
        if (!pkConfigList.isEmpty()) {
            wordPkConfigMapper.insert(pkConfigList);
        }
        if (saveReq.getLiveRoom() != null) {
            scheduleRoomMapper.update(new UpdateWrapper<ScheduleRoom>()
                    .set(ScheduleRoom.SCHEDULE_ID, saveSchedule.getId())
                    .eq(ScheduleRoom.ROOM_ID, saveReq.getLiveRoom())
            );
        }
        return saveSchedule;
    }

    private TaskRelation toTaskRelation(Long scheduleId, Long taskId) {
        TaskRelation relation = new TaskRelation();
        relation.setTaskId(taskId);
        relation.setScheduleId(scheduleId);
        relation.setTeacherId(-1L);
        relation.setAssistantId(-1L);
        relation.setClazzId(-1L);
        return relation;
    }

    @Transactional(rollbackFor = Exception.class)
    public void delay(Long scheduleId, int days, int delayDays) {
        Schedule schedule = scheduleMapper.selectOne(new QueryWrapper<Schedule>().select(
                Schedule.ID, Schedule.START_TIME, Schedule.END_TIME
        ).eq(Schedule.ID, scheduleId));
        List<TaskItem> itemList = frontWordService.taskList(schedule);
        TaskItem taskItem = itemList.get(days);
        WordDelay scheduleWordDelay = new WordDelay();
        scheduleWordDelay.setScheduleId(scheduleId);
        scheduleWordDelay.setRealDays(days);
        scheduleWordDelay.setDelayDays(delayDays);
        wordDelayMapper.insert(scheduleWordDelay);
        scheduleMapper.update(new UpdateWrapper<Schedule>()
                .set(Schedule.END_TIME, schedule.getEndTime().plusDays(delayDays))
                .eq(Schedule.ID, scheduleId));
        Task task = taskMapper.selectOne(new QueryWrapper<Task>().select(Task.ID, Task.END_DATE).eq(Task.SCHEDULE_ID, scheduleId)
                .eq(Task.TASK_TYPE, TaskType.WORD.getCode()).last("limit 1"));
        taskMapper.update(new UpdateWrapper<Task>()
               .set(Task.END_DATE, task.getEndDate().plusDays(delayDays))
               .eq(Task.ID, task.getId()));
        List<Live> updateLiveList = new ArrayList<>();
        List<Live> liveList = liveMapper.selectList(new QueryWrapper<Live>().select(
                Live.ID, Live.START_TIME, Live.REPEAT_START_TIME, Live.REPEAT_END_TIME
        ).eq(Live.SCHEDULE_ID, scheduleId).orderByAsc(Live.START_TIME));
        for (Live liveItem : liveList) {
            if (liveItem.getStartTime().isBefore(taskItem.getRealDate().atStartOfDay())) {
                continue;
            }
            liveItem.setStartTime(liveItem.getStartTime().plusDays(delayDays));
            if (liveItem.getRepeatStartTime() != null) {
                liveItem.setRepeatStartTime(liveItem.getRepeatStartTime().plusDays(delayDays));
            }
            if (liveItem.getRepeatEndTime() != null) {
                liveItem.setRepeatEndTime(liveItem.getRepeatEndTime().plusDays(delayDays));
            }
            updateLiveList.add(liveItem);
        }
        if (!updateLiveList.isEmpty()) {
            liveMapper.updateById(updateLiveList);
        }
    }

}
