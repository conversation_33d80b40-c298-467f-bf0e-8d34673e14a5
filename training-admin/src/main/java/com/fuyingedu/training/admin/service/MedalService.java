package com.fuyingedu.training.admin.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fuyingedu.training.admin.model.medal.*;
import com.fuyingedu.training.common.annotation.UserInfo;
import com.fuyingedu.training.common.enums.RespMetaEnum;
import com.fuyingedu.training.common.enums.Status;
import com.fuyingedu.training.common.exception.WebBaseException;
import com.fuyingedu.training.common.model.CommResp;
import com.fuyingedu.training.common.util.RespUtils;
import com.fuyingedu.training.entity.*;
import com.fuyingedu.training.front.manager.OperationManager;
import com.fuyingedu.training.mapper.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class MedalService {

    @Autowired
    private MedalMapper medalMapper;
    @Autowired
    private MedalRelationMapper medalRelationMapper;
    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private OrderMedalMapper orderMedalMapper;
    @Autowired
    private MedalRecordMapper medalRecordMapper;
    @Autowired
    private OrderStatisticMapper orderStatisticMapper;
    @Autowired
    private AvailableCampMapper availableCampMapper;
    @Autowired
    private ScheduleMapper scheduleMapper;
    @Autowired
    private UserRoleMapper userRoleMapper;
    @Autowired
    private OperationManager operationManager;

    public CommResp<List<ItemResp>> list(UserInfo userInfo, ListReq req) {
        Long userId = userInfo.getUserId();
        Set<Long> userRoles = userInfo.getRoleIds();
        List<Long> campIds = null;
        if (!userRoles.contains(2L)) {
            campIds = availableCampMapper.selectList(new QueryWrapper<AvailableCamp>().select(
                    AvailableCamp.CAMP_ID
            ).eq(AvailableCamp.AVAILABLE_ID, userId).eq(AvailableCamp.AVAILABLE_TYPE, 4).eq(AvailableCamp.CAMP_STATUS, 1))
                    .stream().map(AvailableCamp::getCampId).toList();
            if (campIds.isEmpty()) {
                return RespUtils.warning(RespMetaEnum.NO_AUTH);
            }
        }
        if (req.getCampId() != null) {
            if (campIds == null) {
                campIds = List.of(req.getCampId());
            } else if (!campIds.contains(req.getCampId())) {
                return RespUtils.success(req.getPageNum(), req.getPageSize(), 0L, Collections.emptyList());
            }
        }
        Set<Long> mediaIds = null;
        if (campIds != null) {
            mediaIds = availableCampMapper.selectList(new QueryWrapper<AvailableCamp>().select(
                    AvailableCamp.AVAILABLE_ID
            ).in(AvailableCamp.CAMP_ID, campIds).eq(AvailableCamp.AVAILABLE_TYPE, 2)).stream().map(AvailableCamp::getAvailableId).collect(Collectors.toSet());
            if (mediaIds.isEmpty()) {
                return RespUtils.success(req.getPageNum(), req.getPageSize(), 0L, Collections.emptyList());
            }
        }
        Set<Long> finalMediaIds = mediaIds;
        IPage<Medal> page = new Page<>(req.getPageNum(), req.getPageSize());
        page = medalMapper.selectPage(page, new QueryWrapper<Medal>().select(
                    Medal.MEDAL_NAME, Medal.MEDAL_ICON, Medal.MEDAL_TYPE, Medal.ID,
                    Medal.MEDAL_STATUS, Medal.TEMPLATE_NAME, Medal.MEDAL_CONTENT
            ).and(e -> e.eq(Medal.MEDAL_TYPE, 1)
                        .or(finalMediaIds == null, q -> q.eq(Medal.MEDAL_TYPE, 2))
                        .or(!CollectionUtils.isEmpty(finalMediaIds), q -> q.in(Medal.ID, finalMediaIds))
                )
            .like(StringUtils.hasLength(req.getMedalName()), Medal.MEDAL_NAME, req.getMedalName())
            .like(StringUtils.hasLength(req.getTemplateName()), Medal.TEMPLATE_NAME, req.getTemplateName())
            .orderByDesc(Medal.ID));
        if (page.getTotal() == 0) {
            return RespUtils.success(page.getCurrent(), page.getSize(), page.getTotal(), Collections.emptyList());
        }
        List<ItemResp> respList = page.getRecords().stream().map(this::toItemResp).toList();
        return RespUtils.success(page.getCurrent(), page.getSize(), page.getTotal(), respList);
    }

    public CommResp<List<ItemResp>> listByTeacher(Long userId, Long scheduleId) {
        Schedule schedule = scheduleMapper.selectOne(new QueryWrapper<Schedule>().select(
                Schedule.ID, Schedule.CAMP_ID
        ).eq(Schedule.ID, scheduleId));
        Set<Long> mediaIds = availableCampMapper.selectList(new QueryWrapper<AvailableCamp>().select(
                AvailableCamp.AVAILABLE_ID
        ).eq(AvailableCamp.CAMP_ID, schedule.getCampId())).stream().map(AvailableCamp::getAvailableId).collect(Collectors.toSet());
        mediaIds.addAll(medalRelationMapper.selectList(new QueryWrapper<MedalRelation>().select(
                MedalRelation.MEDAL_ID
        ).eq(MedalRelation.USER_ID, userId).eq(MedalRelation.SCHEDULE_ID, scheduleId))
                        .stream().map(MedalRelation::getMedalId).collect(Collectors.toSet()));
        List<Medal> medals = medalMapper.selectList(new QueryWrapper<Medal>().select(
                Medal.MEDAL_NAME, Medal.MEDAL_ICON, Medal.MEDAL_TYPE, Medal.ID,
                Medal.MEDAL_STATUS, Medal.TEMPLATE_NAME, Medal.MEDAL_CONTENT
        ).and(e -> e.eq(Medal.MEDAL_TYPE, 1)
                .or(!CollectionUtils.isEmpty(mediaIds), q -> q.in(Medal.ID, mediaIds))
        ));
        return RespUtils.success(medals.stream().map(this::toItemResp).toList());
    }

    public CommResp<DetailResp> detail(Long id) {
        Medal medal = medalMapper.selectOne(new QueryWrapper<Medal>().select(
                Medal.MEDAL_NAME, Medal.MEDAL_ICON, Medal.ID, Medal.MEDAL_CONTENT, Medal.TEMPLATE_NAME,
                Medal.MEDAL_STATUS, Medal.MEDAL_TYPE
        ).eq(Medal.ID, id));
        if (medal == null) {
            return RespUtils.warning(RespMetaEnum.PARAM_ERROR);
        }
        DetailResp detailResp = toDetailResp(medal);
        if (medal.getMedalType() == 3) {
            List<MedalRelation> medalRelations = medalRelationMapper.selectList(new QueryWrapper<MedalRelation>().select(
                    MedalRelation.CLAZZ_ID
            ).eq(MedalRelation.MEDAL_ID, medal.getId()));
            if (medalRelations.size() == 1 && medalRelations.getFirst().getClazzId() == -1) {
                detailResp.setType((byte) 1);
            } else {
                detailResp.setType((byte) 2);
                detailResp.setIds(medalRelations.stream().map(MedalRelation::getClazzId).toList());
            }
        } else if (medal.getMedalType() == 2) {
            detailResp.setType((byte) 2);
            List<AvailableCamp> availableCamps = availableCampMapper.selectList(new QueryWrapper<AvailableCamp>().select(
                    AvailableCamp.CAMP_ID
            ).eq(AvailableCamp.AVAILABLE_ID, medal.getId()));
            detailResp.setIds(availableCamps.stream().map(AvailableCamp::getCampId).toList());
        } else {
            detailResp.setType((byte) 1);
        }

        return RespUtils.success(detailResp);
    }

    @Transactional(rollbackFor = Exception.class)
    public void addOrEdit(SaveReq req) {
        if (req.getId() != null) {
            edit(req.getType(), req);
            availableCampMapper.delete(new QueryWrapper<AvailableCamp>().eq(AvailableCamp.AVAILABLE_ID, req.getId()));
        } else {
            add(req.getType(), req);
        }
        if (req.getType() == 2 && !CollectionUtils.isEmpty(req.getIds())) {
            availableCampMapper.insert(req.getIds().stream().map(campId -> {
                AvailableCamp availableCamp = new AvailableCamp();
                availableCamp.setAvailableId(req.getId());
                availableCamp.setCampId(campId);
                availableCamp.setAvailableType((byte) 2);
                return availableCamp;
            }).toList());
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void addOrEditByTeacher(Long userId, SaveReq req) {
        if (req.getType() == 1) {
            req.setIds(Collections.singletonList(-1L));
        }
        if (req.getId() != null) {
            edit((byte) 3, req);
            medalRelationMapper.delete(new UpdateWrapper<MedalRelation>().eq(MedalRelation.MEDAL_ID, req.getId()));
        } else {
            add((byte) 3, req);
        }
        medalRelationMapper.insert(req.getIds().stream().map(clazzId -> {
            MedalRelation relation = new MedalRelation();
            relation.setScheduleId(req.getScheduleId());
            relation.setUserId(userId);
            relation.setMedalId(req.getId());
            relation.setClazzId(clazzId);
            return relation;
        }).toList());
    }

    private void add(Byte type, SaveReq req) {
        Medal medal = toMedal(req);
        medal.setMedalType(type);
        medalMapper.insert(medal);
        req.setId(medal.getId());
    }

    private void edit(Byte type, SaveReq saveReq) {
        Medal oldMedal = medalMapper.selectOne(new QueryWrapper<Medal>()
                .select(Medal.ID).eq(Medal.ID, saveReq.getId()));
        if (oldMedal == null) {
            throw new WebBaseException(RespMetaEnum.PARAM_ERROR);
        }
        Medal medal = toMedal(saveReq);
        medal.setMedalType(type);
        medalMapper.updateById(medal);
    }

    private ItemResp toItemResp(Medal medal) {
        ItemResp itemResp = new ItemResp();
        itemResp.setMedalName(medal.getMedalName());
        itemResp.setMedalIcon(medal.getMedalIcon());
        itemResp.setMedalType(medal.getMedalType());
        itemResp.setId(medal.getId());
        itemResp.setMedalStatus(medal.getMedalStatus());
        itemResp.setTemplateName(medal.getTemplateName());
        itemResp.setMedalContent(medal.getMedalContent());
        return itemResp;
    }

    private DetailResp toDetailResp(Medal medal) {
        DetailResp detailResp = new DetailResp();
        detailResp.setId(medal.getId());
        detailResp.setMedalName(medal.getMedalName());
        detailResp.setMedalIcon(medal.getMedalIcon());
        detailResp.setMedalContent(medal.getMedalContent());
        detailResp.setTemplateName(medal.getTemplateName());
        detailResp.setMedalStatus(medal.getMedalStatus());
        detailResp.setMedalType(medal.getMedalType());
        return detailResp;
    }

    private Medal toMedal(SaveReq saveReq) {
        Medal medal = new Medal();
        medal.setId(saveReq.getId());
        medal.setMedalName(saveReq.getMedalName());
        medal.setMedalIcon(saveReq.getMedalIcon());
        medal.setMedalContent(saveReq.getMedalContent());
        medal.setMedalStatus(saveReq.getMedalStatus());
        medal.setTemplateName(saveReq.getTemplateName());
        return medal;
    }

    @Transactional(rollbackFor = Exception.class)
    public void add(Long userId, AddReq addReq) {
        List<Order> orderList = orderMapper.selectList(new QueryWrapper<Order>().select(
                        Order.ID, Order.USER_ID, Order.SCHEDULE_ID
                )
                .eq(Order.CLAZZ_ID, addReq.getClazzId()).in(Order.ID, addReq.getOrderIdList())
                .eq(Order.ORDER_STATUS, Status.NORMAL.getCode()));
        if (orderList.size() != addReq.getOrderIdList().size()) {
            throw new WebBaseException(RespMetaEnum.PARAM_ERROR);
        }
        Medal medal = medalMapper.selectOne(new QueryWrapper<Medal>().select(
                Medal.ID, Medal.MEDAL_NAME, Medal.MEDAL_ICON, Medal.MEDAL_CONTENT, Medal.TEMPLATE_NAME
        ).eq(Medal.ID, addReq.getMedalId()));
        if (medal == null) {
            throw new WebBaseException(RespMetaEnum.PARAM_ERROR);
        }
        Long scheduleId = orderList.getFirst().getScheduleId();
        MedalRecord medalRecord = toMedalRecord(userId, scheduleId, medal);
        medalRecordMapper.insert(medalRecord);
        List<OrderMedal> orderMedalList = orderList.stream().map(order -> {
            OrderMedal orderMedal = new OrderMedal();
            orderMedal.setScheduleId(order.getScheduleId());
            orderMedal.setUserId(order.getUserId());
            orderMedal.setOrderId(order.getId());
            orderMedal.setMedalId(medalRecord.getId());
            return orderMedal;
        }).toList();
        orderMedalMapper.insert(orderMedalList);

        Map<Long, Long> operationMap = orderMedalList.stream().collect(Collectors.toMap(OrderMedal::getOrderId, OrderMedal::getId));
        operationManager.saveLog(userId, operationMap, 18);
        // 增加奖状记录数
        orderStatisticMapper.update(new UpdateWrapper<OrderStatistic>()
                .setSql(String.format("%s = %s + 1", OrderStatistic.MEDAL_NUM, OrderStatistic.MEDAL_NUM))
                .in(OrderStatistic.ORDER_ID, orderList.stream().map(Order::getId).toList())
        );
    }

    private MedalRecord toMedalRecord(Long userId, Long scheduleId, Medal medal) {
        MedalRecord medalRecord = new MedalRecord();
        medalRecord.setCreatedUserId(userId);
        medalRecord.setScheduleId(scheduleId);
        medalRecord.setMedalName(medal.getMedalName());
        medalRecord.setMedalIcon(medal.getMedalIcon());
        medalRecord.setMedalContent(medal.getMedalContent());
        medalRecord.setTemplateName(medal.getTemplateName());
        return medalRecord;
    }
}
