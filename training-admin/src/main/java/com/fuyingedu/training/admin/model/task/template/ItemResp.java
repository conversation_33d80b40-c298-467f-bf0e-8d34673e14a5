package com.fuyingedu.training.admin.model.task.template;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalTime;

@Getter
@Setter
@ToString
public class ItemResp {

    private Long id;
    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 开始时间
     */
    private LocalTime startTime;

    /**
     * 结束时间
     */
    private LocalTime endTime;

    /**
     * 任务类型 1-签到 2-打卡 3-作业
     */
    private Byte taskType;

    /**
     * 任务积分
     */
    private Integer taskReward;

    /**
     * 扶小鹰小太阳
     */
    private Integer fxyReward;


    private Integer wordReward;
}
