package com.fuyingedu.training.admin.model.live;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class UpdateReq {

    /**
     * 直播id
     */
    @NotNull
    private Long id;

    /**
     * 直播信息
     */
    @NotBlank
    private String liveInfo;

    /**
     * 直播房间号
     */
    private String liveRoom;

    /**
     * 直播密码
     */
    private String livePassword;

    /**
     * 回放链接
     */
    private String repeatUrl;
}
