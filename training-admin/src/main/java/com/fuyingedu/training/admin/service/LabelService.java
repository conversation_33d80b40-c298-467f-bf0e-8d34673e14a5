package com.fuyingedu.training.admin.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fuyingedu.training.admin.model.label.ItemResp;
import com.fuyingedu.training.admin.model.label.ListReq;
import com.fuyingedu.training.admin.model.label.SaveReq;
import com.fuyingedu.training.common.model.CommResp;
import com.fuyingedu.training.common.util.RespUtils;
import com.fuyingedu.training.entity.Label;
import com.fuyingedu.training.front.model.media.MediaConvertor;
import com.fuyingedu.training.mapper.LabelMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class LabelService {

    @Autowired
    private LabelMapper labelMapper;

    public CommResp<List<ItemResp>> list(ListReq listReq) {
        IPage<Label> page = new Page<>(listReq.getPageNum(), listReq.getPageSize());
        page = labelMapper.selectPage(page, new QueryWrapper<Label>().select(
                Label.ID,
                Label.LABEL_NAME,
                Label.MEDIA_URL,
                Label.LABEL_TYPE,
                Label.LABEL_STATUS
        ).eq(listReq.getLabelStatus() != null, Label.LABEL_STATUS, listReq.getLabelStatus())
                .orderByDesc(Label.ID));
        List<ItemResp> respList = page.getRecords().stream().map(this::toItemResp).toList();
        return RespUtils.success(page.getCurrent(), page.getSize(), page.getTotal(), respList);
    }

    public void addOrEdit(SaveReq saveReq) {
        Label label = toLabel(saveReq);
        if (saveReq.getId() == null) {
            labelMapper.insert(label);
        } else {
            labelMapper.updateById(label);
        }
    }

    private ItemResp toItemResp(Label label) {
        ItemResp itemResp = new ItemResp();
        itemResp.setLabelStatus(label.getLabelStatus());
        itemResp.setId(label.getId());
        itemResp.setLabelName(label.getLabelName());
        itemResp.setLabelType(label.getLabelType());
        itemResp.setMediaUrl(MediaConvertor.getMediaUrl(label.getMediaUrl()));
        return itemResp;
    }

    private Label toLabel(SaveReq saveReq) {
        Label label = new Label();
        label.setId(saveReq.getId());
        label.setLabelName(saveReq.getLabelName());
        label.setLabelType(saveReq.getLabelType());
        label.setMediaUrl(MediaConvertor.getUrlSuffix(saveReq.getMediaUrl()));
        label.setLabelStatus(saveReq.getLabelStatus());
        return label;
    }
}
