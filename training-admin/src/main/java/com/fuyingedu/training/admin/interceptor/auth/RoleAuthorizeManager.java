package com.fuyingedu.training.admin.interceptor.auth;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fuyingedu.training.entity.Role;
import com.fuyingedu.training.entity.UserRole;
import com.fuyingedu.training.mapper.RoleMapper;
import com.fuyingedu.training.mapper.UserRoleMapper;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class RoleAuthorizeManager implements AuthorizeManager {

    @Autowired
    private UserRoleMapper userRoleMapper;
    @Autowired
    private RoleMapper roleMapper;

    @Override
    public boolean initAuth(Object userId, String[] values, HttpServletRequest request) {
        Set<Long> roleIds = userRoleMapper.selectList(new QueryWrapper<UserRole>().select(
                UserRole.ROLE_ID
        ).eq(UserRole.USER_ID, userId)).stream().map(UserRole::getRoleId).collect(Collectors.toSet());
        if (roleIds.isEmpty()) {
            return false;
        }
        request.setAttribute("roleIds", roleIds);
        Set<String> roleNameSet = roleMapper.selectList(new QueryWrapper<Role>().select(Role.ROLE_NAME).in(Role.ID, roleIds))
                .stream().map(Role::getRoleName).collect(Collectors.toSet());
        for (String auth : values) {
            if (roleNameSet.contains(auth)) {
                return true;
            }
        }
        return false;
    }

    @Override
    public boolean isAdmin(Long userId) {
        List<Long> roleIds = userRoleMapper.selectList(new QueryWrapper<UserRole>().select(
                UserRole.ROLE_ID
        ).eq(UserRole.USER_ID, userId)).stream().map(UserRole::getRoleId).toList();
        return roleIds.contains(2L) || roleIds.contains(3L);
    }
}
