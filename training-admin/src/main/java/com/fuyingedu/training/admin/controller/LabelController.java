package com.fuyingedu.training.admin.controller;

import com.fuyingedu.training.admin.model.label.ItemResp;
import com.fuyingedu.training.admin.model.label.ListReq;
import com.fuyingedu.training.admin.model.label.SaveReq;
import com.fuyingedu.training.admin.service.LabelService;
import com.fuyingedu.training.common.model.CommResp;
import com.fuyingedu.training.common.model.PageReq;
import com.fuyingedu.training.common.util.RespUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 标签管理
 */
@Slf4j
@RestController
@RequestMapping("admin/label")
public class LabelController {

    @Autowired
    private LabelService labelService;

    /**
     * 标签列表
     */
    @GetMapping("list")
    public CommResp<List<ItemResp>> list(ListReq listReq) {
        return labelService.list(listReq);
    }

    /**
     * 标签新增/编辑
     */
    @PostMapping("save")
    public CommResp<?> addOrEdit(@RequestBody SaveReq saveReq) {
        labelService.addOrEdit(saveReq);
        return RespUtils.success();
    }
}
