package com.fuyingedu.training.admin.model.camp;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Getter
@Setter
@ToString
public class RelationReq {

    /**
     * 训练营ID
     */
    @NotNull
    private Long campId;

    /**
     * 对应的课程信息
     */
    @NotEmpty
    private List<Relation> relationList;
}
