package com.fuyingedu.training.admin.config;

import com.aliyun.openservices.ons.api.ONSFactory;
import com.aliyun.openservices.ons.api.Producer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class MqProducerConfig {

    @Autowired
    private MqConfig mqConfig;

    @Bean
    public Producer producer() {
        Producer producer = ONSFactory.createProducer(mqConfig.getProperties());
        producer.start();
        return producer;
    }
}
