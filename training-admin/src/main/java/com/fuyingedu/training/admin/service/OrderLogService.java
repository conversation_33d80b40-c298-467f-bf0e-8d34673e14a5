package com.fuyingedu.training.admin.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fuyingedu.training.admin.model.order.log.*;
import com.fuyingedu.training.common.model.CommResp;
import com.fuyingedu.training.common.util.DateUtils;
import com.fuyingedu.training.common.util.RespUtils;
import com.fuyingedu.training.dto.order.LogRet;
import com.fuyingedu.training.entity.*;
import com.fuyingedu.training.mapper.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class OrderLogService {

    @Autowired
    private OperationLogMapper operationLogMapper;
    @Autowired
    private UserMapper userMapper;
    @Autowired
    private ScheduleMapper scheduleMapper;
    @Autowired
    private TeacherMapper teacherMapper;
    @Autowired
    private ClazzMapper clazzMapper;
    @Autowired
    private GroupMapper groupMapper;
    @Autowired
    private ScheduleTeacherMapper scheduleTeacherMapper;
    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private StudentMapper studentMapper;
    @Autowired
    private FxyMapper fxyMapper;
    @Autowired
    private OrderFxyMapper orderFxyMapper;
    @Autowired
    private OrderRewardLogMapper orderRewardLogMapper;
    @Autowired
    private OrderFxyRewardLogMapper orderFxyRewardLogMapper;
    @Autowired
    private WordRewardLogMapper wordRewardLogMapper;
    @Autowired
    private MedalMapper medalMapper;
    @Autowired
    private OrderMedalMapper orderMedalMapper;
    @Autowired
    private TaskMapper taskMapper;
    @Autowired
    private TaskSubmitRecordMapper taskSubmitRecordMapper;

    public CommResp<List<ItemResp>> list(Long orderId) {
        List<OperationLog> logList = operationLogMapper.selectList(new LambdaQueryWrapper<OperationLog>().select(
                OperationLog::getId, OperationLog::getCreatedTime, OperationLog::getUserId, OperationLog::getOperationType,
                OperationLog::getPrevSchedule, OperationLog::getNextSchedule, OperationLog::getPrevTeacher, OperationLog::getNextTeacher,
                OperationLog::getPrevAssistant, OperationLog::getNextAssistant, OperationLog::getPrevContent, OperationLog::getNextContent
        ).eq(OperationLog::getOrderId, orderId).eq(OperationLog::getLogStatus, 1).orderByDesc(OperationLog::getId));
        if (logList.isEmpty()) {
            return RespUtils.success(Collections.emptyList());
        }
        Map<Long, User> userMap = userMapper.selectList(new LambdaQueryWrapper<User>().select(
                User::getId, User::getRealName
        ).in(User::getId, logList.stream().map(OperationLog::getUserId).toList())).stream().collect(Collectors.toMap(User::getId, user -> user));
        Set<Long> scheduleIds = new HashSet<>();
        scheduleIds.addAll(logList.stream().map(OperationLog::getPrevSchedule).toList());
        scheduleIds.addAll(logList.stream().map(OperationLog::getNextSchedule).toList());
        Map<Long, Schedule> scheduleMap = scheduleMapper.selectList(new LambdaQueryWrapper<Schedule>().select(
                Schedule::getId, Schedule::getScheduleName
        ).in(Schedule::getId, scheduleIds)).stream().collect(Collectors.toMap(Schedule::getId, schedule -> schedule));
        Set<Long> teacherIds = new HashSet<>();
        teacherIds.addAll(logList.stream().map(OperationLog::getPrevTeacher).toList());
        teacherIds.addAll(logList.stream().map(OperationLog::getNextTeacher).toList());
        Map<Long, Teacher> teacherMap = teacherMapper.selectList(new LambdaQueryWrapper<Teacher>().select(
                Teacher::getId, Teacher::getRealName
        ).in(Teacher::getId, teacherIds)).stream().collect(Collectors.toMap(Teacher::getId, teacher -> teacher));
        Set<Long> clazzIds = new HashSet<>();
        clazzIds.addAll(logList.stream().map(OperationLog::getPrevContent).filter(Objects::nonNull).toList());
        clazzIds.addAll(logList.stream().map(OperationLog::getNextContent).filter(Objects::nonNull).toList());
        Map<Long, Clazz> clazzMap = Collections.emptyMap();
        if (!clazzIds.isEmpty()) {
            clazzMap = clazzMapper.selectList(new LambdaQueryWrapper<Clazz>().select(
                            Clazz::getId, Clazz::getClassName
                    ).in(Clazz::getId, clazzIds))
                    .stream().collect(Collectors.toMap(Clazz::getId, Function.identity()));
        }
        Set<Long> groupIds = new HashSet<>();
        groupIds.addAll(logList.stream().map(OperationLog::getPrevContent).filter(Objects::nonNull).collect(Collectors.toSet()));
        groupIds.addAll(logList.stream().map(OperationLog::getPrevContent).filter(Objects::nonNull).collect(Collectors.toSet()));
        Map<Long, Group> groupMap = Collections.emptyMap();
        if (!groupIds.isEmpty()) {
            groupMap = groupMapper.selectList(new LambdaQueryWrapper<Group>().select(
                            Group::getId, Group::getGroupName
                    ).in(Group::getId, groupIds))
                    .stream().collect(Collectors.toMap(Group::getId, Function.identity()));
        }

        List<Long> logIds = logList.stream().filter(item -> item.getOperationType() == 14).map(OperationLog::getNextContent).toList();
        Map<Long, OrderRewardLog> rewardLogMap = Collections.emptyMap();
        if (!logIds.isEmpty()) {
            rewardLogMap = orderRewardLogMapper.selectList(new LambdaQueryWrapper<OrderRewardLog>().select(
                            OrderRewardLog::getId, OrderRewardLog::getTaskReward
                    ).in(OrderRewardLog::getId, logIds))
                    .stream().collect(Collectors.toMap(OrderRewardLog::getId, Function.identity()));
        }
        List<Long> fxyLogIds = logList.stream().filter(item -> item.getOperationType() == 15).map(OperationLog::getNextContent).toList();
        Map<Long, OrderFxyRewardLog> fxyRewardLogMap = Collections.emptyMap();
        if (!fxyLogIds.isEmpty()) {
            fxyRewardLogMap = orderFxyRewardLogMapper.selectList(new LambdaQueryWrapper<OrderFxyRewardLog>().select(
                            OrderFxyRewardLog::getId, OrderFxyRewardLog::getTaskReward
                    ).in(OrderFxyRewardLog::getId, fxyLogIds)).stream().collect(Collectors.toMap(OrderFxyRewardLog::getId, Function.identity()));
        }
        List<Long> wordLogIds = logList.stream().filter(item -> item.getOperationType() == 16).map(OperationLog::getNextContent).toList();
        Map<Long, WordRewardLog> wordRewardLogMap = Collections.emptyMap();
        if (!wordLogIds.isEmpty()) {
            wordRewardLogMap = wordRewardLogMapper.selectList(new LambdaQueryWrapper<WordRewardLog>().select(
                            WordRewardLog::getId, WordRewardLog::getRewardNum
                    ).in(WordRewardLog::getId, wordLogIds)).stream().collect(Collectors.toMap(WordRewardLog::getId, Function.identity()));
        }
        List<Long> recordIds = logList.stream().filter(item -> item.getOperationType() == 17).map(OperationLog::getNextContent).toList();
        Map<Long, String> taskNameMap = Collections.emptyMap();
        if (!recordIds.isEmpty()) {
            Map<Long, Long> recordTaskIdMap = taskSubmitRecordMapper.selectList(new LambdaQueryWrapper<TaskSubmitRecord>().select(
                    TaskSubmitRecord::getId, TaskSubmitRecord::getTaskId
            ).in(TaskSubmitRecord::getId, recordIds)).stream().collect(Collectors.toMap(TaskSubmitRecord::getId, TaskSubmitRecord::getTaskId));
            Map<Long, String> taskMap = taskMapper.selectList(new LambdaQueryWrapper<Task>().select(
                    Task::getId, Task::getTaskName
            ).in(Task::getId, recordTaskIdMap.values())).stream().collect(Collectors.toMap(Task::getId, Task::getTaskName));
            taskNameMap = new HashMap<>(recordTaskIdMap.size());
            for (Map.Entry<Long, Long> entry : recordTaskIdMap.entrySet()) {
                taskNameMap.put(entry.getKey(), taskMap.get(entry.getValue()));
            }
        }
        List<Long> medalRecordIds = logList.stream().filter(item -> item.getOperationType() == 18).map(OperationLog::getNextContent).toList();
        Map<Long, String> medalNameMap = Collections.emptyMap();
        if (!medalRecordIds.isEmpty()) {
            Map<Long, Long> medalRecordMedalIdMap = orderMedalMapper.selectList(new LambdaQueryWrapper<OrderMedal>().select(
                    OrderMedal::getId, OrderMedal::getMedalId
            ).in(OrderMedal::getId, medalRecordIds)).stream().collect(Collectors.toMap(OrderMedal::getId, OrderMedal::getMedalId));
            Map<Long, String> medalMap = medalMapper.selectList(new LambdaQueryWrapper<Medal>().select(
                    Medal::getId, Medal::getMedalName
            ).in(Medal::getId, medalRecordMedalIdMap.values())).stream().collect(Collectors.toMap(Medal::getId, Medal::getMedalName));
            medalNameMap = new HashMap<>();
            for (Map.Entry<Long, Long> entry : medalRecordMedalIdMap.entrySet()) {
                medalNameMap.put(entry.getKey(), medalMap.get(entry.getValue()));
            }
        }
        List<ItemResp> respList = new ArrayList<>(logList.size());
        for (OperationLog operationLog : logList) {
            ItemResp itemResp = new ItemResp();
            itemResp.setCreatedTime(operationLog.getCreatedTime());
            if (Long.valueOf(-1L).equals(operationLog.getUserId())) {
                itemResp.setUsername("系统");
            } else {
                User user = userMap.get(operationLog.getUserId());
                itemResp.setUsername(user.getRealName());
            }
            itemResp.setOperationType(operationLog.getOperationType());
            if (operationLog.getOperationType() == 1) {
                Schedule prevSchedule = scheduleMap.get(operationLog.getPrevSchedule());
                Schedule nextSchedule = scheduleMap.get(operationLog.getNextSchedule());
                itemResp.setNextContent(prevSchedule.getScheduleName());
                itemResp.setNextContent(nextSchedule.getScheduleName());
            } else if (operationLog.getOperationType() == 2 || operationLog.getOperationType() == 6) {
                Teacher prevTeacher = teacherMap.get(operationLog.getPrevTeacher());
                if (prevTeacher != null) {
                    itemResp.setPrevContent(prevTeacher.getRealName());
                }
                Teacher nextTeacher = teacherMap.get(operationLog.getNextTeacher());
                if (nextTeacher != null) {
                    itemResp.setNextContent(nextTeacher.getRealName());
                }
            } else if (operationLog.getOperationType() == 8) {
                Clazz prevClazz = clazzMap.get(operationLog.getPrevContent());
                if (prevClazz != null) {
                    itemResp.setPrevContent(prevClazz.getClassName());
                }
                Clazz nextClazz = clazzMap.get(operationLog.getNextContent());
                if (nextClazz != null) {
                    itemResp.setNextContent(nextClazz.getClassName());
                }
            } else if (operationLog.getOperationType() == 9) {
                Group group = groupMap.get(operationLog.getPrevContent());
                if (group != null) {
                    itemResp.setPrevContent(group.getGroupName());
                }
                Group nextGroup = groupMap.get(operationLog.getNextContent());
                if (nextGroup != null) {
                    itemResp.setNextContent(nextGroup.getGroupName());
                }
            } else if (operationLog.getOperationType() == 14) {
                OrderRewardLog rewardLog = rewardLogMap.get(operationLog.getNextContent());
                itemResp.setNextContent(rewardLog.getTaskReward().toString());
            } else if (operationLog.getOperationType() == 15) {
                OrderFxyRewardLog fxyRewardLog = fxyRewardLogMap.get(operationLog.getNextContent());
                itemResp.setNextContent(fxyRewardLog.getTaskReward().toString());
            } else if (operationLog.getOperationType() == 16) {
                WordRewardLog wordRewardLog = wordRewardLogMap.get(operationLog.getNextContent());
                itemResp.setNextContent(wordRewardLog.getRewardNum().toString());
            } else if (operationLog.getOperationType() == 17) {
                itemResp.setNextContent(String.format("你的作业%s已推荐为优秀作业。",  taskNameMap.get(operationLog.getNextContent())));
            } else if (operationLog.getOperationType() == 18) {
                itemResp.setNextContent(String.format("你获得了1个奖状《%s》", medalNameMap.get(operationLog.getNextContent())));
            }
            respList.add(itemResp);
        }
        return RespUtils.success(respList);
    }

    public CommResp<List<SubResp>> subList(Long userId, SubReq req) {
        Teacher teacher = teacherMapper.selectOne(new LambdaQueryWrapper<Teacher>().select(
                Teacher::getId
        ).eq(Teacher::getUserId, userId));
        ScheduleTeacher scheduleTeacher = scheduleTeacherMapper.selectOne(new LambdaQueryWrapper<ScheduleTeacher>().select(
                ScheduleTeacher::getId, ScheduleTeacher::getTeacherType
        ).eq(ScheduleTeacher::getTeacherId, teacher.getId()).eq(ScheduleTeacher::getScheduleId, req.getScheduleId()));
        IPage<OperationLog> page = new Page<>(req.getPageNum(), req.getPageSize());
        page = operationLogMapper.pageSubList(page, req.getScheduleId(), teacher.getId(), scheduleTeacher.getTeacherType(),
                req.getUserInfo(), req.getStudentInfo(), req.getRemark());
        List<SubResp> respList = getRespList(page.getRecords());
        return RespUtils.success(page.getCurrent(), page.getSize(), page.getTotal(), respList);
    }

    private List<SubResp> getRespList(List<OperationLog> logList) {
        if (logList.isEmpty()) {
            return Collections.emptyList();
        }
        Set<Long> orderIds = logList.stream().map(OperationLog::getOrderId).collect(Collectors.toSet());
        Map<Long, Order> orderMap = orderMapper.selectList(new LambdaQueryWrapper<Order>().select(
                Order::getId, Order::getUserId, Order::getOrderRemark, Order::getStudentId
                ).in(Order::getId, orderIds)).stream().collect(Collectors.toMap(Order::getId, Function.identity()));
        Set<Long> userIds = new HashSet<>();
        userIds.addAll(logList.stream().map(OperationLog::getUserId).toList());
        userIds.addAll(orderMap.values().stream().map(Order::getUserId).toList());
        Map<Long, User> userMap = userMapper.selectList(new LambdaQueryWrapper<User>().select(
                User::getId, User::getRealName, User::getUserIcon, User::getPhoneNum
        ).in(User::getId, userIds)).stream().collect(Collectors.toMap(User::getId, user -> user));
        Map<Long, Long> bindOrderIds = orderFxyMapper.selectList(new QueryWrapper<OrderFxy>().select(
                OrderFxy.ORDER_ID, OrderFxy.FXY_ID
        ).in(OrderFxy.ORDER_ID, orderIds)).stream().collect(Collectors.toMap(OrderFxy::getOrderId, OrderFxy::getFxyId));
        Map<Long, Fxy> fxyMap = Collections.emptyMap();
        if (!bindOrderIds.isEmpty()) {
            fxyMap = fxyMapper.selectList(new QueryWrapper<Fxy>().select(
                    Fxy.ID, Fxy.ACCOUNT_INFO, Fxy.NICK_NAME
            ).in(Fxy.ID, bindOrderIds.values())).stream().collect(Collectors.toMap(Fxy::getId, Function.identity()));
        }
        Set<Long> scheduleIds = new HashSet<>();
        scheduleIds.addAll(logList.stream().map(OperationLog::getPrevSchedule).toList());
        scheduleIds.addAll(logList.stream().map(OperationLog::getNextSchedule).toList());
        Map<Long, Schedule> scheduleMap = scheduleMapper.selectList(new LambdaQueryWrapper<Schedule>().select(
                Schedule::getId, Schedule::getScheduleName
        ).in(Schedule::getId, scheduleIds)).stream().collect(Collectors.toMap(Schedule::getId, schedule -> schedule));
        Set<Long> teacherIds = new HashSet<>();
        teacherIds.addAll(logList.stream().map(OperationLog::getPrevTeacher).toList());
        teacherIds.addAll(logList.stream().map(OperationLog::getNextTeacher).toList());
        Map<Long, Teacher> teacherMap = teacherMapper.selectList(new LambdaQueryWrapper<Teacher>().select(
                Teacher::getId, Teacher::getRealName
        ).in(Teacher::getId, teacherIds)).stream().collect(Collectors.toMap(Teacher::getId, Function.identity()));
        Set<Long> clazzIds = new HashSet<>();
        clazzIds.addAll(logList.stream().map(OperationLog::getPrevContent).filter(Objects::nonNull).toList());
        clazzIds.addAll(logList.stream().map(OperationLog::getNextContent).filter(Objects::nonNull).toList());
        Map<Long, Clazz> clazzMap = Collections.emptyMap();
        if (!clazzIds.isEmpty()) {
            clazzMap = clazzMapper.selectList(new LambdaQueryWrapper<Clazz>().select(
                            Clazz::getId, Clazz::getClassName
                    ).in(Clazz::getId, clazzIds))
                    .stream().collect(Collectors.toMap(Clazz::getId, Function.identity()));
        }
        Set<Long> groupIds = new HashSet<>();
        groupIds.addAll(logList.stream().map(OperationLog::getPrevContent).filter(Objects::nonNull).collect(Collectors.toSet()));
        groupIds.addAll(logList.stream().map(OperationLog::getPrevContent).filter(Objects::nonNull).collect(Collectors.toSet()));
        Map<Long, Group> groupMap = Collections.emptyMap();
        if (!groupIds.isEmpty()) {
            groupMap = groupMapper.selectList(new LambdaQueryWrapper<Group>().select(
                            Group::getId, Group::getGroupName
                    ).in(Group::getId, groupIds))
                    .stream().collect(Collectors.toMap(Group::getId, Function.identity()));
        }
        Map<Long, Student> studentMap = Collections.emptyMap();
        Set<Long> studentIds = orderMap.values().stream().map(Order::getStudentId).filter(Objects::nonNull).collect(Collectors.toSet());
        if (!studentIds.isEmpty()) {
            studentMap = studentMapper.selectList(new LambdaQueryWrapper<Student>().select(
                    Student::getId, Student::getRealName, Student::getCardNum
            ).in(Student::getId, studentIds)).stream().collect(Collectors.toMap(Student::getId, Function.identity()));
        }
        List<SubResp> respList = new ArrayList<>(logList.size());
        for (OperationLog operationLog : logList) {
            SubResp itemResp = new SubResp();
            itemResp.setId(operationLog.getId());
            itemResp.setCreatedTime(operationLog.getCreatedTime());
            Order order = orderMap.get(operationLog.getOrderId());
            User user = userMap.get(order.getUserId());
            itemResp.setUserIcon(user.getUserIcon());
            itemResp.setPhoneNum(user.getPhoneNum());
            if (StringUtils.hasLength(order.getOrderRemark())) {
                itemResp.setUsername(order.getOrderRemark());
            } else {
                itemResp.setUsername(user.getRealName());
            }
            Long fxyId = bindOrderIds.get(order.getId());
            if (fxyId != null) {
                Fxy fxy = fxyMap.get(fxyId);
                itemResp.setFxyAccountInfo(fxy.getAccountInfo());
                itemResp.setFxyNickName(fxy.getNickName());
            }
            if (Long.valueOf(-1L).equals(operationLog.getUserId())) {
                itemResp.setOperatorName("系统");
            } else {
                User operator = userMap.get(operationLog.getUserId());
                itemResp.setOperatorName(operator.getRealName());
            }
            itemResp.setOperationType(operationLog.getOperationType());
            if (operationLog.getOperationType() == 1) {
                Schedule prevSchedule = scheduleMap.get(operationLog.getPrevSchedule());
                Schedule nextSchedule = scheduleMap.get(operationLog.getNextSchedule());
                itemResp.setNextContent(prevSchedule.getScheduleName());
                itemResp.setNextContent(nextSchedule.getScheduleName());
            } else if (operationLog.getOperationType() == 2 || operationLog.getOperationType() == 6) {
                Teacher prevTeacher = teacherMap.get(operationLog.getPrevTeacher());
                if (prevTeacher != null) {
                    itemResp.setPrevContent(prevTeacher.getRealName());
                }
                Teacher nextTeacher = teacherMap.get(operationLog.getNextTeacher());
                if (nextTeacher != null) {
                    itemResp.setNextContent(nextTeacher.getRealName());
                }
            } else if (operationLog.getOperationType() == 8) {
                Clazz prevClazz = clazzMap.get(operationLog.getPrevContent());
                if (prevClazz != null) {
                    itemResp.setPrevContent(prevClazz.getClassName());
                }
                Clazz nextClazz = clazzMap.get(operationLog.getNextContent());
                if (nextClazz != null) {
                    itemResp.setNextContent(nextClazz.getClassName());
                }
            } else if (operationLog.getOperationType() == 9) {
                Group group = groupMap.get(operationLog.getPrevContent());
                if (group != null) {
                    itemResp.setPrevContent(group.getGroupName());
                }
                Group nextGroup = groupMap.get(operationLog.getNextContent());
                if (nextGroup != null) {
                    itemResp.setNextContent(nextGroup.getGroupName());
                }
            }
            if (order.getStudentId() != null) {
                Student student = studentMap.get(order.getStudentId());
                if (student != null) {
                    itemResp.setStudentName(student.getRealName());
                }
            }
            respList.add(itemResp);
        }
        return respList;
    }

    @Transactional(rollbackFor = Exception.class)
    public CommResp<?> repeat(Long userId, Long id) {
        Teacher teacher = teacherMapper.selectOne(new LambdaQueryWrapper<Teacher>().select(
                Teacher::getId
        ).eq(Teacher::getUserId, userId));
        OperationLog operationLog = operationLogMapper.selectOne(new LambdaQueryWrapper<OperationLog>().select(
                OperationLog::getId, OperationLog::getOrderId, OperationLog::getOperationType, OperationLog::getPrevSchedule,
                OperationLog::getPrevTeacher
        ).eq(OperationLog::getId, id).eq(OperationLog::getOperationType, 1).eq(OperationLog::getLogStatus, 1));
        if (operationLog == null) {
            return RespUtils.warning(4000, "该操作已删除");
        }
        if (!teacher.getId().equals(operationLog.getPrevTeacher())) {
            return RespUtils.warning(4000, "之前该服务单不是您负责的");
        }
        Order order = orderMapper.selectOne(new LambdaQueryWrapper<Order>().select(
                Order::getId, Order::getScheduleId, Order::getTeacherId
        ).eq(Order::getId, operationLog.getOrderId()).in(Order::getOrderStatus, 1, 4));
        if (order == null) {
            return RespUtils.warning(4000, "该服务单不存在或已退款");
        }
        if (order.getTeacherId() != null && !order.getTeacherId().equals(teacher.getId())) {
            Teacher prevTeacher = teacherMapper.selectOne(new LambdaQueryWrapper<Teacher>().select(
                    Teacher::getId, Teacher::getRealName
            ).eq(Teacher::getId, order.getTeacherId()));
            return RespUtils.warning(4000, "用户已分配给辅导老师" + prevTeacher.getRealName() + "，无法重新添加");
        }
        orderMapper.update(new LambdaUpdateWrapper<Order>()
                .set(Order::getScheduleId, operationLog.getPrevSchedule())
                .set(Order::getTeacherId, teacher.getId())
                .eq(Order::getId, operationLog.getOrderId()));
        operationLogMapper.update(new LambdaUpdateWrapper<OperationLog>()
                .set(OperationLog::getLogStatus, 2).eq(OperationLog::getId, id));
        return RespUtils.success();
    }

    public CommResp<List<AddResp>> addList(Long userId, Long scheduleId) {
        Teacher teacher = teacherMapper.selectOne(new LambdaQueryWrapper<Teacher>().select(
                Teacher::getId
        ).eq(Teacher::getUserId, userId));
        ScheduleTeacher scheduleTeacher = scheduleTeacherMapper.selectOne(new LambdaQueryWrapper<ScheduleTeacher>().select(
                ScheduleTeacher::getId, ScheduleTeacher::getTeacherType
        ).eq(ScheduleTeacher::getTeacherId, teacher.getId()).eq(ScheduleTeacher::getScheduleId, scheduleId));
        List<LogRet> logRetList = operationLogMapper.groupByBatchId(scheduleTeacher.getTeacherType(), scheduleId, teacher.getId());
        if (logRetList.isEmpty()) {
            return RespUtils.success(Collections.emptyList());
        }
        Set<Long> userIds = logRetList.stream().map(LogRet::getUserId).collect(Collectors.toSet());
        Map<Long, User> userMap = userMapper.selectList(new LambdaQueryWrapper<User>().select(
                User::getId, User::getRealName
        ).in(User::getId, userIds)).stream().collect(Collectors.toMap(User::getId, user -> user));
        List<AddResp> respList = new ArrayList<>();
        for (LogRet logRet : logRetList) {
            AddResp addResp = new AddResp();
            addResp.setBatchId(logRet.getBatchId());
            addResp.setNum(logRet.getNum());
            addResp.setOperationType(logRet.getOperationType());
            User user = userMap.get(logRet.getUserId());
            addResp.setOperatorName(user.getRealName());
            addResp.setCreatedTime(DateUtils.millisToLocalDateTime(logRet.getBatchId()));
            respList.add(addResp);
        }
        return RespUtils.success(respList);
    }

    public CommResp<List<SubResp>> addDetailList(Long userId, DetailReq req) {
        Teacher teacher = teacherMapper.selectOne(new LambdaQueryWrapper<Teacher>().select(
                Teacher::getId
        ).eq(Teacher::getUserId, userId));
        ScheduleTeacher scheduleTeacher = scheduleTeacherMapper.selectOne(new LambdaQueryWrapper<ScheduleTeacher>().select(
                ScheduleTeacher::getId, ScheduleTeacher::getTeacherType
        ).eq(ScheduleTeacher::getTeacherId, teacher.getId()).eq(ScheduleTeacher::getScheduleId, req.getScheduleId()));
        IPage<OperationLog> page = new Page<>(req.getPageNum(), req.getPageSize());
        if (scheduleTeacher.getTeacherType() == 1) {
            page = operationLogMapper.selectPage(page, new LambdaQueryWrapper<OperationLog>().select(
                        OperationLog::getId, OperationLog::getOrderId, OperationLog::getOperationType,
                        OperationLog::getUserId, OperationLog::getCreatedTime
                ).eq(OperationLog::getNextSchedule, req.getScheduleId()).eq(OperationLog::getBatchId, req.getBatchId())
                .eq(OperationLog::getNextTeacher, teacher.getId()).ne(OperationLog::getPrevTeacher, teacher.getId())
                .eq(OperationLog::getLogStatus, 1)
                .orderByDesc(OperationLog::getId)
            );
        } else {
            page = operationLogMapper.selectPage(page, new LambdaQueryWrapper<OperationLog>().select(
                                    OperationLog::getId, OperationLog::getOrderId, OperationLog::getOperationType,
                                    OperationLog::getUserId, OperationLog::getCreatedTime
                            ).eq(OperationLog::getNextSchedule, req.getScheduleId()).eq(OperationLog::getBatchId, req.getBatchId())
                            .eq(OperationLog::getNextAssistant, teacher.getId()).ne(OperationLog::getPrevAssistant, teacher.getId())
                            .eq(OperationLog::getLogStatus, 1)
                            .orderByDesc(OperationLog::getId)
            );
        }
        return RespUtils.success(page.getCurrent(), page.getSize(), page.getTotal(), getRespList(page.getRecords()));
    }
}
