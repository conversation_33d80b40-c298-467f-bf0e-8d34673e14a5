package com.fuyingedu.training.admin.controller;

import com.fuyingedu.training.common.annotation.Login;
import com.fuyingedu.training.common.model.CommResp;
import com.fuyingedu.training.front.model.stats.GroupResp;
import com.fuyingedu.training.front.model.stats.RewardResp;
import com.fuyingedu.training.front.model.stats.ScheduleResp;
import com.fuyingedu.training.front.service.FrontStatsService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 排行榜
 * <AUTHOR>
 */
@RestController
@RequestMapping("admin/rank")
public class RankController {
    
    @Autowired
    private FrontStatsService frontStatsService;

    /**
     * 个人榜
     */
    @GetMapping("reward")
    public CommResp<ScheduleResp<RewardResp>> reward(@Login Long userId, Long clazzId) {
        return frontStatsService.rewardByClazzId(clazzId);
    }

    /**
     * 小组榜
     */
    @GetMapping("group")
    public CommResp<ScheduleResp<GroupResp>> group(@Login Long userId, Long clazzId) {
        return frontStatsService.groupByClazzId(clazzId);
    }

    /**
     * 小太阳个人榜
     */
    @GetMapping("fxy/reward")
    public CommResp<ScheduleResp<RewardResp>> fxyReward(@Login Long userId, Long clazzId) {
        return frontStatsService.fxyRewardByClazzId(clazzId);
    }

    /**
     * 小太阳小组榜
     */
    @GetMapping("fxy/group")
    public CommResp<ScheduleResp<GroupResp>> fxyGroup(@Login Long userId, Long clazzId) {
        return frontStatsService.fxyGroupByClazzId(clazzId);
    }
}
