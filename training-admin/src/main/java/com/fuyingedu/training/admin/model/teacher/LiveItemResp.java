package com.fuyingedu.training.admin.model.teacher;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.fuyingedu.training.admin.excel.LivePortConverter;
import com.fuyingedu.training.admin.excel.LiveTypeConverter;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;

@Getter
@Setter
@ToString
public class LiveItemResp {

    /**
     * 直播计划名称
     */
    @ExcelProperty("直播计划名称")
    private String liveName;

    /**
     * 直播开始时间
     */
    @ExcelProperty("直播开始时间")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    /**
     * 直播方式 1-公司直播 2-导师直播
     * 字典Key：LIVE_TYPE
     */
    @ExcelProperty(value = "直播方式", converter = LiveTypeConverter.class)
    private Byte liveType;

    /**
     * 直播端口 1-腾讯会议 2-保利威 3-百家云
     * 字典Key：LIVE_PORT
     */
    @ExcelProperty(value = "直播端口", converter = LivePortConverter.class)
    private Byte livePort;

    /**
     * 训练营ID
     */
    @ExcelIgnore
    private Long campId;

    /**
     * 训练营名称
     */
    @ExcelProperty("训练营名称")
    private String campName;

    /**
     * 排期ID
     */
    @ExcelIgnore
    private Long scheduleId;

    /**
     * 排期名称
     */
    @ExcelProperty("排期名称")
    private String scheduleName;
}
