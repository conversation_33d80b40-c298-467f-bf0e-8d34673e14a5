package com.fuyingedu.training.admin.config;

import com.aliyun.openservices.ons.api.Consumer;
import com.aliyun.openservices.ons.api.ONSFactory;
import com.fuyingedu.training.admin.mq.RocketMessageListener;
import jakarta.annotation.PostConstruct;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;

@Configuration
public class MqConsumerConfig {


    @Autowired
    private RocketMessageListener messageListener;
    @Autowired
    private MqConfig mqConfig;

    @PostConstruct
    public void init() {
        Consumer consumer = ONSFactory.createConsumer(mqConfig.getProperties());
        consumer.subscribe("training_camp", "complete_event_sync || pk_complete_event", messageListener);
        consumer.start();
    }

}
