package com.fuyingedu.training.admin.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fuyingedu.training.admin.model.task.UploadItem;
import com.fuyingedu.training.admin.model.task.template.*;
import com.fuyingedu.training.common.annotation.UserInfo;
import com.fuyingedu.training.common.enums.AvailableType;
import com.fuyingedu.training.common.enums.RespMetaEnum;
import com.fuyingedu.training.common.enums.TaskType;
import com.fuyingedu.training.common.exception.WebBaseException;
import com.fuyingedu.training.common.model.CommResp;
import com.fuyingedu.training.common.util.DateUtils;
import com.fuyingedu.training.common.util.JsonUtils;
import com.fuyingedu.training.common.util.RespUtils;
import com.fuyingedu.training.entity.*;
import com.fuyingedu.training.front.model.media.MediaConvertor;
import com.fuyingedu.training.mapper.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@Slf4j
public class TaskTemplateService {

    @Autowired
    private TaskTemplateMapper taskTemplateMapper;
    @Autowired
    private AvailableCampService availableCampService;
    @Autowired
    private AvailableCampMapper availableCampMapper;
    @Autowired
    private ScheduleMapper scheduleMapper;

    public CommResp<List<ItemResp>> list(UserInfo userInfo, ListReq req) {
        Set<Long> temaplateIdSet = null;
        if (!userInfo.getRoleIds().contains(2L)) {
            List<Long> campIds = availableCampMapper.selectList(new QueryWrapper<AvailableCamp>().select(
                            AvailableCamp.CAMP_ID
                    ).eq(AvailableCamp.AVAILABLE_ID, userInfo.getUserId()).eq(AvailableCamp.AVAILABLE_TYPE, 4).eq(AvailableCamp.CAMP_STATUS, 1))
                    .stream().map(AvailableCamp::getCampId).toList();
            if (campIds.isEmpty()) {
                return RespUtils.warning(RespMetaEnum.NO_AUTH);
            }
            temaplateIdSet = availableCampMapper.selectList(new QueryWrapper<AvailableCamp>().select(
                    AvailableCamp.AVAILABLE_ID
            ).in(AvailableCamp.CAMP_ID, campIds).eq(AvailableCamp.AVAILABLE_TYPE, 3)).stream().map(AvailableCamp::getAvailableId).collect(Collectors.toSet());
            if (temaplateIdSet.isEmpty()) {
                return RespUtils.success(req.getPageNum(), req.getPageSize(), 0L, Collections.emptyList());
            }
        }
        Set<Long> finalTemaplateIdSet = temaplateIdSet;
        IPage<TaskTemplate> page = new Page<>(req.getPageNum(), req.getPageSize());
        page = taskTemplateMapper.selectPage(page, new QueryWrapper<TaskTemplate>().select(
                TaskTemplate.ID,
                TaskTemplate.TASK_NAME,
                TaskTemplate.TASK_TYPE,
                TaskTemplate.TASK_REWARD,
                TaskTemplate.FXY_REWARD,
                TaskTemplate.CAMP_TYPE,
                TaskTemplate.START_TIME,
                TaskTemplate.END_TIME, TaskTemplate.WORD_REWARD
        ).like(StringUtils.hasLength(req.getTemplateName()), TaskTemplate.TASK_NAME, req.getTemplateName())
                        .and(temaplateIdSet != null,
                                e -> e.in(TaskTemplate.ID, finalTemaplateIdSet).or().eq(TaskTemplate.CAMP_TYPE, 1))
                .orderByDesc(TaskTemplate.ID));
        List<ItemResp> respList = page.getRecords().stream().map(this::toItemResp).toList();
        return RespUtils.success(page.getCurrent(), page.getSize(), page.getTotal(), respList);
    }

    public CommResp<DetailResp> detail(Long id) {
        TaskTemplate taskTemplate = taskTemplateMapper.selectOne(new QueryWrapper<TaskTemplate>().select(
                TaskTemplate.ID,
                TaskTemplate.TASK_NAME,
                TaskTemplate.TASK_TYPE,
                TaskTemplate.TASK_REWARD,
                TaskTemplate.FXY_REWARD,
                TaskTemplate.CAMP_TYPE,
                TaskTemplate.TASK_CONTENT,
                TaskTemplate.START_TIME,
                TaskTemplate.END_TIME,
                TaskTemplate.CASE_URLS,
                TaskTemplate.UPLOAD_ITEMS, TaskTemplate.WORD_REWARD
        ).eq(TaskTemplate.ID, id));
        if (taskTemplate == null) {
            return RespUtils.warning(RespMetaEnum.PARAM_ERROR);
        }
        DetailResp detailResp = toDetailResp(taskTemplate);
        List<Long> campIds = availableCampService.getCampIds(id, AvailableType.TASK_TEMPLATE.getCode());;
        detailResp.setCampIdList(campIds);
        detailResp.setStartTime(taskTemplate.getStartTime());
        detailResp.setEndTime(taskTemplate.getEndTime());
        if (TaskType.OUT_PUNCH.getCode().equals(taskTemplate.getTaskType())) {
            detailResp.setFxyLabelList(JsonUtils.parseJsonToList(taskTemplate.getUploadItems(), Byte.class));
        } else if (StringUtils.hasLength(taskTemplate.getUploadItems())) {
            detailResp.setItemList(JsonUtils.parseJsonToList(taskTemplate.getUploadItems(), UploadItem.class));
        }
        if (StringUtils.hasLength(taskTemplate.getCaseUrls())) {
            detailResp.setCaseMediaUrlList(MediaConvertor.getMediaList(taskTemplate.getCaseUrls()));
        }
        return RespUtils.success(detailResp);
    }

    @Transactional(rollbackFor = Exception.class)
    public void addOrEdit(SaveReq saveReq) {
        TaskTemplate taskTemplate;
        if (saveReq.getId() == null) {
            taskTemplate = add(saveReq);
        } else {
            taskTemplate = edit(saveReq);
        }
        availableCampService.add(taskTemplate.getId(), AvailableType.TASK_TEMPLATE.getCode(), saveReq.getCampIdList());
    }

    private TaskTemplate edit(SaveReq saveReq) {
        TaskTemplate oldTaskTemplate = taskTemplateMapper.selectOne(new QueryWrapper<TaskTemplate>().select(
                TaskTemplate.ID
        ).eq(TaskTemplate.ID, saveReq.getId()));
        if (oldTaskTemplate == null) {
            throw new WebBaseException(RespMetaEnum.PARAM_ERROR);
        }
        TaskTemplate taskTemplate = toTaskTemplate(saveReq);
        taskTemplateMapper.updateById(taskTemplate);
        return taskTemplate;
    }

    private TaskTemplate add(SaveReq saveReq) {
        TaskTemplate taskTemplate = toTaskTemplate(saveReq);
        taskTemplateMapper.insert(taskTemplate);
        return taskTemplate;
    }

    private ItemResp toItemResp(TaskTemplate taskTemplate) {
        ItemResp itemResp = new ItemResp();
        itemResp.setId(taskTemplate.getId());
        itemResp.setTaskName(taskTemplate.getTaskName());
        itemResp.setEndTime(taskTemplate.getEndTime() != null ? taskTemplate.getEndTime() : DateUtils.parseTime("23:59:59"));
        itemResp.setStartTime(taskTemplate.getStartTime() != null ? taskTemplate.getStartTime() : DateUtils.parseTime("00:00:00"));
        itemResp.setTaskType(taskTemplate.getTaskType());
        itemResp.setTaskReward(taskTemplate.getTaskReward());
        itemResp.setFxyReward(taskTemplate.getFxyReward());
        itemResp.setWordReward(taskTemplate.getWordReward());
        return itemResp;
    }

    private DetailResp toDetailResp(TaskTemplate taskTemplate) {
        DetailResp detailResp = new DetailResp();
        detailResp.setId(taskTemplate.getId());
        detailResp.setTaskName(taskTemplate.getTaskName());
        detailResp.setTaskContent(taskTemplate.getTaskContent());
        detailResp.setCampType(taskTemplate.getCampType());
        detailResp.setTaskType(taskTemplate.getTaskType());
        detailResp.setTaskReward(taskTemplate.getTaskReward());
        detailResp.setFxyReward(taskTemplate.getFxyReward());
        detailResp.setWordReward(taskTemplate.getWordReward());
        return detailResp;
    }

    private TaskTemplate toTaskTemplate(SaveReq saveReq) {
        TaskTemplate taskTemplate = new TaskTemplate();
        taskTemplate.setId(saveReq.getId());
        taskTemplate.setTaskName(saveReq.getTaskName());
        taskTemplate.setCampType(saveReq.getCampType());
        taskTemplate.setTaskType(saveReq.getTaskType());
        taskTemplate.setTaskContent(saveReq.getTaskContent());
        taskTemplate.setTaskReward(saveReq.getTaskReward());
        taskTemplate.setFxyReward(saveReq.getFxyReward());
        taskTemplate.setWordReward(saveReq.getWordReward());
        taskTemplate.setStartTime(saveReq.getStartTime());
        taskTemplate.setEndTime(saveReq.getEndTime());
        if (!CollectionUtils.isEmpty(saveReq.getCaseMediaUrlList())) {
            taskTemplate.setCaseUrls(MediaConvertor.getMediaUrls(saveReq.getCaseMediaUrlList()));
        } else {
            taskTemplate.setCaseUrls(JsonUtils.formatObjToJson(Collections.emptyList()));
        }
        if (TaskType.OUT_PUNCH.getCode().equals(saveReq.getTaskType())) {
            if (CollectionUtils.isEmpty(saveReq.getFxyLabelList())) {
                throw new WebBaseException(4000, "完成的标签不能为空");
            }
            taskTemplate.setUploadItems(JsonUtils.formatObjToJson(saveReq.getFxyLabelList()));
        } else if (!CollectionUtils.isEmpty(saveReq.getItemList())) {
            taskTemplate.setUploadItems(JsonUtils.formatObjToJson(saveReq.getItemList()));
        } else {
            taskTemplate.setUploadItems(JsonUtils.formatObjToJson(Collections.emptyList()));
        }
        return taskTemplate;
    }

    public CommResp<List<TypeItemResp>> typeList(Long scheduleId, Byte type) {
        Schedule schedule = scheduleMapper.selectOne(new QueryWrapper<Schedule>().select(
                Schedule.CAMP_ID
        ).eq(Schedule.ID, scheduleId));
        List<Long> templateIds = availableCampMapper.selectList(new QueryWrapper<AvailableCamp>().select(
                AvailableCamp.AVAILABLE_ID
                ).eq(AvailableCamp.CAMP_ID, schedule.getCampId())
                .eq(AvailableCamp.AVAILABLE_TYPE, AvailableType.TASK_TEMPLATE.getCode()))
                .stream().map(AvailableCamp::getAvailableId).toList();
        List<TaskTemplate> templateList = taskTemplateMapper.selectList(new QueryWrapper<TaskTemplate>()
                .eq(TaskTemplate.TASK_TYPE, type).and(e ->
                        e.eq(TaskTemplate.CAMP_TYPE, 1).or().in(!templateIds.isEmpty(), TaskTemplate.ID, templateIds)
                ));
        List<TypeItemResp> respList = templateList.stream().map(taskTemplate -> {
            TypeItemResp typeItemResp = new TypeItemResp();
            typeItemResp.setId(taskTemplate.getId());
            typeItemResp.setTaskName(taskTemplate.getTaskName());
            return typeItemResp;
        }).toList();
        return RespUtils.success(respList);
    }
}
