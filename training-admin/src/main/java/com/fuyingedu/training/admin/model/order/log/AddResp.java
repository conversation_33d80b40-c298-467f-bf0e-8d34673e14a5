package com.fuyingedu.training.admin.model.order.log;

import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
public class AddResp {

    private Long batchId;
    private Integer num;

    /**
     * 1-改期 2-分配辅导老师 3-关闭订单 4-核销订单 5-删除备注 6-修改辅导老师 7-备注 8-分班 9-分组 10-改班 11-改组 12-修改订单状态-可能退单 13-修改订单状态-正常
     */
    private Integer operationType;

    private String operatorName;

    private LocalDateTime createdTime;
}
