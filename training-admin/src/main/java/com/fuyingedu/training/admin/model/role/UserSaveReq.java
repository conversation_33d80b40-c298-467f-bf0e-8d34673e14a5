package com.fuyingedu.training.admin.model.role;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Getter
@Setter
@ToString
public class UserSaveReq {

    private Long id;

    /**
     * 用户手机号
     */
    @NotNull
    private Long phoneNum;

    @NotEmpty
    private List<Long> roleIds;
}
