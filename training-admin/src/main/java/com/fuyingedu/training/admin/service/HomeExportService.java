package com.fuyingedu.training.admin.service;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.SimpleColumnWidthStyleStrategy;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fuyingedu.training.admin.model.order.RecordItemResp;
import com.fuyingedu.training.admin.model.order.RecordListReq;
import com.fuyingedu.training.common.enums.CampType;
import com.fuyingedu.training.common.enums.StudentFlag;
import com.fuyingedu.training.common.exception.WebBaseException;
import com.fuyingedu.training.common.model.CommResp;
import com.fuyingedu.training.common.util.DateUtils;
import com.fuyingedu.training.entity.*;
import com.fuyingedu.training.mapper.*;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.fuyingedu.training.common.util.DateUtils.DATE_FORMATTER_MD;

@Service
public class HomeExportService {

    @Autowired
    private ClazzMapper clazzMapper;
    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private OrderFxyMapper orderFxyMapper;
    @Autowired
    private FxyMapper fxyMapper;
    @Autowired
    private UserMapper userMapper;
    @Autowired
    private StudentMapper studentMapper;
    @Autowired
    private TaskMapper taskMapper;
    @Autowired
    private TaskRelationMapper taskRelationMapper;
    @Autowired
    private TaskSubmitRecordMapper taskSubmitRecordMapper;
    @Autowired
    private GroupMapper groupMapper;
    @Autowired
    private ScheduleMapper scheduleMapper;
    @Autowired
    private CampMapper campMapper;
    @Autowired
    private OrderService orderService;

    public void export(RecordListReq req, HttpServletResponse response) throws IOException {
        Clazz clazz = clazzMapper.selectOne(new QueryWrapper<Clazz>().select(
                Clazz.ID, Clazz.TEACHER_ID, Clazz.ASSISTANT_ID, Clazz.CLASS_NAME, Clazz.SCHEDULE_ID
        ).eq(Clazz.ID, req.getClazzId()));
        if (clazz == null) {
            throw new WebBaseException(4000, "班级不存在");
        }
        Schedule schedule = scheduleMapper.selectOne(new QueryWrapper<Schedule>().select(Schedule.CAMP_ID)
                .eq(Schedule.ID, req.getScheduleId()));
        Camp camp = campMapper.selectOne(new QueryWrapper<Camp>().select(Camp.CAMP_TYPE)
                .eq(Camp.ID, schedule.getCampId()));
        if (CampType.WORD.getCode().equals(camp.getCampType())) {
            req.setPageSize(5000);
            req.setPageNum(1);
            CommResp<List<RecordItemResp>> listCommResp = orderService.recordList(req);
            List<List<String>> headers = new ArrayList<>(Arrays.asList(
                    Arrays.asList("姓名", "姓名"),
                    Arrays.asList("扶小鹰账号", "扶小鹰账号"),
                    Arrays.asList("扶小鹰昵称", "扶小鹰昵称"),
                    Arrays.asList("手机号", "手机号"),
                    Arrays.asList("学员", "学员"),
                    Arrays.asList("新复训", "新复训"),
                    Arrays.asList("辅导老师", "辅导老师"),
                    Arrays.asList("大志愿者", "大志愿者"),
                    Arrays.asList("班级", "班级"),
                    Arrays.asList("分组", "分组"),
                    Arrays.asList("word鹰积分", "word鹰积分"),
                    Arrays.asList("学练测完成数", "学练测完成数"),
                    Arrays.asList("复习完成数", "复习完成数"),
                    Arrays.asList("直播完成数", "直播完成数"),
                    Arrays.asList("直播答题得分", "直播答题得分"),
                    Arrays.asList("奖状数量", "奖状数量"),
                    Arrays.asList("签到次数", "签到次数"),
                    Arrays.asList("打卡次数", "打卡次数"),
                    Arrays.asList("作业次数", "作业次数"),
                    Arrays.asList("作业点评次数", "作业点评次数")
                    ));
            List<RecordItemResp> data = listCommResp.getData();
            if (data == null || data.isEmpty()) {
                exportHomework(headers, Collections.emptyList(), response);
                return;
            }
            Set<Long> orderIdSet = data.stream().map(RecordItemResp::getId).collect(Collectors.toSet());
            Map<Long, Long> fxyRelationMap = orderFxyMapper.selectList(new QueryWrapper<OrderFxy>().select(
                    OrderFxy.ORDER_ID, OrderFxy.FXY_ID
            ).in(OrderFxy.ORDER_ID, orderIdSet)).stream().collect(Collectors.toMap(OrderFxy::getOrderId, OrderFxy::getFxyId));
            Map<Long, Fxy> fxyMap = Collections.emptyMap();
            if (!fxyRelationMap.isEmpty()) {
                fxyMap = fxyMapper.selectList(new QueryWrapper<Fxy>().select(
                        Fxy.ID, Fxy.ACCOUNT_INFO, Fxy.NICK_NAME
                ).in(Fxy.ID, fxyRelationMap.values())).stream().collect(Collectors.toMap(Fxy::getId, Function.identity()));
            }
            List<List<String>> dataList = new ArrayList<>();
            for (RecordItemResp item : data) {
                List<String> row = new ArrayList<>();
                row.add(item.getRealName());
                Long fxyId = fxyRelationMap.get(item.getId());
                if (fxyId != null) {
                    Fxy fxy = fxyMap.get(fxyId);
                    row.add(fxy.getAccountInfo());
                    row.add(fxy.getNickName());
                } else {
                    row.add("");
                    row.add("");
                }
                row.add(item.getPhoneNum().toString());
                row.add(item.getStudentName());
                if (StudentFlag.OLD.getCode().equals(item.getStudentFlag())) {
                    row.add("复训");
                } else {
                    row.add("新训");
                }
                row.add(item.getTeacherName());
                row.add(item.getAssistantName());
                row.add(item.getClazzName());
                row.add(item.getGroupName());
                row.add(item.getWordNum() == null ? "0" : item.getWordNum().toString());
                row.add(item.getStudentNum() == null ? "0" : item.getStudentNum().toString());
                row.add(item.getReviewNum() == null ? "0" : item.getReviewNum().toString());
                row.add(item.getLiveSignNum() == null ? "0" : item.getLiveSignNum().toString());
                row.add(item.getLiveAnswerNum() == null ? "0" : item.getLiveAnswerNum().toString());
                row.add(item.getMedalNum() == null ? "0" : item.getMedalNum().toString());
                row.add(item.getEnrollmentNum() == null ? "0" : item.getEnrollmentNum().toString());
                row.add(item.getPunchNum() == null ? "0" : item.getPunchNum().toString());
                row.add(item.getHomeworkNum() == null ? "0" : item.getHomeworkNum().toString());
                row.add(item.getRemarkedNum() == null ? "0" : item.getRemarkedNum().toString());
                dataList.add(row);
            }
            exportHomework(headers, dataList, response);
        } else {
            exportOther(clazz, req, response);
        }
    }

    private void exportOther(Clazz clazz, RecordListReq req, HttpServletResponse response) throws IOException {
        List<List<String>> headers = new ArrayList<>(Arrays.asList(Arrays.asList("姓名", "姓名"),
                Arrays.asList("扶小鹰账号", "扶小鹰账号"), Arrays.asList("扶小鹰昵称", "扶小鹰昵称"),
                Arrays.asList("手机号", "手机号"), Arrays.asList("订单号", "订单号"),
                Arrays.asList("班级", "班级"), Arrays.asList("小组", "小组"),
                Arrays.asList("作业总完成次数", "作业总完成次数")));
        List<Long> taskIds = taskRelationMapper.selectList(new QueryWrapper<TaskRelation>().select(TaskRelation.TASK_ID)
                .eq(TaskRelation.SCHEDULE_ID, clazz.getScheduleId())
                .in(TaskRelation.TEACHER_ID, -1, clazz.getTeacherId())
                .in(TaskRelation.ASSISTANT_ID, -1, clazz.getAssistantId())
                .in(TaskRelation.CLAZZ_ID, -1, clazz.getId())
        ).stream().map(TaskRelation::getTaskId).toList();
        if (taskIds.isEmpty()) {
            exportHomework(headers, Collections.emptyList(), response);
            return;
        }
        List<Task> taskList = taskMapper.selectList(new QueryWrapper<Task>().select(Task.ID, Task.START_DATE)
                .in(Task.ID, taskIds).eq(Task.TASK_TYPE, 3).orderByAsc(Task.ID));
        if (taskList.isEmpty()) {
            exportHomework(headers, Collections.emptyList(), response);
            return;
        }
        List<Long> userIds = null;
        if (req.getUserInfo() != null) {
            userIds = userMapper.selectList(new QueryWrapper<User>().select(User.ID)
                            .eq(User.PHONE_NUM, req.getUserInfo()).or().like(User.REAL_NAME, req.getUserInfo()))
                    .stream().map(User::getId).toList();
            if (userIds.isEmpty()) {
                exportHomework(headers, Collections.emptyList(), response);
                return;
            }
        }
        List<Long> studentIds = null;
        if (req.getStudentInfo() != null) {
            studentIds = studentMapper.selectList(new QueryWrapper<Student>().select(
                            Student.ID
                    ).eq(Student.CARD_NUM, req.getStudentInfo()).or().like(Student.REAL_NAME, req.getStudentInfo()))
                    .stream().map(Student::getId).toList();
            if (studentIds.isEmpty()) {
                exportHomework(headers, Collections.emptyList(), response);
                return;
            }
        }
        List<Order> orderList = orderMapper.selectList(new QueryWrapper<Order>()
                .select(Order.ID, Order.USER_ID, Order.GROUP_ID, Order.ORDER_NO)
                .eq(Order.CLAZZ_ID, req.getClazzId()).eq(Order.ORDER_STATUS, 1)
                .eq(req.getGroupId() != null, Order.GROUP_ID, req.getGroupId())
                .eq(req.getStudentType() != null, Order.STUDENT_TYPE, req.getStudentType())
                .in(userIds != null, Order.USER_ID, userIds)
                .in(studentIds != null, Order.STUDENT_ID, studentIds)
        );
        if (orderList.isEmpty()) {
            exportHomework(headers, Collections.emptyList(), response);
            return;
        }
        Set<Long> orderIdSet = orderList.stream().map(Order::getId).collect(Collectors.toSet());
        Map<Long, Set<Long>> taskSubmitMap = taskSubmitRecordMapper.selectList(new QueryWrapper<TaskSubmitRecord>().select(
                        TaskSubmitRecord.TASK_ID, TaskSubmitRecord.ORDER_ID
                ).in(TaskSubmitRecord.ORDER_ID, orderIdSet).in(TaskSubmitRecord.TASK_ID, taskList.stream().map(Task::getId).toList()))
                .stream().collect(Collectors.groupingBy(TaskSubmitRecord::getOrderId,
                        Collectors.mapping(TaskSubmitRecord::getTaskId, Collectors.toSet())));
        Set<Long> userIdSet = orderList.stream().map(Order::getUserId).collect(Collectors.toSet());
        Map<Long, User> userMap = userMapper.selectList(new QueryWrapper<User>().select(
                        User.ID, User.REAL_NAME, User.PHONE_NUM)
                .in(User.ID, userIdSet)).stream().collect(Collectors.toMap(User::getId, v -> v));
        Map<Long, String> groupMap = Collections.emptyMap();
        Set<Long> groupIds = orderList.stream().map(Order::getGroupId).filter(Objects::nonNull).collect(Collectors.toSet());
        if (!groupIds.isEmpty()) {
            groupMap = groupMapper.selectList(new QueryWrapper<Group>().select(Group.ID, Group.GROUP_NAME))
                    .stream().collect(Collectors.toMap(Group::getId, Group::getGroupName));
        }
        for (Task task : taskList) {
            headers.add(Arrays.asList("作业完成情况明细", DateUtils.format(task.getStartDate(), DATE_FORMATTER_MD)));
        }
        Map<Long, Long> fxyRelationMap = orderFxyMapper.selectList(new QueryWrapper<OrderFxy>().select(
                OrderFxy.ORDER_ID, OrderFxy.FXY_ID
        ).in(OrderFxy.ORDER_ID, orderIdSet)).stream().collect(Collectors.toMap(OrderFxy::getOrderId, OrderFxy::getFxyId));
        Map<Long, Fxy> fxyMap = Collections.emptyMap();
        if (!fxyRelationMap.isEmpty()) {
            fxyMap = fxyMapper.selectList(new QueryWrapper<Fxy>().select(
                    Fxy.ID, Fxy.ACCOUNT_INFO, Fxy.NICK_NAME
            ).in(Fxy.ID, fxyRelationMap.values())).stream().collect(Collectors.toMap(Fxy::getId, Function.identity()));
        }
        List<List<String>> dataList = new ArrayList<>();
        for (Order order : orderList) {
            List<String> data = new ArrayList<>();
            User user = userMap.get(order.getUserId());
            data.add(user.getRealName());
            Long fxyId = fxyRelationMap.get(order.getId());
            if (fxyId != null) {
                Fxy fxy = fxyMap.get(fxyId);
                data.add(fxy.getAccountInfo());
                data.add(fxy.getNickName());
            } else {
                data.add("");
                data.add("");
            }
            data.add(String.valueOf(user.getPhoneNum()));
            data.add(order.getOrderNo());
            data.add(clazz.getClassName());
            data.add(groupMap.getOrDefault(order.getGroupId(), "无"));
            int submitCount = 0;
            Set<Long> submitTaskIds = taskSubmitMap.get(order.getId());
            if (submitTaskIds != null) {
                submitCount = submitTaskIds.size();
            }
            data.add(String.valueOf(submitCount));
            for (Task task : taskList) {
                if (submitTaskIds != null && submitTaskIds.contains(task.getId())) {
                    data.add("已完成");
                } else {
                    data.add("未完成");
                }
            }
            dataList.add(data);
        }
        exportHomework(headers, dataList, response);
    }

    private void exportHomework(List<List<String>> headers, List<List<String>> dataList,
                                HttpServletResponse response) throws IOException {
        EasyExcel.write(response.getOutputStream())
                .sheet("作业数据")
                .registerWriteHandler(new SimpleColumnWidthStyleStrategy(30))
                .head(headers)
                .doWrite(dataList);
    }
}
