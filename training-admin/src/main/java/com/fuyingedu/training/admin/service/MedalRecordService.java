package com.fuyingedu.training.admin.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fuyingedu.training.admin.model.medal.RecordItemResp;
import com.fuyingedu.training.admin.model.medal.RecordReq;
import com.fuyingedu.training.common.model.CommResp;
import com.fuyingedu.training.common.util.RespUtils;
import com.fuyingedu.training.entity.*;
import com.fuyingedu.training.mapper.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class MedalRecordService {

    @Autowired
    private MedalRecordMapper medalRecordMapper;
    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private ClazzMapper clazzMapper;
    @Autowired
    private GroupMapper groupMapper;
    @Autowired
    private OrderMedalMapper orderMedalMapper;
    @Autowired
    private StudentMapper studentMapper;
    @Autowired
    private UserMapper userMapper;

    public CommResp<List<RecordItemResp>> recordList(RecordReq recordReq) {
        List<MedalRecord> medalRecordList = medalRecordMapper.selectList(new QueryWrapper<MedalRecord>().select(
                MedalRecord.MEDAL_NAME, MedalRecord.MEDAL_ICON, MedalRecord.ID, MedalRecord.CREATED_TIME
        ).eq(MedalRecord.SCHEDULE_ID, recordReq.getScheduleId()).orderByDesc(MedalRecord.ID));
        if (medalRecordList.isEmpty()) {
            return RespUtils.success(Collections.emptyList());
        }
        List<Long> recordIds = medalRecordList.stream().map(MedalRecord::getId).toList();
        List<OrderMedal> orderMedals = orderMedalMapper.selectList(new QueryWrapper<OrderMedal>().select(
                OrderMedal.ID, OrderMedal.ORDER_ID, OrderMedal.MEDAL_ID
        ).in(OrderMedal.MEDAL_ID, recordIds));
        List<Long> orderIds = orderMedals.stream().map(OrderMedal::getOrderId).distinct().toList();
        List<Order> orderList = orderMapper.selectList(new QueryWrapper<Order>().select(
                Order.GROUP_ID, Order.CLAZZ_ID, Order.STUDENT_ID, Order.ID, Order.USER_ID
        ).in(Order.ID, orderIds));
        List<Long> studentIds = orderList.stream().map(Order::getStudentId).filter(Objects::nonNull).distinct().toList();
        Map<Long, Student> studentMap = Collections.emptyMap();
        if (!studentIds.isEmpty()) {
            studentMap = studentMapper.selectList(new QueryWrapper<Student>().select(
                    Student.ID, Student.REAL_NAME
            ).in(Student.ID, studentIds)).stream().collect(Collectors.toMap(Student::getId, Function.identity()));
        }
        List<Long> clazzIds = orderList.stream().map(Order::getClazzId).filter(Objects::nonNull).distinct().toList();
        Map<Long, Clazz> clazzMap = Collections.emptyMap();
        if (!clazzIds.isEmpty()) {
            clazzMap = clazzMapper.selectList(new QueryWrapper<Clazz>().select(
                            Clazz.ID, Clazz.CLASS_NAME
                    ).in(Clazz.ID, clazzIds))
                    .stream().collect(Collectors.toMap(Clazz::getId, Function.identity()));
        }
        List<Long> groupIds = orderList.stream().map(Order::getGroupId).filter(Objects::nonNull).distinct().toList();
        Map<Long, Group> groupMap = Collections.emptyMap();
        if (!groupIds.isEmpty()) {
            groupMap = groupMapper.selectList(new QueryWrapper<Group>().select(
                            Group.ID, Group.GROUP_NAME
                    ).in(Group.ID, groupIds))
                    .stream().collect(Collectors.toMap(Group::getId, Function.identity()));
        }
        Map<Long, User> userMap = userMapper.selectList(new QueryWrapper<User>().select(
                User.ID, User.REAL_NAME
        ).in(User.ID, orderList.stream().map(Order::getUserId).toList())).stream().collect(Collectors.toMap(User::getId, Function.identity()));
        Map<Long, List<OrderMedal>> orderMedalMap = orderMedals.stream().collect(Collectors.groupingBy(OrderMedal::getMedalId));
        Map<Long, Order> orderMap = orderList.stream().collect(Collectors.toMap(Order::getId, Function.identity()));
        List<RecordItemResp> respList = new ArrayList<>(medalRecordList.size());
        for (MedalRecord record : medalRecordList) {
            RecordItemResp recordItemResp = toRecordItemResp(record);
            List<OrderMedal> orderMedalList = orderMedalMap.get(record.getId());
            if (!CollectionUtils.isEmpty(orderMedalList)) {
                List<RecordItemResp.Student> studentList = new ArrayList<>(orderMedalList.size());
                for (OrderMedal orderMedal : orderMedalList) {
                    Order order = orderMap.get(orderMedal.getOrderId());
                    RecordItemResp.Student student = new RecordItemResp.Student();
                    if (order.getStudentId() != null) {
                        student.setRealName(studentMap.get(order.getStudentId()).getRealName());
                    } else {
                        student.setRealName(userMap.get(order.getUserId()).getRealName());
                    }
                    if (order.getClazzId() != null) {
                        Clazz clazz = clazzMap.get(order.getClazzId());
                        student.setClazzId(clazz.getId());
                        student.setClazzName(clazz.getClassName());
                    }
                    if (order.getGroupId() != null) {
                        Group group = groupMap.get(order.getGroupId());
                        student.setGroupId(group.getId());
                        student.setGroupName(group.getGroupName());
                    }
                    studentList.add(student);
                }
                recordItemResp.setStudentList(studentList);
            }
            respList.add(recordItemResp);
        }
        return RespUtils.success(respList);
    }

    private RecordItemResp toRecordItemResp(MedalRecord record) {
        RecordItemResp recordItemResp = new RecordItemResp();
        recordItemResp.setId(record.getId());
        recordItemResp.setCreatedTime(record.getCreatedTime());
        recordItemResp.setMedalName(record.getMedalName());
        recordItemResp.setTemplateName(record.getTemplateName());
        recordItemResp.setMedalIcon(record.getMedalIcon());
        return recordItemResp;
    }
}
