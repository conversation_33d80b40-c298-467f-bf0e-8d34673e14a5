package com.fuyingedu.training.admin.model.schedule;

import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;
import java.time.LocalTime;

@Getter
@Setter
@ToString
public class Detail {
    private Long id;

    private String relationName;
    /**
     * 排期名称
     */
    @NotNull
    private String scheduleName;

    /**
     * 排期开始时间
     */
    @NotNull
    private LocalDateTime startTime;

    /**
     * 排期结束时间
     */
    @NotNull
    private LocalDateTime endTime;

    /**
     * 直播方式 1-公司直播 2-导师直播
     */
    private Byte liveType;

    /**
     * 直播端口 1-腾讯会议 2-保利威 3-百家云
     */
    private Byte livePort;

    /**
     * 直播房间号
     */
    private Long liveRoom;

    /**
     * 直播开始时间
     */
    private LocalTime liveTime;
    /**
     * 外部的班级ID
     */
    private Long clazzId;
    /**
     * 外部班级名称
     */
    private String clazzName;
    /**
     * 外部排期ID
     */
    @NotNull
    private Long scheduleId;
}
