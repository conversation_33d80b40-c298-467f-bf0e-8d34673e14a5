package com.fuyingedu.training.admin.model.order;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.List;

@Getter
@Setter
@ToString
public class DetailResp {

    /**
     * 导师备注
     */
    private List<Remark> remarkList;
    /**
     * 用户名
     */
    private String realName;

    /**
     * 手机号
     */
    private Long phoneNum;

    /**
     * 训练营名称
     */
    private String campName;

    /**
     * 1-正常 2-已关闭 3-已退款
     */
    private Byte orderStatus;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;
    /**
     * 排期名称
     */
    private String scheduleName;

    /**
     * 1-未开始 2-进行中 3-已结束
     */
    private Byte scheduleStatus;

    /**
     * 辅导老师
     */
    private String teacherName;

    /**
     * 辅导老师手机号
     */
    private Long teacherPhoneNum;

    /**
     * 助教
     */
    private String assistantName;

    /**
     * 助教手机号
     */
    private Long assistantPhoneNum;

    /**
     * 班级名称
     */
    private String className;

    /**
     * 小组名称
     */
    private String groupName;

    /**
     * 1-普通学员 2-陪跑志愿者
     */
    private Byte studentType;
    /**
     * 学员名称
     */
    private String studentName;

    /**
     * 证件号
     */
    private String cardNum;

    /**
     * 扶小鹰名称
     */
    private String fxyName;
    /**
     * 扶小鹰openid
     */
    private String fxyOpenId;

    /**
     * 扶小鹰账号
     */
    private String fxyAccount;
}
