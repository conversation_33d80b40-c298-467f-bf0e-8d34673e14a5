package com.fuyingedu.training.admin.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.fuyingedu.training.admin.model.clazz.*;
import com.fuyingedu.training.admin.model.schedule.MedalItemResp;
import com.fuyingedu.training.common.enums.*;
import com.fuyingedu.training.common.exception.WebBaseException;
import com.fuyingedu.training.common.model.CommResp;
import com.fuyingedu.training.common.util.AsyncUtils;
import com.fuyingedu.training.common.util.RedisKey;
import com.fuyingedu.training.common.util.RespUtils;
import com.fuyingedu.training.dto.clazz.OrderNumRet;
import com.fuyingedu.training.dto.task.RelationTaskRet;
import com.fuyingedu.training.entity.*;
import com.fuyingedu.training.front.manager.BaiJiaYunManager;
import com.fuyingedu.training.front.manager.CheckAuthManager;
import com.fuyingedu.training.front.manager.OperationManager;
import com.fuyingedu.training.front.model.media.MediaConvertor;
import com.fuyingedu.training.mapper.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Predicate;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ClazzService {

    @Autowired
    private CheckAuthManager checkAuthManager;
    @Autowired
    private GroupMapper groupMapper;
    @Autowired
    private UserMapper userMapper;
    @Autowired
    private TeacherMapper teacherMapper;
    @Autowired
    private ScheduleTeacherMapper scheduleTeacherMapper;
    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private MedalMapper medalMapper;
    @Autowired
    private MedalRelationMapper medalRelationMapper;
    @Autowired
    private ScheduleMapper scheduleMapper;
    @Autowired
    private ClazzMapper clazzMapper;
    @Autowired
    private RedisTemplate<String, String> redisTemplate;
    @Autowired
    private TaskRelationMapper taskRelationMapper;
    @Autowired
    private UserWxGroupMapper userWxGroupMapper;
    @Autowired
    private ClazzRoomMapper clazzRoomMapper;
    @Autowired
    private BaiJiaYunManager baiJiaYunManager;
    @Autowired
    private AvailableCampMapper availableCampMapper;
    @Autowired
    private OperationManager operationManager;

    public CommResp<List<ItemResp>> list(Long userId, Long scheduleId) {
        Teacher teacher = teacherMapper.selectOne(new QueryWrapper<Teacher>().select(
                Teacher.ID, Teacher.REAL_NAME
        ).eq(Teacher.USER_ID, userId));
        if (teacher == null) {
            return RespUtils.warning(4000, "该用户不是老师");
        }
        ScheduleTeacher scheduleTeacher = scheduleTeacherMapper.selectOne(new QueryWrapper<ScheduleTeacher>().select(
                ScheduleTeacher.TEACHER_ID, ScheduleTeacher.TEACHER_TYPE
        ).eq(ScheduleTeacher.SCHEDULE_ID, scheduleId).eq(ScheduleTeacher.TEACHER_ID, teacher.getId()));
        if (scheduleTeacher == null) {
            return RespUtils.warning(4000, "该用户不是本排期的老师");
        }
        Map<Long, Teacher> assistantMap;
        List<Clazz> clazzList;
        if (TeacherType.TEACHER.getCode().equals(scheduleTeacher.getTeacherType())) {
            clazzList = clazzMapper.selectList(new QueryWrapper<Clazz>().select(
                    Clazz.ID, Clazz.ASSISTANT_ID, Clazz.CLASS_NAME, Clazz.WX_URL, Clazz.WX_GROUP_ID
            ).eq(Clazz.SCHEDULE_ID, scheduleId).eq(Clazz.TEACHER_ID, teacher.getId()).orderByDesc(Clazz.ID));
            if (CollectionUtils.isEmpty(clazzList)) {
                return RespUtils.success(Collections.emptyList());
            }
            List<Long> teacherIds = clazzList.stream().map(Clazz::getAssistantId).toList();
            assistantMap = teacherMapper.selectList(new QueryWrapper<Teacher>().select(
                            Teacher.ID, Teacher.REAL_NAME
                    ).in(Teacher.ID, teacherIds))
                    .stream().collect(Collectors.toMap(Teacher::getId, v -> v));
        } else {
            clazzList = clazzMapper.selectList(new QueryWrapper<Clazz>().select(
                    Clazz.ID, Clazz.ASSISTANT_ID, Clazz.CLASS_NAME, Clazz.WX_URL, Clazz.WX_GROUP_ID
            ).eq(Clazz.SCHEDULE_ID, scheduleId).eq(Clazz.ASSISTANT_ID, teacher.getId()).orderByDesc(Clazz.ID));
            assistantMap = Map.of(teacher.getId(), teacher);
        }
        if (clazzList.isEmpty()) {
            return RespUtils.success(Collections.emptyList());
        }
        List<Long> clazzIds = clazzList.stream().map(Clazz::getId).toList();
        List<Group> groupList = groupMapper.selectList(new QueryWrapper<Group>().select(
                Group.CLAZZ_ID, Group.MONITOR_ID
        ).in(Group.CLAZZ_ID, clazzIds));
        Map<Long, List<Group>> groupMap = groupList.stream().collect(Collectors.groupingBy(Group::getClazzId));
        List<Long> orderIds = groupList.stream().map(Group::getMonitorId).filter(Objects::nonNull).toList();
        Map<Long, String> userNameMap = Collections.emptyMap();
        if (!orderIds.isEmpty()) {
            List<Order> orderList = orderMapper.selectList(new QueryWrapper<Order>().select(
                    Order.ID, Order.USER_ID
            ).in(Order.ID, orderIds));
            Map<Long, User> userMap = userMapper.selectList(new QueryWrapper<User>().select(
                    User.ID, User.REAL_NAME
            ).in(User.ID, orderList.stream().map(Order::getUserId).toList())).stream().collect(Collectors.toMap(User::getId, user -> user));
            userNameMap = orderList.stream().collect(Collectors.toMap(Order::getId, order -> userMap.get(order.getUserId()).getRealName()));
        }
        Set<Long> wxGroupIds = clazzList.stream().map(Clazz::getWxGroupId).filter(Objects::nonNull).collect(Collectors.toSet());
        Map<Long, String> userWxGroupMap = Collections.emptyMap();
        if (!wxGroupIds.isEmpty()) {
            userWxGroupMap = userWxGroupMapper.selectList(new QueryWrapper<UserWxGroup>().select(
                    UserWxGroup.ID, UserWxGroup.GROUP_NAME
            ).in(UserWxGroup.ID, wxGroupIds)).stream().collect(Collectors.toMap(UserWxGroup::getId, UserWxGroup::getGroupName));
        }
        // 获取PK中未结束的班级
        Map<Long, Map<Byte, Integer>> orderNumMap = orderNumMap(clazzIds);
        List<ItemResp> respList = new ArrayList<>(clazzList.size());
        for (Clazz clazz : clazzList) {
            groupList = groupMap.getOrDefault(clazz.getId(), Collections.emptyList());
            List<String> monitorNameList = new ArrayList<>(groupList.size());
            for (Group group : groupList) {
                if (group.getMonitorId() != null) {
                    monitorNameList.add(userNameMap.get(group.getMonitorId()));
                }
            }
            Teacher assistant = assistantMap.get(clazz.getAssistantId());
            ItemResp itemResp = toItemResp(clazz);
            int studentNum = orderNumMap.getOrDefault(clazz.getId(), Collections.emptyMap())
                    .values().stream().mapToInt(v -> v).sum();
            itemResp.setStudentNum(studentNum);
            itemResp.setMonitorList(monitorNameList);
            itemResp.setTeacherName(assistant.getRealName());
            itemResp.setGroupNum(groupList.size());
            itemResp.setWxUrl(MediaConvertor.getMediaUrl(clazz.getWxUrl()));
            if (clazz.getWxGroupId() != null) {
                itemResp.setGroupId(clazz.getWxGroupId());
                itemResp.setGroupName(userWxGroupMap.get(clazz.getWxGroupId()));
            }
            respList.add(itemResp);
        }
        return RespUtils.success(respList);
    }

    private ItemResp toItemResp(Clazz clazz) {
        ItemResp itemResp = new ItemResp();
        itemResp.setId(clazz.getId());
        itemResp.setClassName(clazz.getClassName());
        itemResp.setTeacherId(clazz.getAssistantId());
        itemResp.setWxUrl(MediaConvertor.getMediaUrl(clazz.getWxUrl()));
        return itemResp;
    }

    @Transactional(rollbackFor = Exception.class)
    public void add(Long userId, SaveReq saveReq) {
        Teacher teacher = teacherMapper.selectOne(new QueryWrapper<Teacher>().select(
                Teacher.ID
        ).eq(Teacher.USER_ID, userId).eq(Teacher.TEACHER_STATUS, Status.NORMAL.getCode()));
        checkAuthManager.checkTeacher(teacher, saveReq.getScheduleId());
        List<ScheduleTeacher> scheduleTeacherList = scheduleTeacherMapper.selectList(new QueryWrapper<ScheduleTeacher>().select(
                        ScheduleTeacher.ID
                ).eq(ScheduleTeacher.SCHEDULE_ID, saveReq.getScheduleId()).eq(ScheduleTeacher.TEACHER_TYPE, TeacherType.ASSISTANT.getCode())
                .in(ScheduleTeacher.TEACHER_ID, saveReq.getTeacherIdList()));
        if (scheduleTeacherList.size() != saveReq.getTeacherIdList().size()) {
            throw new WebBaseException(RespMetaEnum.PARAM_ERROR);
        }
        String hashKey = saveReq.getScheduleId() + ":" + userId;
        long clazzSeq = redisTemplate.opsForHash().increment(RedisKey.CLAZZ_SEQ, hashKey, saveReq.getClazzNum()) - saveReq.getClazzNum() + 1;
        saveReq.setTeacherIdList(saveReq.getTeacherIdList().stream().distinct().toList());
        List<Clazz> clazzList = getClazzList(saveReq, teacher.getId(), clazzSeq);
        clazzMapper.insert(clazzList);
        Schedule schedule = scheduleMapper.selectOne(new QueryWrapper<Schedule>().select(
                Schedule.ID, Schedule.LIVE_PORT, Schedule.LIVE_TYPE, Schedule.LIVE_ROOM
        ).eq(Schedule.ID, saveReq.getScheduleId()));
        if (!clazzList.isEmpty() && Byte.valueOf((byte) 1).equals(schedule.getLiveType())
                && Byte.valueOf((byte) 3).equals(schedule.getLivePort())) {
            List<ClazzRoom> clazzRoomList = clazzList.stream().map(clazz -> {
                ClazzRoom clazzRoom = new ClazzRoom();
                clazzRoom.setScheduleId(schedule.getId());
                clazzRoom.setClazzId(clazz.getId());
                return clazzRoom;
            }).toList();
            clazzRoomMapper.insert(clazzRoomList);
        }
        Set<Long> assistantIdList = clazzList.stream().map(Clazz::getAssistantId).collect(Collectors.toSet());
        if (!assistantIdList.isEmpty()) {
            Map<Long, List<RelationTaskRet>> assistantMap = taskRelationMapper.groupTaskId(saveReq.getScheduleId(), assistantIdList)
                    .stream().collect(Collectors.groupingBy(RelationTaskRet::getAssistantId));
            List<TaskRelation> taskRelationList = new ArrayList<>();
            for (Clazz clazz : clazzList) {
                List<RelationTaskRet> relationTaskRets = assistantMap.get(clazz.getAssistantId());
                if (!CollectionUtils.isEmpty(relationTaskRets)) {
                    Set<Long> teacherIds = relationTaskRets.stream().map(RelationTaskRet::getTeacherId).collect(Collectors.toSet());
                    if (teacherIds.contains(clazz.getTeacherId())) {
                        continue;
                    }
                    for (RelationTaskRet relationTaskRet : relationTaskRets) {
                        TaskRelation taskRelation = new TaskRelation();
                        taskRelation.setScheduleId(saveReq.getScheduleId());
                        taskRelation.setTeacherId(clazz.getTeacherId());
                        taskRelation.setAssistantId(relationTaskRet.getAssistantId());
                        taskRelation.setTaskId(relationTaskRet.getTaskId());
                        taskRelation.setClazzId(-1L);
                        taskRelationList.add(taskRelation);
                    }
                }
            }
            if (!taskRelationList.isEmpty()) {
                taskRelationMapper.insert(taskRelationList);
            }
        }
        AsyncUtils.execute(() -> baiJiaYunManager.createGroup(), "百家云创建分组");
    }

    private static List<Clazz> getClazzList(SaveReq saveReq, Long teacherId, long clazzSeq) {
        List<Clazz> clazzList = new ArrayList<>(saveReq.getClazzNum());
        for (int i = 0; i < saveReq.getClazzNum(); i++) {
            Long assistantId = saveReq.getTeacherIdList().get(i % saveReq.getTeacherIdList().size());
            Clazz clazz = new Clazz();
            clazz.setScheduleId(saveReq.getScheduleId());
            clazz.setTeacherId(teacherId);
            clazz.setAssistantId(assistantId);
            clazz.setClassName((clazzSeq + i) + "班");
            clazzList.add(clazz);
        }
        return clazzList;
    }

    public void update(Long userId, UpdateReq updateReq) {
        Clazz clazz = clazzMapper.selectOne(new QueryWrapper<Clazz>().select(
                Clazz.ID, Clazz.TEACHER_ID, Clazz.SCHEDULE_ID
        ).eq(Clazz.ID, updateReq.getId()));
        if (clazz == null) {
            throw new WebBaseException(RespMetaEnum.PARAM_ERROR);
        }
        checkAuthManager.checkTeacher(userId, clazz.getScheduleId());
        clazzMapper.update(new UpdateWrapper<Clazz>()
                .set(StringUtils.hasLength(updateReq.getClazzName()), Clazz.CLASS_NAME, updateReq.getClazzName())
                .set(Objects.nonNull(updateReq.getTeacherId()), Clazz.ASSISTANT_ID, updateReq.getTeacherId())
                .set(StringUtils.hasLength(updateReq.getWxUrl()), Clazz.WX_URL, MediaConvertor.getUrlSuffix(updateReq.getWxUrl()))
                .set(updateReq.getGroupId() != null, Clazz.WX_GROUP_ID, updateReq.getGroupId())
                .eq(Clazz.ID, updateReq.getId()));
    }

    @Transactional(rollbackFor = Exception.class)
    public CommResp<List<AllocItemResp>> autoAlloc(Long userId, AllocReq allocReq) {
        Teacher teacher = teacherMapper.selectOne(new QueryWrapper<Teacher>().select(
                Teacher.ID
        ).eq(Teacher.USER_ID, userId).eq(Teacher.TEACHER_STATUS, Status.NORMAL.getCode()));
        checkAuthManager.checkTeacher(teacher, allocReq.getScheduleId());

        List<Clazz> clazzList = clazzMapper.selectList(new QueryWrapper<Clazz>().select(
                Clazz.ID, Clazz.CLASS_NAME
        ).eq(Clazz.SCHEDULE_ID, allocReq.getScheduleId()).eq(Clazz.TEACHER_ID, teacher.getId()));
        if (CollectionUtils.isEmpty(clazzList)) {
            throw new WebBaseException(RespMetaEnum.NO_CLAZZ);
        }
        List<Order> orderList = orderMapper.selectList(new QueryWrapper<Order>().select(
                    Order.ID, Order.SCHEDULE_ID, Order.TEACHER_ID, Order.STUDENT_FLAG, Order.CLAZZ_ID
            ).eq(Order.SCHEDULE_ID, allocReq.getScheduleId()).eq(Order.TEACHER_ID, teacher.getId())
            .in(Order.ORDER_STATUS, OrderStatus.NORMAL.getCode(), OrderStatus.MAY_REFUND.getCode())
        );
        if (CollectionUtils.isEmpty(orderList)) {
            throw new WebBaseException(4000, "没有学员，无法分班，请联系管理人员分配学员");
        }
        Boolean b = redisTemplate.opsForValue().setIfAbsent(String.format(RedisKey.ALLOC_ASSISTANT_LOCK, teacher.getId()),
                "", 10, TimeUnit.MINUTES);
        if (Boolean.FALSE.equals(b)) {
            throw new WebBaseException(4000, "正在分配，请勿重复点击");
        }
        try {
            return RespUtils.success(autoAlloc(userId, allocReq, clazzList, orderList));
        } finally {
            redisTemplate.delete(String.format(RedisKey.ALLOC_ASSISTANT_LOCK, teacher.getId()));
        }
    }

    private List<AllocItemResp> autoAlloc(Long userId, AllocReq allocReq, List<Clazz> clazzList, List<Order> orderList) {
        List<Order> needAllocOrderList = orderList.stream().filter(order -> {
            if (AllocType.ALL.getCode().equals(allocReq.getAllocType())) {
                return true;
            }
            return order.getClazzId() == null;
        }).toList();
        Map<Long, Map<Byte, List<Order>>> clazzOrderMap = orderList.stream().filter(order -> {
                    if (AllocType.NOT_ALLOC.getCode().equals(allocReq.getAllocType())) {
                        return order.getClazzId() != null;
                    }
                    return false;
                })
                .collect(Collectors.groupingBy(Order::getClazzId, Collectors.groupingBy(Order::getStudentFlag)));
        Map<Long, Map<Byte, Long>> oldOrderNumMap = orderList.stream().filter(order -> order.getClazzId() != null)
                .collect(Collectors.groupingBy(Order::getClazzId, Collectors.groupingBy(Order::getStudentFlag, Collectors.counting())));
        long gradeStudentNum = orderList.size();
        long gradeNewNum = orderList.stream().filter(order -> order.getStudentFlag().equals(StudentFlag.NEW.getCode())).count();
        long gradeOldNum = gradeStudentNum - gradeNewNum;
        long clazzNum = clazzList.size();
        long maxNum = gradeStudentNum / clazzNum, maxNewNum = gradeNewNum / clazzNum, maxOldNum = gradeOldNum / clazzNum;
        List<Order> newOrderList = needAllocOrderList.stream().filter(order -> order.getStudentFlag().equals(StudentFlag.NEW.getCode())).toList();
        List<Order> oldOrderList = needAllocOrderList.stream().filter(order -> order.getStudentFlag().equals(StudentFlag.OLD.getCode())).toList();

        List<Order> notAllocOrderList = alloc(clazzOrderMap, newOrderList, clazzList, maxNum,
                clazz -> {
                    int newNum = clazzOrderMap.getOrDefault(clazz.getId(), Collections.emptyMap())
                            .getOrDefault(StudentFlag.NEW.getCode(), Collections.emptyList()).size();
                    return newNum < maxNewNum;
                });
        notAllocOrderList.addAll(alloc(clazzOrderMap, oldOrderList, clazzList, maxNum,
                clazz -> {
                    int oldNum = clazzOrderMap.getOrDefault(clazz.getId(), Collections.emptyMap())
                            .getOrDefault(StudentFlag.OLD.getCode(), Collections.emptyList()).size();
                    return oldNum < maxOldNum;
                }));
        notAllocOrderList = alloc(clazzOrderMap, notAllocOrderList, clazzList, maxNum,
                clazz -> true);
        int clazzIdx = 0;
        for (Order order : notAllocOrderList) {
            Clazz clazz = clazzList.get(clazzIdx++);
            alloc(clazzOrderMap, order, clazz);
        }
        List<AllocItemResp> respList = new ArrayList<>(clazzList.size());
        for (Clazz clazz : clazzList) {
            Map<Byte, List<Order>> orderMap = clazzOrderMap.getOrDefault(clazz.getId(), Collections.emptyMap());
            List<Order> oldList = orderMap.getOrDefault(StudentFlag.OLD.getCode(), Collections.emptyList());
            List<Order> newList = orderMap.getOrDefault(StudentFlag.NEW.getCode(), Collections.emptyList());
            Map<Byte, Long> orderNumMap = oldOrderNumMap.getOrDefault(clazz.getId(), Collections.emptyMap());
            int oldNum = orderNumMap.getOrDefault(StudentFlag.OLD.getCode(), 0L).intValue();
            int newNum = orderNumMap.getOrDefault(StudentFlag.NEW.getCode(), 0L).intValue();
            AllocItemResp resp = new AllocItemResp();
            resp.setClazzId(clazz.getId());
            resp.setClazzName(clazz.getClassName());
            resp.setStudentNum(oldList.size() + newList.size());
            resp.setAllocNum(oldNum + newNum);
            resp.setUnAllocNum(oldList.size() + newList.size() - oldNum - newNum);
            resp.setNewNum(newList.size() - newNum);
            resp.setOldNum(oldList.size() - oldNum);
            respList.add(resp);
        }
        if (AllocSave.SAVE.getCode().equals(allocReq.getAllocSave())) {
            List<Order> updateOrderList = new ArrayList<>();
            for (Map.Entry<Long, Map<Byte, List<Order>>> entry : clazzOrderMap.entrySet()) {
                entry.getValue().values().forEach(list -> list.forEach(
                        order -> {
                            Order updateOrder = new Order();
                            updateOrder.setId(order.getId());
                            updateOrder.setClazzId(entry.getKey());
                            updateOrderList.add(updateOrder);
                        }
                ));
            }
            if (!updateOrderList.isEmpty()) {
                orderMapper.updateById(updateOrderList);
                operationManager.allocAssistant(userId, updateOrderList, orderList);
            }
        }
        return respList;
    }

    private List<Order> alloc(Map<Long, Map<Byte, List<Order>>> clazzOrderMap,
                              List<Order> orderList, List<Clazz> clazzList, long maxNum,
                              Predicate<Clazz> predicate) {
        List<Order> notAllocOrderList = new ArrayList<>();
        int clazzIdx = 0;
        for (Order order : orderList) {
            while (clazzIdx < clazzList.size()) {
                Clazz clazz = clazzList.get(clazzIdx);
                long studentNum = clazzOrderMap.getOrDefault(clazz.getId(), Collections.emptyMap())
                        .values().stream().mapToInt(List::size).sum();
                if (studentNum < maxNum && predicate.test(clazz)) {
                    alloc(clazzOrderMap, order, clazz);
                    break;
                } else {
                    clazzIdx++;
                }
            }
            if (clazzIdx >= clazzList.size()) {
                notAllocOrderList.add(order);
            }
        }
        return notAllocOrderList;
    }

    private void alloc(Map<Long, Map<Byte, List<Order>>> clazzOrderMap, Order order, Clazz clazz) {
        Map<Byte, List<Order>> orderFlagMap = clazzOrderMap.computeIfAbsent(clazz.getId(), k -> new HashMap<>());
        List<Order> allocOrderList = orderFlagMap.computeIfAbsent(order.getStudentFlag(), k -> new ArrayList<>());
        allocOrderList.add(order);
    }

    public CommResp<List<MedalItemResp>> medalList(Long clazzId) {
        Clazz clazz = clazzMapper.selectOne(new QueryWrapper<Clazz>().select(
                Clazz.ID, Clazz.SCHEDULE_ID
        ).eq(Clazz.ID, clazzId));
        Set<Long> teacherMedalIds = medalRelationMapper.selectList(new QueryWrapper<MedalRelation>().select(
                        MedalRelation.MEDAL_ID
                ).eq(MedalRelation.SCHEDULE_ID, clazz.getScheduleId()).in(MedalRelation.CLAZZ_ID, -1, clazzId))
                .stream().map(MedalRelation::getMedalId).collect(Collectors.toSet());

        Schedule schedule = scheduleMapper.selectOne(new QueryWrapper<Schedule>().select(
                Schedule.SCHEDULE_ID, Schedule.CAMP_ID
        ).eq(Schedule.ID, clazz.getScheduleId()));
        Set<Long> medalIds = availableCampMapper.selectList(new QueryWrapper<AvailableCamp>().select(
                AvailableCamp.AVAILABLE_ID
        ).eq(AvailableCamp.CAMP_ID, schedule.getCampId())).stream().map(AvailableCamp::getAvailableId).collect(Collectors.toSet());

        List<Medal> medalList = medalMapper.selectList(new QueryWrapper<Medal>().select(
                        Medal.ID, Medal.MEDAL_NAME, Medal.MEDAL_ICON, Medal.TEMPLATE_NAME, Medal.MEDAL_CONTENT
                ).eq(Medal.MEDAL_TYPE, 1)
                .or(!medalIds.isEmpty(), wq -> wq.in(Medal.ID, medalIds).eq(Medal.MEDAL_TYPE, 2))
                .or(!teacherMedalIds.isEmpty(), wq -> wq.in(Medal.ID, teacherMedalIds).eq(Medal.MEDAL_TYPE, 3)));
        return RespUtils.success(medalList.stream().map(medal -> {
            MedalItemResp medalItemResp = new MedalItemResp();
            medalItemResp.setId(medal.getId());
            medalItemResp.setMedalName(medal.getMedalName());
            medalItemResp.setTemplateName(medal.getTemplateName());
            medalItemResp.setMedalIcon(medal.getMedalIcon());
            medalItemResp.setMedalContent(medal.getMedalContent());
            return medalItemResp;
        }).toList());
    }

    public CommResp<List<ManageItemResp>> manageList(Long userId, Long scheduleId) {
        Teacher teacher = teacherMapper.selectOne(new QueryWrapper<Teacher>().select(
                Teacher.ID
        ).eq(Teacher.USER_ID, userId));
        if (teacher == null) {
            return RespUtils.success(Collections.emptyList());
        }
        List<Clazz> clazzList = clazzMapper.selectList(new QueryWrapper<Clazz>().select(
                Clazz.ID,
                Clazz.CLASS_NAME
        ).eq(Clazz.SCHEDULE_ID, scheduleId).eq(Clazz.TEACHER_ID, teacher.getId()));
        if (clazzList.isEmpty()) {
            clazzList = clazzMapper.selectList(new QueryWrapper<Clazz>().select(
                    Clazz.ID,
                    Clazz.CLASS_NAME
            ).eq(Clazz.SCHEDULE_ID, scheduleId).eq(Clazz.ASSISTANT_ID, teacher.getId()));
        }
        return RespUtils.success(clazzList.stream().map(clazz -> {
            ManageItemResp resp = new ManageItemResp();
            resp.setId(clazz.getId());
            resp.setClassName(clazz.getClassName());
            return resp;
        }).toList());
    }

    public Map<Long, Map<Byte, Integer>> orderNumMap(List<Long> clazzIds) {
        if (CollectionUtils.isEmpty(clazzIds)) {
            return Collections.emptyMap();
        }
        List<OrderNumRet> orderNumRets = clazzMapper.groupOrderNum(clazzIds);
        return orderNumRets.stream().collect(Collectors.groupingBy(OrderNumRet::getClazzId,
                Collectors.groupingBy(OrderNumRet::getStudentFlag, Collectors.summingInt(OrderNumRet::getStudentNum))));
    }
}
