package com.fuyingedu.training.admin.model.order;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class SearchItemResp {
    private Long id;

    /**
     * 服务单号
     */
    private String orderNo;
    /**
     * 用户名
     */
    private String realName;
    /**
     * 用户手机号
     */
    private Long phoneNum;
    /**
     * 学员名称
     */
    private String studentName;
    /**
     * 学员证件号
     */
    private String cartNo;
    /**
     * 1-新训 2-复训
     */
    private Byte studentFlag;
    /**
     * 导师
     */
    private String teacherName;
    /**
     * 大班表主键
     */
    private Long gradeId;

    /**
     * 小班表主键
     */
    private Long clazzId;
    /**
     * 小组表主键
     */
    private Long groupId;
    /**
     * 助教
     */
    private String assistantName;
    /**
     * 班级
     */
    private String clazzName;
    /**
     * 组名
     */
    private String groupName;

    /**
     * 组内身份 1-学员 2-陪跑
     */
    private Byte studentType;

    /**
     * 训练营名称
     */
    private String campName;

    /**
     * 排期名称
     */
    private String scheduleName;
    /**
     * 1-正常 2-已关闭 3-已退款
     */
    private Byte orderStatus;

}
