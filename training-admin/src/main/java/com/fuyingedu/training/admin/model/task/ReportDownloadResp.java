package com.fuyingedu.training.admin.model.task;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ReportDownloadResp {

    @ExcelProperty("辅导老师")
    private String teacherName;
    @ExcelProperty("班级")
    private String clazzName;
    @ExcelProperty("小组")
    private String groupName;
    @ExcelProperty("学员手机号")
    private String phoneNo;
    @ExcelProperty("本期训练营昵称")
    private String nickName;


    @ExcelProperty("是否按时完成")
    private String taskState;
    @ExcelProperty("答题正确率")
    private String corrRate;
    @ExcelProperty("练习题")
    private String step2Que;
    @ExcelProperty("应用题")
    private String step3Corr;
}
