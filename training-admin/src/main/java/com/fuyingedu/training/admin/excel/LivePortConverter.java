package com.fuyingedu.training.admin.excel;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;

public class LivePortConverter implements Converter<Byte> {

    public WriteCellData<?> convertToExcelData(Byte value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) {
        if (Byte.valueOf((byte) 1).equals(value)) {
            return new WriteCellData<>("腾讯会议");
        } else if (Byte.valueOf((byte) 2).equals(value)) {
            return new WriteCellData<>("保利威");
        }
        return new WriteCellData<>("百家云");
    }
}
