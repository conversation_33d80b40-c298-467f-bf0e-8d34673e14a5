package com.fuyingedu.training.admin.controller;

import com.fuyingedu.training.admin.model.group.*;
import com.fuyingedu.training.admin.service.GroupService;
import com.fuyingedu.training.common.annotation.Login;
import com.fuyingedu.training.common.model.CommResp;
import com.fuyingedu.training.common.util.RespUtils;
import com.fuyingedu.training.front.model.clazz.GroupResp;
import com.fuyingedu.training.front.service.FrontGroupService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 小组管理
 */
@RestController
@RequestMapping("admin/group")
public class GroupController {

    @Autowired
    private GroupService groupService;
    @Autowired
    private FrontGroupService frontGroupService;

    /**
     * 小组列表
     */
    @GetMapping("list")
    public CommResp<List<GroupResp>> list(@RequestParam("clazzId") Long clazzId) {
        return frontGroupService.list(clazzId);
    }

    /**
     * 添加小组
     */
    @PostMapping("save")
    public CommResp<?> save(@RequestBody @Valid SaveReq saveReq) {
        groupService.save(saveReq);
        return RespUtils.success();
    }

    /**
     * 编辑小组
     */
    @PostMapping("update")
    public CommResp<?> update(@RequestBody @Valid UpdateReq updateReq) {
        groupService.update(updateReq);
        return RespUtils.success();
    }

    /**
     * 选择陪跑志愿者列表
     */
    @GetMapping("user/list")
    public CommResp<List<UserItemResp>> userList(@RequestParam("groupId") Long groupId) {
        return groupService.userList(groupId);
    }

    /**
     * 班级里的分组情况
     * @param userId 前端不需要传
     */
    @GetMapping("info")
    public CommResp<InfoResp> info(@Login Long userId, @RequestParam("clazzId") Long clazzId) {
        return groupService.info(userId, clazzId);
    }

    /**
     * 自动分组
     * @param userId 前端不需要传
     */
    @PostMapping("auto/alloc")
    public CommResp<List<AllocItemResp>> autoAlloc(@Login Long userId, @RequestBody @Valid AllocReq allocReq) {
        return groupService.autoAlloc(userId, allocReq);
    }
}
