package com.fuyingedu.training.admin.model.task;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;

@Getter
@Setter
@ToString
public class ItemResp {
    private Long id;
    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 签到开始时间
     */
    private LocalTime startTime;

    /**
     * 签到结束时间
     */
    private LocalTime endTime;

    /**
     * 任务开始日期
     */
    private LocalDate startDate;

    /**
     * 任务结束日期
     */
    private LocalDate endDate;

    /**
     * 任务类型 1-签到 2-打卡 3-作业
     */
    private Byte taskType;

    /**
     * 配置人
     */
    private String realName;

    /**
     * 小班名称
     */
    private List<String> classNameList;

    /**
     * 任务级别 1-排期 2-大班 3-小班
     */
    private Byte taskLevel;

    /**
     * 上传内容
     */
    private List<UploadItem> itemList;

}
