package com.fuyingedu.training.admin.model.student;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDate;

@Getter
@Setter
@ToString
public class TaskItemResp {
    private Long id;
    /**
     * 任务名称
     */
    private String taskName;
    /**
     * 任务开启时间
     */
    private LocalDate startDate;

    /**
     * 任务截止时间
     */
    private LocalDate endDate;
    /**
     * 状态 1-未开始 2-进行中（未提交） 3-已完成（已提交） 4-已结束（已逾期）
     */
    private Byte taskStatus;

    /**
     * 作业点评次数
     */
    private Integer remarkedNum;
    /**
     * 点赞数量
     */
    private Integer likeNum;

    /**
     * 1-普通作业 2-优秀作业
     */
    private Byte recordType;
    /**
     * 任务介绍
     */
    private String taskContent;
}
