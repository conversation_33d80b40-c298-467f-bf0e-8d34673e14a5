package com.fuyingedu.training.admin.controller;

import com.fuyingedu.training.admin.model.login.LoginReq;
import com.fuyingedu.training.admin.model.login.LoginResp;
import com.fuyingedu.training.admin.service.LoginService;
import com.fuyingedu.training.common.model.CommResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 后端用户登录
 */
@RestController
@RequestMapping("admin")
@Slf4j
public class LoginController {

    @Autowired
    private LoginService loginService;

    /**
     * 登录
     * 前端将该接口返回的token通过Authorization请求头带回，如果Token即将过去，会重新生成一个Token并通过Authorization请求头返回给前端
     */
    @PostMapping("login")
    public CommResp<LoginResp> login(@RequestBody LoginReq loginReq) {
        return loginService.login(loginReq);
    }

    @GetMapping("loginForTest")
    public CommResp<LoginResp> loginForTest(@RequestParam Long phoneNum) {
        return loginService.loginForTest(phoneNum);
    }
}
