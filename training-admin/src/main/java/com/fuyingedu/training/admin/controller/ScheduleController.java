package com.fuyingedu.training.admin.controller;

import com.fuyingedu.training.admin.model.schedule.*;
import com.fuyingedu.training.admin.service.ScheduleService;
import com.fuyingedu.training.common.annotation.Login;
import com.fuyingedu.training.common.annotation.PreAuthorize;
import com.fuyingedu.training.common.model.CommResp;
import com.fuyingedu.training.common.util.RespUtils;
import com.fuyingedu.training.dto.fuying.ScheduleRet;
import com.fuyingedu.training.front.model.feign.ClazzItem;
import com.fuyingedu.training.front.model.schedule.FutureResp;
import com.fuyingedu.training.front.service.FrontScheduleService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 排期管理
 */
@RestController
@RequestMapping("admin/schedule")
public class ScheduleController {

    @Autowired
    private ScheduleService scheduleService;
    @Autowired
    private FrontScheduleService frontScheduleService;

    /**
     * 添加/编辑排期
     */
    @PreAuthorize({"管理员", "普通管理员"})
    @PostMapping("save")
    public CommResp<?> addOrEdit(@Login Long userId, @Valid @RequestBody SaveReq saveReq) {
        scheduleService.addOrEdit(userId, saveReq);
        return RespUtils.success();
    }

    /**
     * 排期列表
     */
    @GetMapping("list")
    public CommResp<List<ItemResp>> list(@Valid ListReq listReq) {
        return scheduleService.list(listReq);
    }

    /**
     * 学员参加的排期列表
     */
    @GetMapping("order/list")
    public CommResp<List<Relation>> orderList(@RequestParam("orderId") Long orderId) {
        return scheduleService.orderList(orderId);
    }

    /**
     * 未开始的排期列表
     */
    @GetMapping("future/list")
    public CommResp<List<FutureResp>> futureList(@RequestParam("campId") Long orderId) {
        // campId前端传的是orderId
        return frontScheduleService.futureList(orderId);
    }

    /**
     * 排期详情
     */
    @GetMapping("detail")
    public CommResp<DetailResp> detail(@RequestParam("id") Long id) {
        return scheduleService.detail(id);
    }

    /**
     * 扶鹰系统的排期列表
     */
    @GetMapping("fy/list")
    public CommResp<List<ScheduleRet>> fyList(FyListReq fyListReq) {
        return scheduleService.fyList(fyListReq);
    }

    /**
     * 扶鹰系统的排期班级
     */
    @GetMapping("fy/clazz/list")
    public CommResp<List<ClazzItem>> fyClazzList(@RequestParam("scheduleId") Long scheduleId) {
        return scheduleService.fyClazzList(scheduleId);
    }

    /**
     * 手动同步服务单
     */
    @PostMapping("sync/order")
    public CommResp<?> syncOrder(@Login Long userId, @RequestBody @Valid SyncOrderReq syncOrderReq) {
        scheduleService.syncOrder(syncOrderReq);
        return RespUtils.success();
    }

    @PostMapping("flush")
    public CommResp<?> flush() {
        scheduleService.flushClazz();
        return RespUtils.success();
    }
}
