package com.fuyingedu.training.admin.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fuyingedu.training.admin.model.pk.ItemResp;
import com.fuyingedu.training.admin.model.pk.SaveReq;
import com.fuyingedu.training.common.model.CommResp;
import com.fuyingedu.training.common.util.RespUtils;
import com.fuyingedu.training.entity.WordPkConfig;
import com.fuyingedu.training.mapper.WordPkConfigMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

@Service
public class PkService {

    @Autowired
    private WordPkConfigMapper wordPkConfigMapper;

    public CommResp<?> save(Long userId, SaveReq req) {
        if (req.getStartTime().isAfter(req.getStopTime())) {
            return RespUtils.warning(500, "PK时间错误");
        }
        List<WordPkConfig> pkConfigList = wordPkConfigMapper.selectList(new QueryWrapper<WordPkConfig>()
                .eq(WordPkConfig.SCHEDULE_ID, req.getScheduleId()).ne(req.getId() != null, WordPkConfig.ID, req.getId())
                .orderByAsc(WordPkConfig.START_TIME));
        for (WordPkConfig config : pkConfigList) {
            if (!req.getStartTime().isBefore(config.getStartTime()) && !req.getStartTime().isAfter(config.getStopTime())) {
                return RespUtils.warning(500, "PK时间冲突");
            }
            if (!req.getStopTime().isBefore(config.getStartTime()) && !req.getStopTime().isAfter(config.getStopTime())) {
                return RespUtils.warning(500, "PK时间冲突");
            }
            if (!config.getStartTime().isBefore(req.getStartTime()) && !config.getStartTime().isAfter(req.getStopTime())) {
                return RespUtils.warning(500, "PK时间冲突");
            }
            if (!config.getStopTime().isBefore(req.getStartTime()) && !config.getStopTime().isAfter(req.getStopTime())) {
                return RespUtils.warning(500, "PK时间冲突");
            }
        }
        WordPkConfig config = new WordPkConfig();
        config.setId(req.getId());
        config.setScheduleId(req.getScheduleId());
        config.setStartTime(req.getStartTime());
        config.setStopTime(req.getStopTime());
        wordPkConfigMapper.insertOrUpdate(config);
        return RespUtils.success();
    }

    public CommResp<?> delete(Long id) {
        WordPkConfig config = wordPkConfigMapper.selectById(id);
        LocalDateTime now = LocalDateTime.now();
        if (now.isAfter(config.getStartTime())) {
            return RespUtils.warning(500, "PK已开始无法删除");
        }
        wordPkConfigMapper.deleteById(id);
        return RespUtils.success();
    }

    public CommResp<List<ItemResp>> list(Long scheduleId) {
        List<WordPkConfig> configList = wordPkConfigMapper.selectList(new QueryWrapper<WordPkConfig>().select(
                WordPkConfig.START_TIME, WordPkConfig.ID, WordPkConfig.STOP_TIME
                ).eq(WordPkConfig.SCHEDULE_ID, scheduleId).orderByAsc(WordPkConfig.START_TIME));
        return RespUtils.success(configList.stream().map(
                item -> {
                    ItemResp itemResp = new ItemResp();
                    itemResp.setPkId(item.getId());
                    itemResp.setStartTime(item.getStartTime());
                    itemResp.setStopTime(item.getStopTime());
                    if (item.getStartTime().isAfter(LocalDateTime.now())) {
                        itemResp.setStatus(1);
                    } else if (item.getStopTime().isAfter(LocalDateTime.now())) {
                        itemResp.setStatus(2);
                    } else {
                        itemResp.setStatus(3);
                    }
                    return itemResp;
                }
        ).toList());
    }
}
