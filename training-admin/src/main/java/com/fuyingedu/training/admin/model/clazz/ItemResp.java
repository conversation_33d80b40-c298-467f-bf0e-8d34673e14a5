package com.fuyingedu.training.admin.model.clazz;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Getter
@Setter
@ToString
public class ItemResp {

    private Long id;

    /**
     * 班级名称
     */
    private String className;

    /**
     * 班级人数
     */
    private Integer studentNum;

    /**
     * 助教id
     */
    private Long teacherId;

    /**
     * 助教名称
     */
    private String teacherName;

    /**
     * 小组数量
     */
    private Integer groupNum;

    /**
     * 班级群二维码
     */
    private String wxUrl;

    private Long groupId;
    /**
     * 班级群名称
     */
    private String groupName;

    /**
     * 陪跑志愿者
     */
    private List<String> monitorList;

    /**
     * 单词训练营PK状态
     * 1:未开始，2：进行中
     */
    private Byte pkStatus = 1;
}
