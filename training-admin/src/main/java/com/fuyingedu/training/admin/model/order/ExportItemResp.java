package com.fuyingedu.training.admin.model.order;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.fuyingedu.training.admin.excel.OrderStatusConverter;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * 服务单导出Excel响应类
 */
@Getter
@Setter
@ToString
public class ExportItemResp {

    /**
     * 服务单号
     */
    @ExcelProperty("服务单号")
    private Long id;

    /**
     * 来源服务单号
     */
    @ExcelProperty("来源服务单号")
    private String orderNo;

    /**
     * 订单号
     */
    @ExcelProperty("订单号")
    private String realOrderNo;

    /**
     * 用户昵称
     */
    @ExcelProperty("用户昵称")
    private String realName;

    /**
     * 用户手机号
     */
    @ExcelProperty("用户手机号")
    private String phoneNum;

    /**
     * 课程名称
     */
    @ExcelProperty("课程名称")
    private String campName;

    /**
     * 排期名称
     */
    @ExcelProperty("排期名称")
    private String scheduleName;

    /**
     * 分配导师
     */
    @ExcelProperty("分配导师")
    private String teacherName;

    /**
     * 班级名称
     */
    @ExcelProperty("班级名称")
    private String clazzName;

    /**
     * 分组名称
     */
    @ExcelProperty("分组名称")
    private String groupName;

    /**
     * 服务状态
     */
    @ExcelProperty(value = "服务状态", converter = OrderStatusConverter.class)
    private Byte orderStatus;

    /**
     * 核销时间
     */
    @ExcelProperty("核销时间")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private LocalDateTime signTime;
}
