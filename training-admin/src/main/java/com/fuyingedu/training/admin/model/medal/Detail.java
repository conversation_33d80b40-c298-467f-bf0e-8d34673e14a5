package com.fuyingedu.training.admin.model.medal;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Getter
@Setter
@ToString
public class Detail {
    private Long id;
    /**
     * 奖状名称
     */
    @NotBlank
    private String medalName;

    /**
     * 模板名称
     */
    @NotBlank
    private String templateName;

    /**
     * 奖状图片
     */
    @NotBlank
    private String medalIcon;

    /**
     * 奖状描述
     */
    @NotBlank
    private String medalContent;

    /**
     * 奖状状态 1-有效 2-无效
     */
    private Byte medalStatus;
    /**
     * 管理端：训练营类型
     * 导师端：班级类型
     * 1 - 所有 2 - 指定
     */
    private Byte type;

    /**
     * 管理端：训练营id列表
     * 导师端：班级列表
     */
    private List<Long> ids;

    /**
     * 排期ID
     */
    private Long scheduleId;
}
