package com.fuyingedu.training.admin.model.order;

import com.fuyingedu.training.common.model.SortPageReq;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class RecordListReq extends SortPageReq {

    /**
     * 排期ID
     */
    @NotNull
    private Long scheduleId;
    /**
     * 用户信息
     */
    private String userInfo;

    /**
     * 学员信息
     */
    private String studentInfo;
    /**
     * 班级id 必填
     */
    @NotNull
    private Long clazzId;

    /**
     * 分组id -1 表示未分组
     */
    private Long groupId;
    /**
     * 组内身份 1-学员 2-陪跑
     */
    private Byte studentType;
}
