package com.fuyingedu.training.admin.controller;

import com.fuyingedu.training.common.annotation.Login;
import com.fuyingedu.training.admin.model.clazz.*;
import com.fuyingedu.training.admin.model.schedule.MedalItemResp;
import com.fuyingedu.training.admin.service.ClazzService;
import com.fuyingedu.training.common.model.CommResp;
import com.fuyingedu.training.common.util.RespUtils;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 小班管理
 */
@RestController
@RequestMapping("admin/clazz")
public class ClazzController {

    @Autowired
    private ClazzService clazzService;

    /**
     * 小班列表
     * @param userId 前端不需要传
     */
    @GetMapping("list")
    public CommResp<List<ItemResp>> list(@Login Long userId, @RequestParam("scheduleId") Long scheduleId) {
        return clazzService.list(userId, scheduleId);
    }

    /**
     * 我管理的班级列表
     * @param userId 前端不需要传
     */
    @GetMapping("manage/list")
    public CommResp<List<ManageItemResp>> manageList(@Login Long userId, @RequestParam("scheduleId") Long scheduleId) {
        return clazzService.manageList(userId, scheduleId);
    }

    /**
     * 添加小班
     * @param userId 前端不需要传
     */
    @PostMapping("save")
    public CommResp<?> add(@Login Long userId, @RequestBody @Valid SaveReq saveReq) {
        if (saveReq.getClazzNum() < saveReq.getTeacherIdList().size()) {
            return RespUtils.warning(500, "助教数量不能大于班级人数");
        }
        clazzService.add(userId, saveReq);
        return RespUtils.success();
    }

    /**
     * 修改小班名称或班级二维码
     * @param userId 前端不需要传
     */
    @PostMapping("update")
    public CommResp<?> update(@Login Long userId, @RequestBody @Valid UpdateReq updateReq) {
        clazzService.update(userId, updateReq);
        return RespUtils.success();
    }

    /**
     * 自动分班
     * @param userId 前端不需要传
     */
    @PostMapping("auto/alloc")
    public CommResp<List<AllocItemResp>> autoAlloc(@Login Long userId, @RequestBody @Valid AllocReq allocReq) {
        return clazzService.autoAlloc(userId, allocReq);
    }

    /**
     * 查询班级可用的所有奖牌
     */
    @GetMapping("medal/list")
    public CommResp<List<MedalItemResp>> medalList(@RequestParam("clazzId") Long clazzId) {
        return clazzService.medalList(clazzId);
    }
}
