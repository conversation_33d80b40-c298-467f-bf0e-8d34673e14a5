package com.fuyingedu.training.admin.model.bjy;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

@Getter
@Setter
@Accessors(chain = true)
public class RoomResp {
    /**
     * 房间创建时间
     */
    private LocalDateTime roomTime;

    /**
     * 教室id
     */
    private Long roomId;

    /**
     * 排课id
     */
    private Long scheduleId;

    /**
     * 教室名称
     */
    private String roomName;

    /**
     * 老师参与码
     */
    private String teacherCode;

    private String teacherUrl;

    /**
     * 助教参与码
     */
    private String adminCode;

    private String adminUrl;

    /**
     * 学员参与码
     */
    private String studentCode;

    private Long campId;

    private String campName;

    private String scheduleName;

    /**
     * 开播次数
     */
    private Integer repeatNum;

    /**
     * 分组数量
     */
    private Integer groupNum;

    /**
     * 测试题目数量
     */
    private Integer quizNum;
}
