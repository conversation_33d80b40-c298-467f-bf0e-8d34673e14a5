package com.fuyingedu.training.admin.controller;

import com.fuyingedu.training.admin.model.teacher.ListReq;
import com.fuyingedu.training.admin.model.user.AdminResp;
import com.fuyingedu.training.admin.model.user.SaveReq;
import com.fuyingedu.training.admin.service.UserService;
import com.fuyingedu.training.common.annotation.PreAuthorize;
import com.fuyingedu.training.common.model.CommResp;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 用户管理
 */
@RestController
@RequestMapping("admin/user")
public class UserController {

    @Autowired
    private UserService userService;

    /**
     * 管理员账号列表
     */
    @PreAuthorize("管理员")
    @GetMapping("list")
    public CommResp<List<AdminResp>> list(ListReq req) {
        return userService.list(req);
    }

    /**
     * 管理员账号详情
     */
    @PreAuthorize("管理员")
    @GetMapping("detail")
    public CommResp<AdminResp> detail(Long id) {
        return userService.detail(id);
    }

    /**
     * 添加或修改管理员账号
     */
    @PreAuthorize("管理员")
    @PostMapping("save")
    public CommResp<?> addOrEdit(@RequestBody SaveReq req) {
        return userService.addOrEdit(req);
    }
}
