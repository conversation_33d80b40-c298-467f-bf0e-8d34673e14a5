package com.fuyingedu.training.admin.model.user;

import com.fuyingedu.training.admin.model.teacher.SaveReq;
import com.fuyingedu.training.entity.User;
import com.fuyingedu.training.front.model.media.MediaConvertor;

public class UserConvertor {

    public static AdminResp toAdminResp(User user) {
        AdminResp adminResp = new AdminResp();
        adminResp.setUid(user.getId());
        adminResp.setUserIcon(MediaConvertor.getMediaUrl(user.getUserIcon()));
        adminResp.setNickName(user.getNickName());
        adminResp.setRealName(user.getRealName());
        adminResp.setPhoneNum(user.getPhoneNum());
        adminResp.setRoleType(user.getLiveMessageNum());
        return adminResp;
    }

    public static User toUser(SaveReq saveReq) {
        User user = new User();
        user.setId(saveReq.getOuterId());
        user.setUserIcon(saveReq.getUserIcon());
        user.setRealName(saveReq.getRealName());
        user.setPhoneNum(saveReq.getPhoneNum());
        user.setUnionId(saveReq.getUnionId());
        return user;
    }


    public static User toUser(com.fuyingedu.training.admin.model.user.SaveReq  saveReq) {
        User user = new User();
        user.setId(saveReq.getUid());
        user.setUserIcon(saveReq.getUserIcon());
        user.setRealName(saveReq.getRealName());
        user.setPhoneNum(saveReq.getPhoneNum());
        user.setUnionId(saveReq.getUnionId());
        return user;
    }
}
