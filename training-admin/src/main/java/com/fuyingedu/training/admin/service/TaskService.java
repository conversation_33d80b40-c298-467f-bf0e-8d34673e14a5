package com.fuyingedu.training.admin.service;

import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.fasterxml.jackson.databind.JsonNode;
import com.fuyingedu.training.admin.model.task.*;
import com.fuyingedu.training.common.enums.*;
import com.fuyingedu.training.common.exception.WebBaseException;
import com.fuyingedu.training.common.model.CommResp;
import com.fuyingedu.training.common.util.AsyncUtils;
import com.fuyingedu.training.common.util.DateUtils;
import com.fuyingedu.training.common.util.JsonUtils;
import com.fuyingedu.training.common.util.RespUtils;
import com.fuyingedu.training.entity.*;
import com.fuyingedu.training.front.manager.WxMaManager;
import com.fuyingedu.training.front.model.media.MediaConvertor;
import com.fuyingedu.training.front.model.word.TaskItem;
import com.fuyingedu.training.front.service.FrontWordService;
import com.fuyingedu.training.mapper.*;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class TaskService {

    @Autowired
    private TaskMapper taskMapper;
    @Autowired
    private UserMapper userMapper;
    @Autowired
    private GroupMapper groupMapper;
    @Autowired
    private ClazzMapper clazzMapper;
    @Autowired
    private TaskStatisticMapper taskStatisticMapper;
    @Autowired
    private TaskRelationMapper taskRelationMapper;
    @Autowired
    private TeacherMapper teacherMapper;
    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private TaskSubmitRecordMapper taskSubmitRecordMapper;
    @Autowired
    private TaskRecordRemarkMapper taskRecordRemarkMapper;
    @Autowired
    private TaskRecordLabelMapper taskRecordLabelMapper;
    @Autowired
    private LabelMapper labelMapper;
    @Autowired
    private ScheduleMapper scheduleMapper;
    @Autowired
    private ScheduleTeacherMapper scheduleTeacherMapper;
    @Autowired
    private WxMaManager wxMaManager;
    @Autowired
    private FrontWordService frontWordService;
    @Autowired
    private RestTemplate restTemplate;
    @Value("${fxy.base-url}")
    private String baseUrl;

    public CommResp<List<ItemResp>> list(ListReq listReq) {
        List<Task> taskList = taskMapper.selectList(new QueryWrapper<Task>().select(
                Task.ID,
                Task.TASK_NAME,
                Task.START_DATE,
                Task.END_DATE,
                Task.TASK_TYPE,
                Task.CREATED_USER_ID,
                Task.TASK_LEVEL,
                Task.START_TIME,
                Task.END_TIME,
                Task.UPLOAD_ITEMS, Task.CLAZZ_TYPE
        ).eq(Task.SCHEDULE_ID, listReq.getScheduleId()).orderByDesc(Task.ID));
        if (taskList.isEmpty()) {
            return RespUtils.success(Collections.emptyList());
        }
        List<Long> userIds = taskList.stream().map(Task::getCreatedUserId).toList();
        Map<Long, User> userMap = userMapper.selectList(new QueryWrapper<User>().select(User.ID, User.REAL_NAME).in(User.ID, userIds))
                .stream().collect(Collectors.toMap(User::getId, Function.identity()));

        List<Long> taskIds = taskList.stream().filter(task -> TaskClazzType.SPECIFIC.getCode().equals(task.getClazzType())).map(Task::getId).toList();
        Map<Long, List<String>> clazzNameMap = Collections.emptyMap();
        if (!taskIds.isEmpty()) {
            List<TaskRelation> taskRelationList = taskRelationMapper.selectList(new QueryWrapper<TaskRelation>().select(
                    TaskRelation.TASK_ID,
                    TaskRelation.CLAZZ_ID
            ).in(TaskRelation.TASK_ID, taskIds));
            if (!taskRelationList.isEmpty()) {
                List<Long> clazzIds = taskRelationList.stream().map(TaskRelation::getClazzId).distinct().toList();
                Map<Long, String> nameMap = clazzMapper.selectList(new QueryWrapper<Clazz>().select(
                        Clazz.ID,
                        Clazz.CLASS_NAME
                ).in(Clazz.ID, clazzIds)).stream().collect(Collectors.toMap(Clazz::getId, Clazz::getClassName));
                clazzNameMap = taskRelationList.stream().collect(Collectors.groupingBy(TaskRelation::getTaskId,
                        Collectors.mapping(taskRelation -> nameMap.get(taskRelation.getClazzId()), Collectors.toList())));
            }
        }
        List<ItemResp> respList = new ArrayList<>(taskList.size());
        for (Task task : taskList) {
            ItemResp itemResp = toItemResp(task);
            itemResp.setRealName(userMap.get(task.getCreatedUserId()).getRealName());
            if (TaskClazzType.SPECIFIC.getCode().equals(task.getClazzType())) {
                itemResp.setClassNameList(clazzNameMap.get(task.getId()));
            } else {
                itemResp.setClassNameList(Collections.singletonList("全部"));
            }
            respList.add(itemResp);
        }
        return RespUtils.success(respList);
    }

    public CommResp<DetailResp> detail(Long id) {
        Task task = taskMapper.selectOne(new QueryWrapper<Task>().select(
                Task.ID,
                Task.TASK_NAME,
                Task.TASK_TYPE,
                Task.TASK_REWARD,
                Task.FXY_REWARD,
                Task.TASK_CONTENT,
                Task.START_DATE,
                Task.END_DATE,
                Task.TASK_LEVEL,
                Task.START_TIME,
                Task.END_TIME,
                Task.CASE_URLS, Task.WORD_REWARD,
                Task.UPDATED_TIME, Task.CLAZZ_TYPE, Task.UPLOAD_ITEMS
        ).eq(Task.ID, id));
        if (task == null) {
            return RespUtils.warning(RespMetaEnum.PARAM_ERROR);
        }
        DetailResp detailResp = toDetailResp(task);
        detailResp.setClazzType(task.getClazzType());
        if (TaskClazzType.SPECIFIC.getCode().equals(task.getClazzType())) {
            List<Long> clazzIds = taskRelationMapper.selectList(new QueryWrapper<TaskRelation>().select(
                    TaskRelation.CLAZZ_ID
            ).eq(TaskRelation.TASK_ID, id)).stream().map(TaskRelation::getClazzId).toList();
            detailResp.setClazzIdList(clazzIds);
        }
        detailResp.setStartTime(task.getStartTime());
        detailResp.setEndTime(task.getEndTime());
        if (StringUtils.hasLength(task.getCaseUrls())) {
            detailResp.setCaseMediaUrlList(MediaConvertor.getMediaList(task.getCaseUrls()));
        }
        if (TaskType.OUT_PUNCH.getCode().equals(task.getTaskType())) {
            detailResp.setFxyLabelList(JsonUtils.parseJsonToList(task.getUploadItems(), Byte.class));
        } else if (StringUtils.hasLength(task.getUploadItems())) {
            detailResp.setItemList(JsonUtils.parseJsonToList(task.getUploadItems(), UploadItem.class));
        }
        return RespUtils.success(detailResp);
    }

    @Transactional(rollbackFor = Exception.class)
    public void saveByTeacher(Long userId, SaveReq saveReq) {
        checkSaveReq(saveReq);
        Teacher teacher = teacherMapper.selectOne(new QueryWrapper<Teacher>().select(
                Teacher.ID
        ).eq(Teacher.USER_ID, userId));
        ScheduleTeacher scheduleTeacher = scheduleTeacherMapper.selectOne(new QueryWrapper<ScheduleTeacher>().select(
                ScheduleTeacher.ID, ScheduleTeacher.TEACHER_ID, ScheduleTeacher.TEACHER_TYPE
        ).eq(ScheduleTeacher.SCHEDULE_ID, saveReq.getScheduleId()).eq(ScheduleTeacher.TEACHER_ID, teacher.getId()));
        Task task = toTask(saveReq);
        task.setClazzType(saveReq.getClazzType());
        task.setCreatedUserId(userId);
        if (TeacherType.TEACHER.getCode().equals(scheduleTeacher.getTeacherType())) {
            task.setTaskLevel(TaskLevel.GRADE.getCode());
        } else {
            task.setTaskLevel(TaskLevel.CLAZZ.getCode());
        }
        List<Clazz> clazzList = Collections.emptyList();
        if (TaskClazzType.SPECIFIC.getCode().equals(saveReq.getClazzType())) {
            if (CollectionUtils.isEmpty(saveReq.getClazzIdList())) {
                throw new WebBaseException(4000, "班级列表不能未空");
            }
            clazzList = clazzMapper.selectList(new QueryWrapper<Clazz>().select(
                    Clazz.ID, Clazz.SCHEDULE_ID, Clazz.CLASS_NAME, Clazz.TEACHER_ID, Clazz.ASSISTANT_ID
            ).eq(Clazz.SCHEDULE_ID, saveReq.getScheduleId())
                    .eq(TeacherType.TEACHER.getCode().equals(scheduleTeacher.getTeacherType()), Clazz.TEACHER_ID, teacher.getId())
                    .eq(TeacherType.ASSISTANT.getCode().equals(scheduleTeacher.getTeacherType()), Clazz.ASSISTANT_ID, teacher.getId())
                    .in(Clazz.ID, saveReq.getClazzIdList()));
            if (clazzList.isEmpty()) {
                throw new WebBaseException(4000, "班级列表不能未空");
            }
        }
        if (saveReq.getId() == null) {
            add(task, saveReq);
            List<Clazz> finalClazzList = clazzList;
            AsyncUtils.execute(() -> wxMaManager.homeworkMessage(saveReq.getScheduleId(), scheduleTeacher, task, finalClazzList),
                    "老师添加任务");
        } else {
            edit(userId, task);
            taskRelationMapper.delete(new QueryWrapper<TaskRelation>().eq(TaskRelation.TASK_ID, task.getId()));
        }
        List<TaskRelation> relationList = new ArrayList<>();
        if (TaskClazzType.ALL.getCode().equals(saveReq.getClazzType())) {
            if (TeacherType.ASSISTANT.getCode().equals(scheduleTeacher.getTeacherType())) {
                Set<Long> teacherIds = clazzMapper.selectList(new QueryWrapper<Clazz>().select(Clazz.ID, Clazz.TEACHER_ID)
                                .eq(Clazz.SCHEDULE_ID, saveReq.getScheduleId()).eq(Clazz.ASSISTANT_ID, teacher.getId()))
                        .stream().map(Clazz::getTeacherId).collect(Collectors.toSet());
                for (Long teacherId : teacherIds) {
                    relationList.add(toTaskRelation(saveReq.getScheduleId(), teacherId, teacher.getId(), -1L, task.getId()));
                }
            } else {
                relationList.add(toTaskRelation(saveReq.getScheduleId(), teacher.getId(), -1L, -1L, task.getId()));
            }
        } else {
            for (Clazz clazz : clazzList) {
                relationList.add(toTaskRelation(saveReq.getScheduleId(), clazz.getTeacherId(), clazz.getAssistantId(), clazz.getId(), task.getId()));
            }
        }
        if (!relationList.isEmpty()) {
            taskRelationMapper.insert(relationList);
        }
    }

    private TaskRelation toTaskRelation(Long scheduleId, Long teacherId, Long assistantId, Long clazzId, Long taskId) {
        TaskRelation relation = new TaskRelation();
        relation.setTaskId(taskId);
        relation.setScheduleId(scheduleId);
        relation.setTeacherId(teacherId);
        relation.setAssistantId(assistantId);
        relation.setClazzId(clazzId);
        return relation;
    }

    @Transactional(rollbackFor = Exception.class)
    public void addOrEdit(Long userId, SaveReq saveReq) {
        checkSaveReq(saveReq);
        Task task = toTask(saveReq);
        task.setTaskLevel(TaskLevel.SCHEDULE.getCode());
        task.setCreatedUserId(userId);
        if (saveReq.getId() == null) {
            add(task, saveReq);
            taskRelationMapper.insert(toTaskRelation(saveReq.getScheduleId(), -1L, -1L, -1L, task.getId()));
            AsyncUtils.execute(() -> wxMaManager.homeworkMessage(saveReq.getScheduleId(), task), "管理员添加作业消息");
        } else {
            edit(userId, task);
        }
    }

    private void checkSaveReq(SaveReq saveReq) {
        if (saveReq.getStartDate().isAfter(saveReq.getEndDate())) {
            throw new WebBaseException(4000, "任务开始日期不能大于结束日期");
        }
        if (!TaskType.HOMEWORK.getCode().equals(saveReq.getTaskType())) {
            if (saveReq.getStartTime() != null && saveReq.getEndTime() != null && saveReq.getStartTime().isAfter(saveReq.getEndTime())) {
                throw new WebBaseException(4000, "任务开始时间不能大于结束时间");
            }
        }
        Schedule schedule = scheduleMapper.selectOne(new QueryWrapper<Schedule>().select(
                Schedule.ID, Schedule.START_TIME, Schedule.END_TIME
        ).eq(Schedule.ID, saveReq.getScheduleId()));
        if (saveReq.getStartDate().isBefore(schedule.getStartTime().toLocalDate())) {
            throw new WebBaseException(4000, "任务开始日期不能小于排期开始日期");
        }
        if (saveReq.getEndDate().isAfter(schedule.getEndTime().toLocalDate())) {
            throw new WebBaseException(4000, "任务结束日期不能大于排期结束日期");
        }
    }

    private void edit(Long userId, Task task) {
        Task oldTask = taskMapper.selectOne(new QueryWrapper<Task>().select(
                Task.ID, Task.CREATED_USER_ID, Task.TASK_TYPE
        ).eq(Task.ID, task.getId()));
        if (oldTask == null) {
            throw new WebBaseException(RespMetaEnum.PARAM_ERROR);
        }
        if (TaskType.WORD.getCode().equals(oldTask.getTaskType())) {
            throw new WebBaseException(4000, "单词训练营默认任务不能编辑");
        }
        if (!userId.equals(oldTask.getCreatedUserId())) {
            throw new WebBaseException(4000, "您不是该任务的创建者，不能进行编辑");
        }
        taskMapper.updateById(task);
    }

    private void add(Task task, SaveReq saveReq) {
        taskMapper.insert(task);
        scheduleMapper.update(new UpdateWrapper<Schedule>().setSql(String.format("%s = %s + 1", Schedule.TASK_NUM, Schedule.TASK_NUM))
                .eq(Schedule.ID, saveReq.getScheduleId()));
    }

    private Task toTask(SaveReq saveReq) {
        Task task = new Task();
        task.setId(saveReq.getId());
        task.setScheduleId(saveReq.getScheduleId());
        task.setTaskName(saveReq.getTaskName());
        task.setTaskType(saveReq.getTaskType());
        task.setTaskContent(saveReq.getTaskContent());
        task.setTaskReward(saveReq.getTaskReward());
        task.setFxyReward(saveReq.getFxyReward());
        task.setWordReward(saveReq.getWordReward());
        task.setStartDate(saveReq.getStartDate());
        task.setEndDate(saveReq.getEndDate());
        task.setStartTime(saveReq.getStartTime());
        task.setEndTime(saveReq.getEndTime());
        if (!CollectionUtils.isEmpty(saveReq.getCaseMediaUrlList())) {
            task.setCaseUrls(MediaConvertor.getMediaUrls(saveReq.getCaseMediaUrlList()));
        } else {
            task.setCaseUrls(JsonUtils.formatObjToJson(Collections.emptyList()));
        }
        if (TaskType.OUT_PUNCH.getCode().equals(task.getTaskType())) {
            if (CollectionUtils.isEmpty(saveReq.getFxyLabelList())) {
                throw new WebBaseException(4000, "完成的标签不能为空");
            }
            task.setUploadItems(JsonUtils.formatObjToJson(saveReq.getFxyLabelList()));
        } else if (!CollectionUtils.isEmpty(saveReq.getItemList())) {
            task.setUploadItems(JsonUtils.formatObjToJson(saveReq.getItemList()));
        } else {
            task.setUploadItems(JsonUtils.formatObjToJson(Collections.emptyList()));
        }
        return task;
    }

    private ItemResp toItemResp(Task task) {
        ItemResp itemResp = new ItemResp();
        itemResp.setId(task.getId());
        itemResp.setTaskName(task.getTaskName());
        itemResp.setStartDate(task.getStartDate());
        itemResp.setEndDate(task.getEndDate());
        itemResp.setTaskType(task.getTaskType());
        itemResp.setTaskLevel(task.getTaskLevel());
        itemResp.setStartTime(task.getStartTime());
        itemResp.setEndTime(task.getEndTime());
        if (!TaskType.OUT_PUNCH.getCode().equals(task.getTaskType()) && StringUtils.hasLength(task.getUploadItems())) {
            itemResp.setItemList(JsonUtils.parseJsonToList(task.getUploadItems(), UploadItem.class));
        }
        return itemResp;
    }

    private DetailResp toDetailResp(Task task) {
        DetailResp detailResp = new DetailResp();
        detailResp.setId(task.getId());
        detailResp.setTaskName(task.getTaskName());
        detailResp.setTaskContent(task.getTaskContent());
        detailResp.setTaskType(task.getTaskType());
        detailResp.setTaskReward(task.getTaskReward());
        detailResp.setFxyReward(task.getFxyReward());
        detailResp.setWordReward(task.getWordReward());
        detailResp.setStartDate(task.getStartDate());
        detailResp.setEndDate(task.getEndDate());
        return detailResp;
    }

    public CommResp<List<RecordItemResp>> recordList(Long userId, Long scheduleId) {
        Teacher teacher = teacherMapper.selectOne(new QueryWrapper<Teacher>().select(
                Teacher.ID
        ).eq(Teacher.USER_ID, userId));
        List<Clazz> clazzList = clazzMapper.selectList(new QueryWrapper<Clazz>().select(
                Clazz.ID
        ).eq(Clazz.ASSISTANT_ID, teacher.getId()).eq(Clazz.SCHEDULE_ID, scheduleId));
        Set<Long> clazzIds = new HashSet<>();
        if (!CollectionUtils.isEmpty(clazzList)) {
            clazzIds.addAll(clazzList.stream().map(Clazz::getId).toList());
        } else {
            clazzIds.addAll(clazzMapper.selectList(new QueryWrapper<Clazz>().select(Clazz.ID)
                            .eq(Clazz.SCHEDULE_ID, scheduleId).eq(Clazz.TEACHER_ID, teacher.getId()))
                    .stream().map(Clazz::getId).toList());
        }
        if (clazzIds.isEmpty()) {
            return RespUtils.success(Collections.emptyList());
        }
        List<Task> taskList = getTaskList(scheduleId, teacher.getId());
        if (taskList.isEmpty()) {
            return RespUtils.success(Collections.emptyList());
        }
        Map<Long, List<TaskStatistic>> taskRecordMap = taskStatisticMapper.selectList(new QueryWrapper<TaskStatistic>().select(
                        TaskStatistic.RECORD_NUM, TaskStatistic.TASK_ID)
                .in(TaskStatistic.TASK_ID, taskList.stream().map(Task::getId).toList())
                .in(TaskStatistic.CLAZZ_ID, clazzIds)
                .and(e -> e.eq(TaskStatistic.RECORD_DATE, LocalDate.now()).or().isNull(TaskStatistic.RECORD_DATE))
        ).stream().collect(Collectors.groupingBy(TaskStatistic::getTaskId));
        List<Order> orderList = orderMapper.selectList(new QueryWrapper<Order>().select(Order.ID, Order.CLAZZ_ID).in(Order.CLAZZ_ID, clazzIds)
                .in(Order.ORDER_STATUS, OrderStatus.NORMAL.getCode(), OrderStatus.MAY_REFUND.getCode()));
        Map<Long, List<Order>> clazzOrderMap = orderList.stream().collect(Collectors.groupingBy(Order::getClazzId));
        List<RecordItemResp> respList = taskList.stream().map(task -> {
                    RecordItemResp recordItemResp = toRecordItemResp(task, taskRecordMap);
                    if (TaskClazzType.SPECIFIC.getCode().equals(task.getClazzType())) {
                        List<Long> realClazzIds = taskRelationMapper.selectList(new QueryWrapper<TaskRelation>().select(TaskRelation.CLAZZ_ID)
                                .eq(TaskRelation.TASK_ID, task.getId()).in(TaskRelation.CLAZZ_ID, clazzIds)).stream().map(TaskRelation::getClazzId).toList();
                        int totalNum = 0;
                        for (Long clazzId : realClazzIds) {
                            totalNum += clazzOrderMap.getOrDefault(clazzId, Collections.emptyList()).size();
                        }
                        recordItemResp.setTotalNum(totalNum);
                    } else {
                        recordItemResp.setTotalNum(orderList.size());
                    }
                    if (TaskType.WORD.getCode().equals(task.getTaskType())) {
                        List<TaskItem> taskItems = frontWordService.taskList(scheduleId);
                        Integer i = 0;
                        for (TaskItem taskItem : taskItems) {
                            if (taskItem.getRealDate().isAfter(LocalDate.now())) {
                                break;
                            }
                            i = taskItem.getDays();
                        }
                        recordItemResp.setCurrentDay(i);
                        recordItemResp.setAllDay(taskItems.size());
                    }
                    return recordItemResp;
                })
                .sorted(Comparator.comparing(RecordItemResp::getTaskType))
                .sorted(Comparator.comparing(RecordItemResp::getTaskStatus)).toList();
        return RespUtils.success(respList);
    }

    private RecordItemResp toRecordItemResp(Task task, Map<Long, List<TaskStatistic>> taskRecordMap) {
        RecordItemResp resp = new RecordItemResp();
        resp.setId(task.getId());
        resp.setTaskName(task.getTaskName());
        resp.setTaskType(task.getTaskType());
        resp.setAllDay(DateUtils.days(task.getStartDate(), task.getEndDate()));
        resp.setCurrentDay(DateUtils.days(task.getStartDate(), LocalDate.now()));
        resp.setDoneNum(taskRecordMap.getOrDefault(task.getId(), Collections.emptyList())
                .stream().mapToInt(TaskStatistic::getRecordNum).sum());
        return resp;
    }

    private List<Task> getTaskList(Long scheduleId, Long teacherId) {
        ScheduleTeacher scheduleTeacher = scheduleTeacherMapper.selectOne(new QueryWrapper<ScheduleTeacher>().select(
                ScheduleTeacher.ID, ScheduleTeacher.TEACHER_TYPE
        ).eq(ScheduleTeacher.SCHEDULE_ID, scheduleId).eq(ScheduleTeacher.TEACHER_ID, teacherId));
        Set<Long> taskIds;
        if (TeacherType.TEACHER.getCode().equals(scheduleTeacher.getTeacherType())) {
            taskIds = taskRelationMapper.selectList(new QueryWrapper<TaskRelation>().select(
                    TaskRelation.TASK_ID
                ).eq(TaskRelation.SCHEDULE_ID, scheduleId).in(TaskRelation.TEACHER_ID, -1, teacherId))
                    .stream().map(TaskRelation::getTaskId).collect(Collectors.toSet());
        } else {
            taskIds = taskRelationMapper.selectList(new QueryWrapper<TaskRelation>().select(
                    TaskRelation.TASK_ID
            ).eq(TaskRelation.SCHEDULE_ID, scheduleId).in(TaskRelation.ASSISTANT_ID, -1, teacherId))
                    .stream().map(TaskRelation::getTaskId).collect(Collectors.toSet());
        }
        if (taskIds.isEmpty()) {
            return Collections.emptyList();
        }
        return taskMapper.selectList(new QueryWrapper<Task>().select(
                                Task.ID, Task.TASK_NAME, Task.START_DATE, Task.END_DATE, Task.TASK_REWARD,
                Task.FXY_REWARD, Task.TASK_TYPE, Task.CLAZZ_TYPE
                        ).in(Task.ID, taskIds).orderByDesc(Task.ID)
        );
    }

    public CommResp<List<DateItemResp>> dateList(Long userId, Long taskId) {
        Teacher teacher = teacherMapper.selectOne(new QueryWrapper<Teacher>().select(
                Teacher.ID
        ).eq(Teacher.USER_ID, userId));
        Task task = taskMapper.selectOne(new QueryWrapper<Task>().select(
                Task.START_DATE, Task.END_DATE, Task.SCHEDULE_ID, Task.TASK_TYPE
        ).eq(Task.ID, taskId));
        if (task == null) {
            throw new WebBaseException(RespMetaEnum.PARAM_ERROR);
        }
        List<Long> clazzIds = new ArrayList<>();
        List<Clazz> clazzList = clazzMapper.selectList(new QueryWrapper<Clazz>().select(
                Clazz.ID
        ).eq(Clazz.SCHEDULE_ID, task.getScheduleId()).eq(Clazz.ASSISTANT_ID, teacher.getId()));
        if (!CollectionUtils.isEmpty(clazzList)) {
            clazzIds.addAll(clazzList.stream().map(Clazz::getId).toList());
        } else {
            clazzIds.addAll(clazzMapper.selectList(new QueryWrapper<Clazz>().select(Clazz.ID)
                            .eq(Clazz.SCHEDULE_ID, task.getScheduleId()).eq(Clazz.TEACHER_ID, teacher.getId()))
                    .stream().map(Clazz::getId).toList());
        }
        Map<LocalDate, List<TaskStatistic>> taskRecordMap = Collections.emptyMap();
        long totalNum = 0;
        if (!clazzIds.isEmpty()) {
            taskRecordMap =
                    taskStatisticMapper.selectList(new QueryWrapper<TaskStatistic>().select(
                                    TaskStatistic.RECORD_NUM, TaskStatistic.RECORD_DATE)
                            .eq(TaskStatistic.TASK_ID, taskId)
                            .in(TaskStatistic.CLAZZ_ID, clazzIds)
                    ).stream().collect(Collectors.groupingBy(TaskStatistic::getRecordDate));
            totalNum = orderMapper.selectCount(new QueryWrapper<Order>().in(Order.CLAZZ_ID, clazzIds)
                    .in(Order.ORDER_STATUS, OrderStatus.NORMAL.getCode(), OrderStatus.MAY_REFUND.getCode()));
        }
        Map<LocalDate, TaskItem> taskItemMap = Collections.emptyMap();
        if (TaskType.WORD.getCode().equals(task.getTaskType())) {
            List<TaskItem> taskItems = frontWordService.taskList(task.getScheduleId());
            taskItemMap = taskItems.stream().collect(Collectors.toMap(TaskItem::getRealDate, Function.identity()));
        }
        List<DateItemResp> respList = new ArrayList<>();
        LocalDate startDate = task.getStartDate();
        LocalDate endDate = task.getEndDate().plusDays(1);
        while (startDate.isBefore(endDate)) {
            List<TaskStatistic> taskRecordList = taskRecordMap.getOrDefault(startDate, Collections.emptyList());
            DateItemResp resp = new DateItemResp();
            resp.setRecordDate(startDate);
            int doneNum = taskRecordList.stream().mapToInt(TaskStatistic::getRecordNum).sum();
            resp.setDoneRate(doneNum, (int) totalNum);
            resp.setTaskStatus(task.getStartDate(), task.getEndDate());
            TaskItem taskItem = taskItemMap.get(startDate);
            if (taskItem != null) {
                resp.setTaskType(taskItem.getTaskType());
            }
            respList.add(resp);
            startDate = startDate.plusDays(1);
        }
        return RespUtils.success(respList);
    }

    public CommResp<StudentResp> studentList(Long userId, Long taskId, LocalDate recordDate) {
        Task task = taskMapper.selectOne(new QueryWrapper<Task>().select(
                Task.ID, Task.TASK_TYPE, Task.SCHEDULE_ID, Task.CLAZZ_TYPE, Task.CREATED_USER_ID, Task.TASK_LEVEL
        ).eq(Task.ID, taskId));
        List<Order> orderList = getOrderList(task);
        return RespUtils.success(studentList(orderList, task, taskId, recordDate));
    }

    public CommResp<StudentResp> studentList(Long userId, Long teacherId, Long taskId, LocalDate recordDate) {
        Task task = taskMapper.selectOne(new QueryWrapper<Task>().select(
                Task.TASK_TYPE, Task.SCHEDULE_ID, Task.CLAZZ_TYPE
        ).eq(Task.ID, taskId));
        List<Order> orderList = getOrderList(userId, teacherId, task.getScheduleId(), taskId, task.getClazzType());
        return RespUtils.success(studentList(orderList, task, taskId, recordDate));
    }

    public StudentResp studentList(List<Order> orderList, Task task, Long taskId, LocalDate recordDate) {
        StudentResp resp = new StudentResp();
        if (TaskType.WORD.getCode().equals(task.getTaskType()) && recordDate != null) {
            List<TaskItem> taskItems = frontWordService.taskList(task.getScheduleId());
            TaskItem item = taskItems.stream().filter(taskItem -> recordDate.isEqual(taskItem.getRealDate())).findFirst().orElse(null);
            if (item == null) {
                return resp;
            }
            resp.setWordTaskType(item.getTaskType());
        }
        Map<Long, Clazz> clazzMap = getClazzMap(orderList);
        Map<Long, Group> groupMap = getGroupMap(orderList);
        Map<Long, Teacher> teacherMap = Collections.emptyMap();
        if (!clazzMap.isEmpty()) {
            Set<Long> teacherIds = clazzMap.values().stream().map(Clazz::getTeacherId).filter(Objects::nonNull).collect(Collectors.toSet());
            teacherMap = teacherMapper.selectList(new QueryWrapper<Teacher>().select(
                            Teacher.ID, Teacher.REAL_NAME
                    ).in(Teacher.ID, teacherIds)
            ).stream().collect(Collectors.toMap(Teacher::getId, Function.identity()));
        }
        List<Long> userIdList = orderList.stream().map(Order::getUserId).distinct().toList();
        Map<Long, User> userMap = getUserMap(userIdList);
        Map<Long, TaskSubmitRecord> submitOrderMap = getOrderIds(taskId, task.getTaskType(), recordDate, orderList.stream().map(Order::getId).collect(Collectors.toList()));
        List<Order> committedOrderList = new ArrayList<>(), unCommittedOrderList = new ArrayList<>(), outCommitedOrderList = new ArrayList<>();

        StringBuilder orderIdsStr = new StringBuilder();
        for (Order order : orderList) {
            orderIdsStr.append(order.getId()).append(",");
            TaskSubmitRecord record = submitOrderMap.get(order.getId());
            if (record == null) {
                unCommittedOrderList.add(order);
            } else {
                if (record.getSubmitDate().equals(record.getCreatedTime().toLocalDate())) {
                    committedOrderList.add(order);
                } else {
                    outCommitedOrderList.add(order);
                }
            }
        }

        Map<Long, JsonNode> wordMap = getWordMap(orderIdsStr);
        resp.setCommittedList(getClazzList(committedOrderList, teacherMap, clazzMap, groupMap, userMap, wordMap));
        resp.setUnCommittedList(getClazzList(unCommittedOrderList, teacherMap, clazzMap, groupMap, userMap, wordMap));
        resp.setTimeoutList(getClazzList(outCommitedOrderList, teacherMap, clazzMap, groupMap, userMap, wordMap));
        return resp;
    }

    public List<WrongDownloadResp> downloadWrongWordList(Long userId, Long taskId) {
        Task task = taskMapper.selectOne(new QueryWrapper<Task>().select(
                Task.TASK_TYPE, Task.SCHEDULE_ID, Task.CLAZZ_TYPE
        ).eq(Task.ID, taskId));
        List<Order> orderList = getOrderList(userId, task.getScheduleId(), taskId, task.getClazzType());
        if (orderList.isEmpty()) {
            return Collections.emptyList();
        }
        StringBuilder orderIdsStr = new StringBuilder();
        List<Long> userIds = new ArrayList<>();
        for (Order order : orderList) {
            orderIdsStr.append(order.getId()).append(",");
            userIds.add(order.getUserId());
        }
        Map<Long, Clazz> clazzMap = getClazzMap(orderList);
        Map<Long, Group> groupMap = getGroupMap(orderList);
        Map<Long, User> userMap = getUserMap(userIds);
        Map<Long, JsonNode> wordMap = getWordMap(orderIdsStr);
        List<WrongDownloadResp> respList = new ArrayList<>();
        for (Order order : orderList) {
            WrongDownloadResp resp = new WrongDownloadResp();
            if (StringUtils.hasLength(order.getOrderRemark())) {
                resp.setUserName(order.getOrderRemark());
            } else {
                User user = userMap.get(order.getUserId());
                resp.setUserName(user.getRealName());
            }
            Clazz clazz = clazzMap.get(order.getClazzId());
            if (clazz != null) {
                resp.setClazzName(clazz.getClassName());
            } else {
                resp.setClazzName("未分班");
            }
            Group group = groupMap.get(order.getGroupId());
            if (group != null) {
                resp.setGroupName(group.getGroupName());
            } else {
                resp.setGroupName("未分组");
            }
            JsonNode wrongNode = wordMap.get(order.getId());
            if (wrongNode != null) {
                resp.setWrongQueCnt(wrongNode.get("wrongQueCnt").asInt());
                resp.setWrongQueValidCnt(wrongNode.get("wrongQueValidCnt").asInt());
            } else {
                resp.setWrongQueCnt(0);
                resp.setWrongQueValidCnt(0);
            }
            respList.add(resp);
        }
        return respList;
    }

    private Map<Long, JsonNode> getWordMap(StringBuilder orderIdsStr) {
        if (orderIdsStr.isEmpty()) {
            return Collections.emptyMap();
        }
        HttpHeaders headers = new HttpHeaders();
        headers.set("Access-Key", "3447CB6560E99791");
        MultiValueMap<String, Object> formData = new LinkedMultiValueMap<>();
        formData.add("trainingOrderIds", orderIdsStr.substring(0, orderIdsStr.length() - 1));
        HttpEntity<Object> httpEntity = new HttpEntity<>(formData, headers);
        String url = "/traApi/admin/campStudy/wrongQuestionCensus";
        ResponseEntity<String> respEntity = restTemplate.exchange(baseUrl + url, HttpMethod.POST, httpEntity, String.class);
        JsonNode jsonNode = JsonUtils.parseJsonToJsonNode(respEntity.getBody());
        Map<Long, JsonNode> wordMap = new HashMap<>();
        if ("200".equals(jsonNode.get("code").asText())) {
            JsonNode dataList = jsonNode.get("data");
            for (JsonNode node : dataList) {
                wordMap.put(node.get("trainingOrderId").asLong(), node);
            }
        } else {
            log.error("请求url=[{}]错误", url);
        }
        return wordMap;
    }

    private List<Order> getOrderList(Long userId, Long teacherId, Long scheduleId, Long taskId, Byte clazzType) {
        if (teacherId != null) {
            ScheduleTeacher scheduleTeacher = scheduleTeacherMapper.selectOne(new QueryWrapper<ScheduleTeacher>().select(
                    ScheduleTeacher.TEACHER_ID, ScheduleTeacher.TEACHER_TYPE
            ).eq(ScheduleTeacher.ID, teacherId));
            return getOrderList(scheduleTeacher, scheduleId, taskId, clazzType);
        }
        return getOrderList(userId, scheduleId, taskId, clazzType);
    }

    private List<Order> getOrderList(Long userId, Long scheduleId, Long taskId, Byte clazzType) {
        Teacher teacher = teacherMapper.selectOne(new QueryWrapper<Teacher>().select(
                Teacher.ID
        ).eq(Teacher.USER_ID, userId));
        ScheduleTeacher scheduleTeacher = scheduleTeacherMapper.selectOne(new QueryWrapper<ScheduleTeacher>().select(
                ScheduleTeacher.TEACHER_ID, ScheduleTeacher.TEACHER_TYPE
        ).eq(ScheduleTeacher.SCHEDULE_ID, scheduleId).eq(ScheduleTeacher.TEACHER_ID, teacher.getId()));
        return getOrderList(scheduleTeacher, scheduleId, taskId, clazzType);
    }

    private List<Order> getOrderList(ScheduleTeacher scheduleTeacher, Long scheduleId, Long taskId, Byte clazzType) {
        List<Long> clazzIds;
        if (TeacherType.TEACHER.getCode().equals(scheduleTeacher.getTeacherType())) {
            clazzIds = clazzMapper.selectList(new QueryWrapper<Clazz>().select(Clazz.ID).eq(Clazz.SCHEDULE_ID, scheduleId)
                    .eq(Clazz.TEACHER_ID, scheduleTeacher.getTeacherId())).stream().map(Clazz::getId).toList();
        } else {
            clazzIds = clazzMapper.selectList(new QueryWrapper<Clazz>().select(Clazz.ID).eq(Clazz.ASSISTANT_ID, scheduleTeacher.getTeacherId())
                    .eq(Clazz.SCHEDULE_ID, scheduleId)).stream().map(Clazz::getId).toList();
        }
        if (clazzIds.isEmpty()) {
            return Collections.emptyList();
        }
        if (TaskClazzType.SPECIFIC.getCode().equals(clazzType)) {
            clazzIds = taskRelationMapper.selectList(new QueryWrapper<TaskRelation>().select(TaskRelation.CLAZZ_ID)
                    .eq(TaskRelation.TASK_ID, taskId).in(TaskRelation.CLAZZ_ID, clazzIds)).stream().map(TaskRelation::getClazzId).toList();
        }
        if (clazzIds.isEmpty()) {
            return Collections.emptyList();
        }
        return orderMapper.selectList(new QueryWrapper<Order>().select(
                Order.ID, Order.CLAZZ_ID, Order.TEACHER_ID, Order.USER_ID, Order.GROUP_ID, Order.ORDER_REMARK
        ).eq(Order.SCHEDULE_ID, scheduleId).in(Order.CLAZZ_ID, clazzIds).in(Order.ORDER_STATUS, 1, 4));
    }

    private Map<Long, Clazz> getClazzMap(List<Order> orderList) {
        List<Long> clazzIdList = orderList.stream().map(Order::getClazzId).distinct().toList();
        if (CollectionUtils.isEmpty(clazzIdList)) {
            return Collections.emptyMap();
        }
        return clazzMapper.selectList(new QueryWrapper<Clazz>().select(
                Clazz.ID, Clazz.CLASS_NAME, Clazz.TEACHER_ID
        ).in(Clazz.ID, clazzIdList)).stream().collect(Collectors.toMap(Clazz::getId, Function.identity()));
    }

    private Map<Long, User> getUserMap(List<Long> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return Collections.emptyMap();
        }
        return userMapper.selectList(new QueryWrapper<User>().select(
                User.ID, User.REAL_NAME, User.USER_ICON, User.PHONE_NUM
        ).in(User.ID, userIds)).stream().collect(Collectors.toMap(User::getId, Function.identity()));
    }

    private Map<Long, Group> getGroupMap(List<Order> orderList) {
        List<Long> groupIdList = orderList.stream().map(Order::getGroupId).distinct().toList();
        if (CollectionUtils.isEmpty(groupIdList)) {
            return Collections.emptyMap();
        }
        return groupMapper.selectList(new QueryWrapper<Group>().select(
                Group.ID, Group.GROUP_NAME
        ).in(Group.ID, groupIdList)).stream().collect(Collectors.toMap(Group::getId, Function.identity()));
    }

    private List<StudentResp.Clazz> getClazzList(List<Order> orderList,
                                                 Map<Long, Teacher> teacherMap,
                                                 Map<Long, Clazz> clazzMap,
                                                 Map<Long, Group> groupMap, Map<Long, User> userMap,
                                                 Map<Long, JsonNode> wordMap) {
        Map<Long, List<Order>> clazzOrderMap = orderList.stream().collect(Collectors.groupingBy(Order::getClazzId));
        List<StudentResp.Clazz> clazzList = new ArrayList<>();
        for (Map.Entry<Long, List<Order>> entry : clazzOrderMap.entrySet()) {
            Clazz clazz = clazzMap.get(entry.getKey());
            StudentResp.Clazz clazzResp = new StudentResp.Clazz();
            clazzResp.setId(clazz.getId());
            clazzResp.setClazzName(clazz.getClassName());
            Teacher teacher = teacherMap.get(clazz.getTeacherId());
            clazzResp.setTeacherName(teacher.getRealName());
            clazzResp.setTeacherId(clazz.getTeacherId());
            List<StudentResp.Group> groupList = new ArrayList<>();
            clazzResp.setGroupList(groupList);
            clazzList.add(clazzResp);
            Map<Long, List<Order>> groupOrderMap = entry.getValue().stream().filter(order -> order.getGroupId() != null)
                    .collect(Collectors.groupingBy(Order::getGroupId));
            List<Order> noGroupList = entry.getValue().stream().filter(order -> order.getGroupId() == null).toList();
            if (!noGroupList.isEmpty()) {
                groupOrderMap.put(null, noGroupList);
            }
            for (Map.Entry<Long, List<Order>> groupEntry : groupOrderMap.entrySet()) {
                StudentResp.Group groupResp = new StudentResp.Group();
                if (groupEntry.getKey() == null) {
                    groupResp.setId(-1L);
                    groupResp.setGroupName("未分组");
                } else {
                    Group group = groupMap.get(groupEntry.getKey());
                    groupResp.setId(group.getId());
                    groupResp.setGroupName(group.getGroupName());
                }
                groupResp.setStudentList(groupEntry.getValue().stream().map(order -> {
                    StudentResp.Student student = new StudentResp.Student();
                    User user = userMap.get(order.getUserId());
                    student.setId(user.getId());
                    student.setOrderId(order.getId());
                    student.setRealName(user.getRealName());
                    if (StringUtils.hasLength(order.getOrderRemark())) {
                        student.setRealName(order.getOrderRemark());
                    }
                    JsonNode word = wordMap.get(order.getId());
                    if (word != null) {
                        student.setWrongQueCnt(word.get("wrongQueCnt").asInt());
                        student.setWrongQueValidCnt(word.get("wrongQueValidCnt").asInt());
                    } else {
                        student.setWrongQueCnt(0);
                        student.setWrongQueValidCnt(0);
                    }
                    student.setUserIcon(user.getUserIcon());
                    student.setPhoneNo(user.getPhoneNum());
                    return student;
                }).collect(Collectors.toList()));
                groupList.add(groupResp);
            }
        }
        return clazzList;
    }

    private Map<Long, TaskSubmitRecord> getOrderIds(Long taskId, Byte taskType, LocalDate recordDate, List<Long> orderIds) {
        if (CollectionUtils.isEmpty(orderIds)) {
            return Collections.emptyMap();
        }
        return taskSubmitRecordMapper.selectList(new QueryWrapper<TaskSubmitRecord>().select(
                        TaskSubmitRecord.ORDER_ID, TaskSubmitRecord.SUBMIT_DATE, TaskSubmitRecord.CREATED_TIME
                ).eq(TaskSubmitRecord.TASK_ID, taskId)
                        .eq(!TaskType.HOMEWORK.getCode().equals(taskType), TaskSubmitRecord.SUBMIT_DATE, recordDate).in(TaskSubmitRecord.ORDER_ID, orderIds))
                .stream().collect(Collectors.toMap(TaskSubmitRecord::getOrderId, Function.identity()));
    }

    public CommResp<CommittedResp> committedList(Long userId, Long taskId, LocalDate recordDate) {
        Task task = taskMapper.selectOne(new QueryWrapper<Task>().select(
                Task.ID, Task.TASK_TYPE, Task.SCHEDULE_ID, Task.CLAZZ_TYPE, Task.CREATED_USER_ID, Task.TASK_LEVEL
        ).eq(Task.ID, taskId));
        List<Order> orderList = getOrderList(task);
        return RespUtils.success(committedList(orderList, task, taskId, recordDate));
    }

    public List<Order> getOrderList(Task task) {
        if (TaskLevel.SCHEDULE.getCode().equals(task.getTaskLevel())) {
            return orderMapper.selectList(new QueryWrapper<Order>().select(
                    Order.ID, Order.CLAZZ_ID, Order.TEACHER_ID, Order.USER_ID, Order.GROUP_ID, Order.ORDER_REMARK
            ).eq(Order.SCHEDULE_ID, task.getScheduleId()).isNotNull(Order.CLAZZ_ID).in(Order.ORDER_STATUS, 1, 4));
        } else {
            return getOrderList(task.getCreatedUserId(), task.getScheduleId(), task.getId(), task.getClazzType());
        }
    }

    public CommResp<CommittedResp> committedList(Long userId, Long teacherId, Long taskId, LocalDate recordDate) {
        Task task = taskMapper.selectOne(new QueryWrapper<Task>().select(
                Task.TASK_TYPE, Task.SCHEDULE_ID, Task.CLAZZ_TYPE
        ).eq(Task.ID, taskId));
        List<Order> orderList = getOrderList(userId, teacherId, task.getScheduleId(), taskId, task.getClazzType());
        return RespUtils.success(committedList(orderList, task, taskId, recordDate));
    }

    public CommittedResp committedList(List<Order> orderList, Task task, Long taskId, LocalDate recordDate) {
        if (orderList.isEmpty()) {
            return new CommittedResp();
        }
        Map<Long, Order> orderMap = orderList.stream().collect(Collectors.toMap(Order::getId, Function.identity()));
        Map<Long, Clazz> clazzMap = getClazzMap(orderList);
        Map<Long, Group> groupMap = getGroupMap(orderList);
        List<CommittedResp.Record> recordList = new ArrayList<>();
        if (TaskType.HOMEWORK.getCode().equals(task.getTaskType()) || TaskType.PUNCH.getCode().equals(task.getTaskType())) {
            List<Long> userIds = new ArrayList<>(orderList.stream().map(Order::getUserId).toList());
            List<TaskSubmitRecord> punchRecords = taskSubmitRecordMapper.selectList(new QueryWrapper<TaskSubmitRecord>().select(
                            TaskSubmitRecord.ORDER_ID, TaskSubmitRecord.LIKE_NUM, TaskSubmitRecord.SUBMIT_DATE, TaskSubmitRecord.SUBMIT_CONTENT,
                            TaskSubmitRecord.SUBMIT_URLS, TaskSubmitRecord.CREATED_TIME, TaskSubmitRecord.RECORD_TYPE, TaskSubmitRecord.ID
                    ).eq(TaskSubmitRecord.TASK_ID, taskId).in(TaskSubmitRecord.ORDER_ID, orderList.stream().map(Order::getId).toList())
                    .eq(recordDate != null, TaskSubmitRecord.SUBMIT_DATE, recordDate));
            List<TaskRecordRemark> remarkList = Collections.emptyList();
            if (!punchRecords.isEmpty()) {
                remarkList = taskRecordRemarkMapper.selectList(new QueryWrapper<TaskRecordRemark>().select(
                                TaskRecordRemark.ID, TaskRecordRemark.RECORD_ID, TaskRecordRemark.REMARK_CONTENT, TaskRecordRemark.REMARK_URLS,
                                TaskRecordRemark.CREATE_USER_ID, TaskRecordRemark.CREATED_TIME
                        ).in(TaskRecordRemark.RECORD_ID, punchRecords.stream().map(TaskSubmitRecord::getId).toList())
                );
                userIds.addAll(remarkList.stream().map(TaskRecordRemark::getCreateUserId).toList());
            }
           Map<Long, User> userMap = getUserMap(userIds);
            Map<Long, List<TaskRecordRemark>> recordRemarkMap = remarkList.stream().collect(Collectors.groupingBy(TaskRecordRemark::getRecordId));
            Map<Long, List<CommittedResp.Label>> labelMap = getLabelMap(remarkList);
            for (TaskSubmitRecord punchRecord : punchRecords) {
                CommittedResp.Record record = getRecord(punchRecord.getId(), punchRecord.getOrderId(),
                        orderMap, clazzMap, groupMap, userMap, recordRemarkMap, labelMap);
                record.setCreatedTime(punchRecord.getCreatedTime());
                record.setContent(punchRecord.getSubmitContent());
                record.setLikeNum(punchRecord.getLikeNum());
                record.setRecordType(punchRecord.getRecordType());
                record.setUrlList(MediaConvertor.getMediaList(punchRecord.getSubmitUrls()));
                recordList.add(record);
            }
        }
        CommittedResp committedResp = new CommittedResp();
        committedResp.setRemarkRecordList(recordList.stream().filter(record -> !CollectionUtils.isEmpty(record.getRemarkList())).toList());
        committedResp.setUnRemarkRecordList(recordList.stream().filter(record -> CollectionUtils.isEmpty(record.getRemarkList())).toList());
        return committedResp;
    }

    private CommittedResp.Record getRecord(
            Long recordId, Long orderId,
            Map<Long, Order> orderMap,
            Map<Long, Clazz> clazzMap,
            Map<Long, Group> groupMap,
            Map<Long, User> userMap,
            Map<Long, List<TaskRecordRemark>> recordRemarkMap,
            Map<Long, List<CommittedResp.Label>> labelMap
    ) {
        CommittedResp.Record record = new CommittedResp.Record();
        record.setRecordId(recordId);
        Order order = orderMap.get(orderId);
        Clazz clazz = clazzMap.get(order.getClazzId());
        Group group = groupMap.get(order.getGroupId());
        User user = userMap.get(order.getUserId());
        record.setClazzId(order.getClazzId());
        record.setTeacherId(order.getTeacherId());
        record.setGroupId(order.getGroupId());
        if (clazz != null) {
            record.setClazzName(clazz.getClassName());
        }
        if (group != null) {
            record.setGroupName(group.getGroupName());
        }
        if (user != null) {
            record.setRealName(user.getRealName());
            if (StringUtils.hasLength(order.getOrderRemark())) {
                record.setRealName(order.getOrderRemark());
            }
            record.setUserIcon(user.getUserIcon());
        }
        List<TaskRecordRemark> recordRemarkList = recordRemarkMap.get(recordId);
        if (!CollectionUtils.isEmpty(recordRemarkList)) {
            record.setRemarkList(recordRemarkList.stream().map(recordRemark -> {
                CommittedResp.Remark remark = new CommittedResp.Remark();
                remark.setCreatedTime(recordRemark.getCreatedTime());
                remark.setUserId(recordRemark.getCreateUserId());
                User remarkUser = userMap.get(recordRemark.getCreateUserId());
                remark.setRealName(remarkUser.getRealName());
                remark.setUserIcon(remarkUser.getUserIcon());
                remark.setRemarkContent(recordRemark.getRemarkContent());
                remark.setRemarkUrlList(MediaConvertor.getMediaList(recordRemark.getRemarkUrls()));
                remark.setLabelList(labelMap.get(recordRemark.getId()));
                return remark;
            }).toList());
            record.setRemarkNum(recordRemarkList.size());
        } else {
            record.setRemarkNum(0);
        }
        return record;
    }

    private Map<Long, List<CommittedResp.Label>> getLabelMap(List<TaskRecordRemark> remarkList) {
        if (remarkList.isEmpty()) {
            return Collections.emptyMap();
        }
        List<Long> remarkIds = remarkList.stream().map(TaskRecordRemark::getId).toList();
        List<TaskRecordLabel> labelList = taskRecordLabelMapper.selectList(new QueryWrapper<TaskRecordLabel>().select(
                TaskRecordLabel.REMARK_ID, TaskRecordLabel.LABEL_ID
        ).in(TaskRecordLabel.REMARK_ID, remarkIds));
        if (labelList.isEmpty()) {
            return Collections.emptyMap();
        }
        List<Long> labelIds = labelList.stream().map(TaskRecordLabel::getLabelId).distinct().toList();
        Map<Long, Label> labelMap = labelMapper.selectList(new QueryWrapper<Label>().select(
                Label.ID, Label.LABEL_NAME, Label.MEDIA_URL
        ).in(Label.ID, labelIds)).stream().collect(Collectors.toMap(Label::getId, Function.identity()));
        return labelList.stream().collect(Collectors.groupingBy(TaskRecordLabel::getRemarkId, Collectors.mapping(taskRecordLabel -> {
            Label label = labelMap.get(taskRecordLabel.getLabelId());
            CommittedResp.Label respLabel = new CommittedResp.Label();
            respLabel.setId(label.getId());
            respLabel.setLabelName(label.getLabelName());
            respLabel.setMediaUrl(MediaConvertor.getMediaUrl(label.getMediaUrl()));
            return respLabel;
        }, Collectors.toList())));
    }

    public CommResp<?> studyReport(Long orderId, LocalDate reportDate) {
        Order order = orderMapper.selectOne(new QueryWrapper<Order>().select(
                Order.ID, Order.USER_ID, Order.SCHEDULE_ID
        ).eq(Order.ID, orderId));

        List<TaskItem> itemList = frontWordService.taskList(order.getScheduleId());
        TaskItem taskItem = itemList.stream().filter(item -> item.getRealDate().equals(reportDate))
                .findFirst().orElseThrow(() -> new WebBaseException(RespMetaEnum.PARAM_ERROR));
        if (taskItem == null) {
            return RespUtils.success(null);
        }
        HttpHeaders headers = new HttpHeaders();
        headers.set("Access-Key", "3447CB6560E99791");
        HttpEntity<Object> httpEntity = new HttpEntity<>(headers);
        String url = String.format("/traApi/admin/campStudy/day/studyReport?trainingOrderId=%d&uid=%d&day=%d",
                orderId, order.getUserId(), taskItem.getDays());
        ResponseEntity<String> resp = restTemplate.exchange(baseUrl + url, HttpMethod.GET, httpEntity, String.class);
        JsonNode jsonNode = JsonUtils.parseJsonToJsonNode(resp.getBody());
        return RespUtils.success(jsonNode.get("data"));
    }

    public void downloadStudyReport(Long userId, Long scheduleId, Long orderId, LocalDate date, HttpServletResponse response) throws IOException {
        HttpHeaders headers = new HttpHeaders();
        headers.set("Access-Key", "3447CB6560E99791");
        HttpEntity<Object> httpEntity = new HttpEntity<>(headers);
        response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
        if (orderId == null) {
            Teacher teacher = teacherMapper.selectOne(new QueryWrapper<Teacher>().select(
                    Teacher.ID, Teacher.REAL_NAME
            ).eq(Teacher.USER_ID, userId));
            ScheduleTeacher scheduleTeacher = scheduleTeacherMapper.selectOne(new QueryWrapper<ScheduleTeacher>().select(
                    ScheduleTeacher.ID, ScheduleTeacher.TEACHER_TYPE
            ).eq(ScheduleTeacher.SCHEDULE_ID, scheduleId).eq(ScheduleTeacher.TEACHER_ID, teacher.getId()));
            Map<Long, Clazz> clazzMap;
            if (TeacherType.TEACHER.getCode().equals(scheduleTeacher.getTeacherType())) {
                clazzMap = clazzMapper.selectList(new QueryWrapper<Clazz>().select(
                        Clazz.ID, Clazz.CLASS_NAME
                ).eq(Clazz.SCHEDULE_ID, scheduleId).eq(Clazz.TEACHER_ID, teacher.getId())).stream().collect(Collectors.toMap(Clazz::getId, Function.identity()));
            } else {
               clazzMap = clazzMapper.selectList(new QueryWrapper<Clazz>().select(
                        Clazz.ID, Clazz.CLASS_NAME
                ).eq(Clazz.SCHEDULE_ID, scheduleId).eq(Clazz.ASSISTANT_ID, teacher.getId())).stream().collect(Collectors.toMap(Clazz::getId, Function.identity()));
            }
            if (clazzMap.isEmpty()) {
                downloadEmptyFile(ReportDownloadResp.class, response);
                return;
            }
            Map<Long, Order> orderMap = orderMapper.selectList(new QueryWrapper<Order>().select(
                    Order.ID, Order.USER_ID, Order.GROUP_ID, Order.CLAZZ_ID, Order.ORDER_REMARK, Order.TEACHER_ID
            ).eq(Order.SCHEDULE_ID, scheduleId).in(Order.CLAZZ_ID, clazzMap.keySet())).stream().collect(Collectors.toMap(Order::getId, Function.identity()));
            downloadStudyReport(scheduleId, clazzMap, orderMap, date, response);
        } else {
            Order order = orderMapper.selectOne(new QueryWrapper<Order>().select(
                    Order.ID, Order.USER_ID, Order.ORDER_REMARK, Order.SCHEDULE_ID
            ).eq(Order.ID, orderId));
            User user = userMapper.selectOne(new QueryWrapper<User>().select(
                    User.ID, User.PHONE_NUM, User.REAL_NAME
            ).eq(User.ID, userId));
            List<TaskItem> taskItems = frontWordService.taskList(order.getScheduleId());
            String url = String.format("/traApi/admin/campStudy/student/studyReportAll?trainingOrderId=%d", orderId);
            ResponseEntity<String> resp = restTemplate.exchange(baseUrl + url, HttpMethod.GET, httpEntity, String.class);
            String body = resp.getBody();
            JsonNode jsonNode = JsonUtils.parseJsonToJsonNode(body);
            if ("200".equals(jsonNode.get("code").asText())) {
                JsonNode dataList = jsonNode.get("data");
                Map<Integer, OrderDownloadResp> taskStateMap = new HashMap<>();
                for (JsonNode node : dataList) {
                    OrderDownloadResp data = new OrderDownloadResp();
                    data.setDate(node.get("createDate").asText());
                    data.setTaskState(node.get("taskState").asInt() == 2 ? "是" : "否");
                    data.setCorrRate(node.get("corrRate").asInt() + "%");
                    int step2QueCnt = node.get("step2QueCnt").asInt();
                    int step2CorrQueCnt = node.get("step2CorrQueCnt").asInt();
                    data.setStep2Que("对" + step2CorrQueCnt + " 错" + (step2QueCnt - step2CorrQueCnt));
                    int step3QueCnt = node.get("step3QueCnt").asInt();
                    int step3CorrQueCnt = node.get("step3CorrQueCnt").asInt();
                    data.setStep3Corr("对" + step3CorrQueCnt + " 错" + (step3QueCnt - step3CorrQueCnt));
                    Integer day = node.get("day").asInt();
                    taskStateMap.put(day, data);
                }
                List<OrderDownloadResp> respList = new ArrayList<>(taskItems.size());
                for (TaskItem item : taskItems) {
                    if (item.getTaskType() == 3 || item.getRealDate().isAfter(LocalDate.now())) {
                        continue;
                    }
                    OrderDownloadResp data = taskStateMap.get(item.getDays());
                    if (data == null) {
                        data = new OrderDownloadResp();
                        data.setTaskState("否");
                        data.setCorrRate("-");
                        data.setStep2Que("-");
                        data.setStep3Corr("-");
                    }
                    data.setDate(DateUtils.format(item.getRealDate()));
                    respList.add(data);
                }
                Schedule schedule = scheduleMapper.selectOne(new QueryWrapper<Schedule>().select(
                        Schedule.ID, Schedule.SCHEDULE_NAME
                ).eq(Schedule.ID, order.getScheduleId()));
                String name = schedule.getScheduleName() + "_" + user.getPhoneNum() + "_" + (StringUtils.hasLength(order.getOrderRemark()) ? order.getOrderRemark() : user.getRealName());
                response.setHeader("Content-disposition", "attachment;filename=" + URLEncoder.encode(name, StandardCharsets.UTF_8) +".xlsx");
                EasyExcel.write(response.getOutputStream(), OrderDownloadResp.class).sheet(name).doWrite(respList);
            } else {
                log.error("请求错误, body=[{}]",body);
                downloadEmptyFile(OrderDownloadResp.class, response);
            }
        }
    }

    public void downloadStudyReport1(Long scheduleId, LocalDate date, HttpServletResponse response) throws IOException {
        response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
        Map<Long, Clazz> clazzMap = clazzMapper.selectList(new QueryWrapper<Clazz>().select(
                Clazz.ID, Clazz.CLASS_NAME
        ).eq(Clazz.SCHEDULE_ID, scheduleId)).stream().collect(Collectors.toMap(Clazz::getId, Function.identity()));
        if (clazzMap.isEmpty()) {
            downloadEmptyFile(ReportDownloadResp.class, response);
            return;
        }
        Map<Long, Order> orderMap = orderMapper.selectList(new QueryWrapper<Order>().select(
                Order.ID, Order.USER_ID, Order.GROUP_ID, Order.CLAZZ_ID, Order.ORDER_REMARK, Order.TEACHER_ID
        ).eq(Order.SCHEDULE_ID, scheduleId)).stream().collect(Collectors.toMap(Order::getId, Function.identity()));
        downloadStudyReport(scheduleId, clazzMap, orderMap, date, response);
    }

    private void downloadStudyReport(Long scheduleId, Map<Long, Clazz> clazzMap, Map<Long, Order> orderMap, LocalDate date, HttpServletResponse response) throws IOException {
        if (orderMap.isEmpty()) {
            downloadEmptyFile(ReportDownloadResp.class, response);
            return;
        }
        Schedule schedule = scheduleMapper.selectOne(new QueryWrapper<Schedule>().select(
                Schedule.ID, Schedule.SCHEDULE_NAME, Schedule.START_TIME, Schedule.END_TIME
        ).eq(Schedule.ID, scheduleId));
        HttpHeaders headers = new HttpHeaders();
        headers.set("Access-Key", "3447CB6560E99791");
        HttpEntity<Object> httpEntity = new HttpEntity<>(headers);
        List<TaskItem> taskItems = frontWordService.taskList(schedule);
        Map<Long, Group> groupMap = groupMapper.selectList(new QueryWrapper<Group>().select(
                Group.ID, Group.GROUP_NAME
        ).in(Group.CLAZZ_ID, clazzMap.keySet())).stream().collect(Collectors.toMap(Group::getId, Function.identity()));
        Map<Long, Teacher> teacherMap = teacherMapper.selectList(new QueryWrapper<Teacher>().select(
                Teacher.ID, Teacher.REAL_NAME
        ).in(Teacher.ID, orderMap.values().stream().map(Order::getTeacherId).filter(Objects::nonNull).distinct().toList()))
                .stream().collect(Collectors.toMap(Teacher::getId, Function.identity()));
        Map<Long, User> userMap = userMapper.selectList(new QueryWrapper<User>().select(
                User.ID, User.REAL_NAME, User.PHONE_NUM
        ).in(User.ID, orderMap.values().stream().map(Order::getUserId).distinct().toList())).stream().collect(Collectors.toMap(User::getId, Function.identity()));
        StringBuilder orderIds = new StringBuilder();
        for (Long id : orderMap.keySet()) {
            orderIds.append(id).append(",");
        }
        TaskItem taskItem = taskItems.stream().filter(item -> item.getRealDate().equals(date)).findFirst()
                .orElseThrow(() -> new WebBaseException(RespMetaEnum.PARAM_ERROR));
        String url = String.format("/traApi/admin/campStudy/students/studyReportDay?trainingOrderIds=%s&day=%d",
                orderIds.substring(0, orderIds.length() - 1), taskItem.getDays());
        ResponseEntity<String> resp = restTemplate.exchange(baseUrl + url, HttpMethod.GET, httpEntity, String.class);
        String body = resp.getBody();
        JsonNode jsonNode = JsonUtils.parseJsonToJsonNode(body);
        if ("200".equals(jsonNode.get("code").asText())) {
            JsonNode dataList = jsonNode.get("data");
            List<ReportDownloadResp> respList = new ArrayList<>(orderMap.size());
            for (JsonNode node : dataList) {
                ReportDownloadResp data = new ReportDownloadResp();
                Long id = node.get("trainingOrderId").asLong();
                Order order = orderMap.remove(id);
                if (order.getTeacherId() != null) {
                    data.setTeacherName(teacherMap.get(order.getTeacherId()).getRealName());
                }
                Clazz clazz = clazzMap.get(order.getClazzId());
                if (clazz == null) {
                    data.setClazzName("-");
                } else {
                    data.setClazzName(clazz.getClassName());
                }
                Group group = groupMap.get(order.getGroupId());
                if (group == null) {
                    data.setGroupName("-");
                } else {
                    data.setGroupName(group.getGroupName());
                }
                User user = userMap.get(order.getUserId());
                data.setPhoneNo(String.valueOf(user.getPhoneNum()));
                data.setNickName(StringUtils.hasLength(order.getOrderRemark()) ? order.getOrderRemark() : user.getRealName());
                data.setTaskState(node.get("taskState").asInt() == 2 ? "是" : "否");
                data.setCorrRate(node.get("corrRate").asInt() + "%");
                int step2QueCnt = node.get("step2QueCnt").asInt();
                int step2CorrQueCnt = node.get("step2CorrQueCnt").asInt();
                data.setStep2Que("对" + step2CorrQueCnt + " 错" + (step2QueCnt - step2CorrQueCnt));
                int step3QueCnt = node.get("step3QueCnt").asInt();
                int step3CorrQueCnt = node.get("step3CorrQueCnt").asInt();
                data.setStep3Corr("对" + step3CorrQueCnt + " 错" + (step3QueCnt - step3CorrQueCnt));
                respList.add(data);
            }
            for (Order order : orderMap.values()) {
                ReportDownloadResp data = new ReportDownloadResp();
                Teacher teacher = teacherMap.get(order.getTeacherId());
                if (teacher == null) {
                    data.setTeacherName("-");
                } else {
                    data.setTeacherName(teacher.getRealName());
                }
                Clazz clazz = clazzMap.get(order.getClazzId());
                if (clazz == null) {
                    data.setClazzName("-");
                } else {
                    data.setClazzName(clazz.getClassName());
                }
                Group group = groupMap.get(order.getGroupId());
                if (group == null) {
                    data.setGroupName("-");
                } else {
                    data.setGroupName(group.getGroupName());
                }
                User user = userMap.get(order.getUserId());
                data.setPhoneNo(String.valueOf(user.getPhoneNum()));
                data.setNickName(StringUtils.hasLength(order.getOrderRemark()) ? order.getOrderRemark() : user.getRealName());
                data.setTaskState("否");
                data.setCorrRate("-");
                data.setStep2Que("-");
                data.setStep3Corr("-");
                respList.add(data);
            }
            String name = schedule.getScheduleName() + "_" + DateUtils.format(date);
            response.setHeader("Content-disposition", "attachment;filename=" + URLEncoder.encode(name, StandardCharsets.UTF_8) +".xlsx");
            EasyExcel.write(response.getOutputStream(), ReportDownloadResp.class).sheet(name).doWrite(respList);
        } else {
            log.error("获取训练营学习报告失败：{}", body);
            downloadEmptyFile(ReportDownloadResp.class, response);
        }
    }

    /**
     * 下载空文件
     */
    public void downloadEmptyFile(Class<?> clazz, HttpServletResponse response) throws IOException {
        response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
        response.setHeader("Content-disposition", "attachment;filename=" + URLEncoder.encode("空文件", StandardCharsets.UTF_8) +".xlsx");
        EasyExcel.write(response.getOutputStream(), clazz).sheet("空文件").doWrite(Collections.emptyList());
    }
}
