package com.fuyingedu.training.admin.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.fuyingedu.training.admin.model.wx.ExternalUserResp;
import com.fuyingedu.training.admin.model.wx.RemarkReq;
import com.fuyingedu.training.admin.model.wx.StatusResp;
import com.fuyingedu.training.common.enums.TeacherType;
import com.fuyingedu.training.common.enums.WxStatus;
import com.fuyingedu.training.common.exception.WebBaseException;
import com.fuyingedu.training.common.model.CommResp;
import com.fuyingedu.training.common.util.DateUtils;
import com.fuyingedu.training.common.util.RedisKey;
import com.fuyingedu.training.common.util.RespUtils;
import com.fuyingedu.training.entity.*;
import com.fuyingedu.training.front.feign.FuyingUserFeign;
import com.fuyingedu.training.front.model.feign.UnionItem;
import com.fuyingedu.training.mapper.*;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.cp.api.WxCpService;
import me.chanjar.weixin.cp.bean.external.*;
import me.chanjar.weixin.cp.bean.external.contact.ExternalContact;
import me.chanjar.weixin.cp.bean.external.contact.FollowedUser;
import me.chanjar.weixin.cp.bean.external.contact.WxCpExternalContactBatchInfo;
import me.chanjar.weixin.cp.bean.external.contact.WxCpExternalContactInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Slf4j
public class WxEnterpriseService {

    @Autowired
    private WxCpService wxCpService;
    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private UserMapper userMapper;
    @Autowired
    private TeacherMapper teacherMapper;
    @Autowired
    private WxExternalUserMapper wxExternalUserMapper;
    @Autowired
    private ClazzMapper clazzMapper;
    @Autowired
    private UserWxGroupMapper userWxGroupMapper;
    @Autowired
    private RedisTemplate<String, String> redisTemplate;
    @Autowired
    private FuyingUserFeign fuyingUserFeign;

    @Value("${spring.profiles.active}")
    private String profiles;

    private String getUserId(String phoneNum) throws WxErrorException {
        return wxCpService.getUserService().getUserId(phoneNum);
    }

    private List<String> contactList(String userId) throws WxErrorException {
            return wxCpService.getExternalContactService().listExternalContacts(userId);
    }

    private List<String> wxGroupList(String phoneNum) throws WxErrorException {
        String userId = getUserId(phoneNum);
        WxCpUserExternalGroupChatList groupList = wxCpService.getExternalContactService().listGroupChat(1000, null, 0, new String[]{userId});
        return groupList.getGroupChatList().stream().map(WxCpUserExternalGroupChatList.ChatStatus::getChatId).toList();
    }

    private WxCpUserExternalGroupChatInfo.GroupChat groupChatDetail(String chatId) throws WxErrorException {
        WxCpUserExternalGroupChatInfo groupChat = wxCpService.getExternalContactService().getGroupChat(chatId, 0);
        return groupChat.getGroupChat();
    }

    private WxCpExternalContactInfo contactInfo(String externalUserId) {
        try {
            return wxCpService.getExternalContactService()
                    .getContactDetail(externalUserId, "");
        } catch (WxErrorException e) {
            throw new WebBaseException(500, "企业微信接口异常", e);
        }
    }

    private List<WxCpExternalContactBatchInfo.ExternalContactInfo> contactInfoList(String userId) {
        List<WxCpExternalContactBatchInfo.ExternalContactInfo> contactInfoList = new ArrayList<>();
        String cursor = "";
        do {
            try {
                WxCpExternalContactBatchInfo contactBatchInfo = wxCpService.getExternalContactService()
                        .getContactDetailBatch(new String[]{userId}, cursor, 100);
                contactInfoList.addAll(contactBatchInfo.getExternalContactList());
                cursor = contactBatchInfo.getNextCursor();
            } catch (WxErrorException e) {
                throw new WebBaseException(500, "企业微信接口异常", e);
            }
        } while (StringUtils.hasLength(cursor));
        return contactInfoList;

    }

    public void remark(String userId, String externalUserId, String remark, String description) {
        try {
            WxCpUpdateRemarkRequest request = new WxCpUpdateRemarkRequest();
            request.setUserId(userId);
            request.setExternalUserId(externalUserId);
            request.setRemark(remark);
            request.setDescription(description);
            wxCpService.getExternalContactService().updateRemark(request);
        } catch (WxErrorException e) {
            throw new WebBaseException(500, "企业微信接口异常", e);
        }
    }

    private Byte getWxStatus(Long teacherId, String unionId) throws WxErrorException {
        Teacher teacher = teacherMapper.selectOne(new QueryWrapper<Teacher>().select(
                Teacher.USER_ID
        ).eq(Teacher.ID, teacherId));
        User user = userMapper.selectOne(new QueryWrapper<User>().select(
                User.PHONE_NUM
        ).eq(User.ID, teacher.getUserId()));
        String userId = getUserId(String.valueOf(user.getPhoneNum()));
        WxExternalUser wxExternalUser = wxExternalUserMapper.selectOne(new QueryWrapper<WxExternalUser>().select(
                WxExternalUser.EXTERNAL_USER_ID
        ).eq(WxExternalUser.USER_ID, userId).eq(WxExternalUser.UNION_ID, unionId).last("limit 1"));
        if (wxExternalUser != null) {
            return WxStatus.ADDED.getCode();
        }
        List<WxExternalUser> externalUserList = wxExternalUserMapper.selectList(new QueryWrapper<WxExternalUser>().select(
                WxExternalUser.EXTERNAL_USER_ID, WxExternalUser.UNION_ID
        ).eq(WxExternalUser.USER_ID, userId));
        Set<String> unionIdSet = externalUserList.stream().map(WxExternalUser::getUnionId).collect(Collectors.toSet());
        if (!unionIdSet.contains(unionId)) {
            unionIdSet = getContactList(userId, externalUserList);
        }
        if (unionIdSet.contains(unionId)) {
            return WxStatus.ADDED.getCode();
        }
        return null;
    }

    private void remark(Long teacherId, String unionId, RemarkReq remarkReq) throws WxErrorException {
        Teacher teacher = teacherMapper.selectOne(new QueryWrapper<Teacher>().select(
                Teacher.USER_ID
        ).eq(Teacher.ID, teacherId));
        User teacherUser = userMapper.selectOne(new QueryWrapper<User>().select(
                User.PHONE_NUM
        ).eq(User.ID, teacher.getUserId()));
        String userId = getUserId(String.valueOf(teacherUser.getPhoneNum()));
        WxExternalUser wxExternalUser = wxExternalUserMapper.selectOne(new QueryWrapper<WxExternalUser>().select(
                WxExternalUser.EXTERNAL_USER_ID
        ).eq(WxExternalUser.USER_ID, userId).eq(WxExternalUser.UNION_ID, unionId));
        remark(userId, wxExternalUser.getExternalUserId(),
                remarkReq.getRemark(), remarkReq.getDescription());
    }

    public void remark(RemarkReq remarkReq) throws WxErrorException {
        Order order = orderMapper.selectOne(new QueryWrapper<Order>().select(
                Order.USER_ID, Order.CLAZZ_ID, Order.TEACHER_ID, Order.TEACHER_WX_STATUS, Order.ASSISTANT_WX_STATUS
        ).eq(Order.ID, remarkReq.getOrderId()));
        User user = userMapper.selectOne(new QueryWrapper<User>().select(
                User.UNION_ID
        ).eq(User.ID, order.getUserId()));
        if (TeacherType.TEACHER.getCode().equals(remarkReq.getTeacherType())) {
            if (order.getTeacherId() != null && WxStatus.ADDED.getCode().equals(order.getTeacherWxStatus())) {
                remark(order.getTeacherId(), user.getUnionId(), remarkReq);
           }
        } else if (TeacherType.ASSISTANT.getCode().equals(remarkReq.getTeacherType())) {
            if (order.getClazzId() != null && WxStatus.ADDED.getCode().equals(order.getAssistantWxStatus())) {
               Clazz clazz = clazzMapper.selectOne(new QueryWrapper<Clazz>().select(
                        Clazz.ASSISTANT_ID
                ).eq(Clazz.ID, order.getClazzId()));
                remark(clazz.getAssistantId(), user.getUnionId(), remarkReq);
            }
        }
    }

    public CommResp<StatusResp> flushWxStatus(Long orderId) throws WxErrorException {
        if (!"prod".equals(profiles)) {
            return RespUtils.success(null);
        }
        Order order = orderMapper.selectOne(new QueryWrapper<Order>().select(
                Order.USER_ID, Order.CLAZZ_ID, Order.TEACHER_ID, Order.TEACHER_WX_STATUS, Order.ASSISTANT_WX_STATUS, Order.GROUP_WX_STATUS
        ).eq(Order.ID, orderId));
        User user = userMapper.selectOne(new QueryWrapper<User>().select(
                User.UNION_ID, User.PHONE_NUM
        ).eq(User.ID, order.getUserId()));
        Byte teacherWxStatus = null, assistantWxStatus = null, groupWxStatus = null;
        if (order.getTeacherId() != null && WxStatus.NOT_ADD.getCode().equals(order.getTeacherWxStatus())) {
            try {
                teacherWxStatus = getWxStatus(order.getTeacherId(), user.getUnionId());
            } catch (WxErrorException e) {
                log.error("flushWxStatus", e);
            }
        }
        if (order.getClazzId() != null) {
            Clazz clazz = clazzMapper.selectOne(new QueryWrapper<Clazz>().select(
                    Clazz.ASSISTANT_ID, Clazz.WX_GROUP_ID
            ).eq(Clazz.ID, order.getClazzId()));
            if (WxStatus.NOT_ADD.getCode().equals(order.getAssistantWxStatus())) {
                try {
                    assistantWxStatus = getWxStatus(clazz.getAssistantId(), user.getUnionId());
                } catch (WxErrorException e) {
                    log.error("flushWxStatus", e);
                }
            }
            if (clazz.getWxGroupId() != null && WxStatus.NOT_ADD.getCode().equals(order.getGroupWxStatus())) {
                UserWxGroup wxGroup = userWxGroupMapper.selectOne(new QueryWrapper<UserWxGroup>().select(UserWxGroup.GROUP_ID).eq(UserWxGroup.ID, clazz.getWxGroupId()));
                try {
                    WxCpUserExternalGroupChatInfo.GroupChat groupChat = groupChatDetail(wxGroup.getGroupId());
                    List<WxCpUserExternalGroupChatInfo.GroupMember> memberList = groupChat.getMemberList();
                    for (WxCpUserExternalGroupChatInfo.GroupMember member : memberList) {
                        if (member.getUnionId() != null && member.getUnionId().equals(user.getUnionId())) {
                            groupWxStatus = WxStatus.ADDED.getCode();
                            break;
                        }
                    }
                } catch (WxErrorException e) {
                    log.error("groupChatDetail", e);
                }
            }
        }
        if (teacherWxStatus != null || assistantWxStatus != null || groupWxStatus != null) {
            orderMapper.update(new UpdateWrapper<Order>()
                    .set(teacherWxStatus != null, Order.TEACHER_WX_STATUS, teacherWxStatus)
                    .set(assistantWxStatus != null, Order.ASSISTANT_WX_STATUS, assistantWxStatus)
                    .set(groupWxStatus != null, Order.GROUP_WX_STATUS, groupWxStatus)
                    .eq(Order.ID, orderId));
        }
        StatusResp statusResp = new StatusResp();
        statusResp.setTeacherWxStatus(teacherWxStatus != null ? teacherWxStatus : order.getTeacherWxStatus());
        statusResp.setAssistantWxStatus(assistantWxStatus != null ? assistantWxStatus : order.getAssistantWxStatus());
        return RespUtils.success(statusResp);
    }
    private Set<String> getContactList(String userId, List<WxExternalUser> externalUserList) throws WxErrorException {
        Map<String, WxExternalUser> externalUserMap = externalUserList.stream()
                .collect(Collectors.toMap(WxExternalUser::getExternalUserId, v -> v, (v1, v2) -> v2));
        List<WxExternalUser> insertList = new ArrayList<>();
        Set<String> unionIds = new HashSet<>();
        if (externalUserList.size() < 100) {
            List<WxCpExternalContactBatchInfo.ExternalContactInfo> contactInfoList = contactInfoList(userId);
            if (CollectionUtils.isEmpty(contactInfoList)) {
                return Collections.emptySet();
            }
            for (WxCpExternalContactBatchInfo.ExternalContactInfo contactInfo : contactInfoList) {
                String externalUserId = contactInfo.getExternalContact().getExternalUserId();
                WxExternalUser externalUser = externalUserMap.get(externalUserId);
                if (externalUser != null) {
                    unionIds.add(externalUser.getUnionId());
                    continue;
                }
                insertList.add(getExternalUser(userId, externalUserId, contactInfo.getExternalContact().getUnionId(),
                        contactInfo.getExternalContact().getType()));
                unionIds.add(contactInfo.getExternalContact().getUnionId());
            }
        } else {
            List<String> externalUserIdList = contactList(userId);
            if (CollectionUtils.isEmpty(externalUserIdList)) {
                return Collections.emptySet();
            }
            for (String externalId : externalUserIdList) {
                WxExternalUser externalUser = externalUserMap.get(externalId);
                if (externalUser != null) {
                    unionIds.add(externalUser.getUnionId());
                    continue;
                }
                WxCpExternalContactInfo contactInfo = contactInfo(externalId);
                if (contactInfo == null) {
                    continue;
                }
                ExternalContact externalContact = contactInfo.getExternalContact();
                if (externalContact != null) {
                    insertList.add(getExternalUser(userId, externalId, contactInfo.getExternalContact().getUnionId(),
                            contactInfo.getExternalContact().getType()));
                    unionIds.add(contactInfo.getExternalContact().getUnionId());
                }
            }
        }
        if (!insertList.isEmpty()) {
            wxExternalUserMapper.insert(insertList);
        }
        return unionIds;
    }

    private WxExternalUser getExternalUser(String userId, String externalUserId, String unionId, int type) {
        WxExternalUser externalUser = new WxExternalUser();
        externalUser.setUserId(userId);
        externalUser.setExternalUserId(externalUserId);
        externalUser.setUnionId(unionId);
        externalUser.setType((byte) type);
        return externalUser;
    }

    public CommResp<ExternalUserResp> getUserInfo(Long orderId, Byte teacherType) throws WxErrorException {
        if (!"prod".equals(profiles)) {
            return RespUtils.success(null);
        }
        Order order = orderMapper.selectOne(new QueryWrapper<Order>().select(
                Order.USER_ID, Order.CLAZZ_ID, Order.TEACHER_ID, Order.TEACHER_WX_STATUS, Order.ASSISTANT_WX_STATUS
        ).eq(Order.ID, orderId));
        User user = userMapper.selectOne(new QueryWrapper<User>().select(
                User.UNION_ID
        ).eq(User.ID, order.getUserId()));
        Long teacherId = null;
        if (TeacherType.TEACHER.getCode().equals(teacherType)) {
            if (order.getTeacherId() != null && WxStatus.ADDED.getCode().equals(order.getTeacherWxStatus())) {
                teacherId = order.getTeacherId();
            }
        } else if (TeacherType.ASSISTANT.getCode().equals(teacherType)) {
            if (order.getClazzId() != null && WxStatus.ADDED.getCode().equals(order.getAssistantWxStatus())) {
                Clazz clazz = clazzMapper.selectOne(new QueryWrapper<Clazz>().select(
                        Clazz.ASSISTANT_ID
                ).eq(Clazz.ID, order.getClazzId()));
                teacherId = clazz.getAssistantId();
            }
        }
        ExternalUserResp externalUserResp = new ExternalUserResp();
        if (teacherId == null) {
            return RespUtils.success(externalUserResp);
        }
        Teacher teacher = teacherMapper.selectOne(new QueryWrapper<Teacher>().select(
                Teacher.USER_ID
        ).eq(Teacher.ID, teacherId));
        User teacherUser = userMapper.selectOne(new QueryWrapper<User>().select(
                User.PHONE_NUM
        ).eq(User.ID, teacher.getUserId()));
        String userId = getUserId(String.valueOf(teacherUser.getPhoneNum()));
        WxExternalUser wxExternalUser = wxExternalUserMapper.selectOne(new QueryWrapper<WxExternalUser>().select(
                        WxExternalUser.EXTERNAL_USER_ID
                ).eq(WxExternalUser.USER_ID, userId).eq(WxExternalUser.UNION_ID, user.getUnionId()).last("limit 1"));
        WxCpExternalContactInfo contactInfo = contactInfo(wxExternalUser.getExternalUserId());
        if (contactInfo != null) {
            externalUserResp.setName(contactInfo.getExternalContact().getName());
            externalUserResp.setNickname(contactInfo.getExternalContact().getNickname());
            externalUserResp.setAvatar(contactInfo.getExternalContact().getAvatar());
            List<FollowedUser> followedUsers = contactInfo.getFollowedUsers();
            if (followedUsers != null) {
                followedUsers = followedUsers.stream().filter(v -> v.getUserId().equals(userId)).toList();
                if (!followedUsers.isEmpty()) {
                    FollowedUser followedUser = followedUsers.get(0);
                    externalUserResp.setRemark(followedUser.getRemark());
                    externalUserResp.setDescription(followedUser.getDescription());
                    externalUserResp.setCreateTime(DateUtils.secondToLocalDateTime(followedUser.getCreateTime()));
                }
            }
        }
        return RespUtils.success(externalUserResp);
    }

    public void flushAllWxStatus(Long userId, Long scheduleId) throws WxErrorException {
        if (!"prod".equals(profiles)) {
            return ;
        }
        Boolean b = redisTemplate.opsForValue().setIfAbsent(String.format(RedisKey.FLUSH_WX_LOCK, scheduleId), "", 10, TimeUnit.MINUTES);
        if (Boolean.FALSE.equals(b)) {
            return;
        }
        try {
            List<Order> orderList = orderMapper.selectList(new QueryWrapper<Order>().select(
                    Order.ID, Order.TEACHER_ID, Order.CLAZZ_ID, Order.USER_ID, Order.ASSISTANT_WX_STATUS, Order.TEACHER_WX_STATUS, Order.GROUP_WX_STATUS
            ).eq(Order.SCHEDULE_ID, scheduleId).isNotNull(Order.TEACHER_ID));
            Set<Long> clazzIds = orderList.stream().map(Order::getClazzId).filter(Objects::nonNull).collect(Collectors.toSet());
            Map<Long, Clazz> clazzMap = Collections.emptyMap();
            Set<Long> teacherIds = orderList.stream().map(Order::getTeacherId).filter(Objects::nonNull).collect(Collectors.toSet());
            if (!clazzIds.isEmpty()) {
                clazzMap = clazzMapper.selectList(new QueryWrapper<Clazz>().select(
                        Clazz.ID, Clazz.ASSISTANT_ID, Clazz.WX_GROUP_ID
                ).in(Clazz.ID, clazzIds)).stream().collect(Collectors.toMap(Clazz::getId, v -> v));
                teacherIds.addAll(clazzMap.values().stream().map(Clazz::getAssistantId).filter(Objects::nonNull).collect(Collectors.toSet()));
            }
            if (teacherIds.isEmpty()) {
                return;
            }
            Map<Long, Teacher> teacherMap = teacherMapper.selectList(new QueryWrapper<Teacher>().select(
                    Teacher.ID, Teacher.USER_ID
            ).in(Teacher.ID, teacherIds)).stream().collect(Collectors.toMap(Teacher::getId, v -> v));
            Map<Long, User> teacherUserMap = userMapper.selectList(new QueryWrapper<User>().select(
                    User.ID, User.PHONE_NUM
            ).in(User.ID, teacherMap.values().stream().map(Teacher::getUserId).collect(Collectors.toSet())))
                    .stream().collect(Collectors.toMap(User::getId, v -> v));
            Set<Long> userIds = orderList.stream().map(Order::getUserId).filter(Objects::nonNull).collect(Collectors.toSet());
            Map<Long, User> studentUserMap = userMapper.selectList(new QueryWrapper<User>().select(
                    User.ID, User.UNION_ID
            ).in(User.ID, userIds)).stream().collect(Collectors.toMap(User::getId, v -> v));
            Map<Long, UserWxGroup> userWxGroupMap = Collections.emptyMap();
            Set<Long> groupIds = clazzMap.values().stream().map(Clazz::getWxGroupId).filter(Objects::nonNull).collect(Collectors.toSet());
            if (!groupIds.isEmpty()) {
                userWxGroupMap = userWxGroupMapper.selectList(new QueryWrapper<UserWxGroup>().select(
                        UserWxGroup.ID, UserWxGroup.GROUP_ID
                ).in(UserWxGroup.ID, groupIds)).stream().collect(Collectors.toMap(UserWxGroup::getId, v -> v));
            }
            Map<Long, Set<String>> wxUserMap = new HashMap<>(teacherUserMap.size());
            Map<Long, Set<String>> wxGroupMap = new HashMap<>(clazzMap.size());
            List<Order> updateOrderList = new ArrayList<>(orderList.size());
            for (Order order : orderList) {
                boolean isUpdate = false;
                if (WxStatus.NOT_ADD.getCode().equals(order.getTeacherWxStatus())) {
                    boolean check = checkWxStatus(order, order.getTeacherId(), teacherMap, teacherUserMap, wxUserMap, studentUserMap);
                    if (check) {
                        order.setTeacherWxStatus(WxStatus.ADDED.getCode());
                        isUpdate = true;
                    }
                }
                if (order.getClazzId() != null) {
                    Clazz clazz = clazzMap.get(order.getClazzId());
                    Long assistantId = clazz.getAssistantId();
                    if (WxStatus.NOT_ADD.getCode().equals(order.getAssistantWxStatus())) {
                        boolean check = checkWxStatus(order, assistantId, teacherMap, teacherUserMap, wxUserMap, studentUserMap);
                        if (check) {
                            order.setAssistantWxStatus(WxStatus.ADDED.getCode());
                            isUpdate = true;
                        }
                    }
                    if (WxStatus.NOT_ADD.getCode().equals(order.getGroupWxStatus())) {
                        User user = studentUserMap.get(order.getUserId());
                        Set<String> memberIds = setMemberIds(clazz.getWxGroupId(), userWxGroupMap, wxGroupMap);
                        checkUnionId(user);
                        if (user.getUnionId() != null && memberIds.contains(user.getUnionId())) {
                            order.setGroupWxStatus(WxStatus.ADDED.getCode());
                            isUpdate = true;
                        }
                    }
                }
                if (isUpdate) {
                    updateOrderList.add(order);
                }
            }
            if (!updateOrderList.isEmpty()) {
                orderMapper.updateById(updateOrderList);
            }
        } finally {
            redisTemplate.delete(String.format(RedisKey.FLUSH_WX_LOCK, scheduleId));
        }
    }

    private boolean checkWxStatus(Order order, Long teacherId, Map<Long, Teacher> teacherMap, Map<Long, User> teacherUserMap,
                                  Map<Long, Set<String>> wxUserMap, Map<Long, User> studentUserMap) throws WxErrorException {
        User user = studentUserMap.get(order.getUserId());
        checkUnionId(user);
        if (user.getUnionId() == null) {
            return false;
        }
        Teacher teacher = teacherMap.get(teacherId);
        User teacherUser = teacherUserMap.get(teacher.getUserId());
        Set<String> teacherFriends = wxUserMap.get(teacherUser.getId());
        if (teacherFriends == null) {
            teacherFriends = getUnionIds(teacherUser.getPhoneNum());
            wxUserMap.put(teacherUser.getId(), teacherFriends);
        }
        return teacherFriends.contains(user.getUnionId());
    }

    private void checkUnionId(User user) {
        if (user.getUnionId() == null) {
            UnionItem unionId = fuyingUserFeign.getUnionId(user.getId());
            if (unionId == null || !StringUtils.hasLength(unionId.getThirdAuthId())) {
                return;
            }
            user.setUnionId(unionId.getThirdAuthId());
            userMapper.updateById(user);
        }
    }

    private Set<String> getUnionIds(Long phoneNum) {
        try {
            String userId = getUserId(String.valueOf(phoneNum));
            List<WxExternalUser> externalUserList = wxExternalUserMapper.selectList(new QueryWrapper<WxExternalUser>().select(
                    WxExternalUser.EXTERNAL_USER_ID, WxExternalUser.UNION_ID
            ).eq(WxExternalUser.USER_ID, userId));
            return getContactList(userId, externalUserList);
        } catch (WxErrorException e) {
            log.warn("获取用户好友列表失败", e);
            return Collections.emptySet();
        }
    }

    private Set<String> setMemberIds(Long groupId, Map<Long, UserWxGroup> userWxGroupMap, Map<Long, Set<String>> wxGroupMap) throws WxErrorException {
        if (groupId == null) {
            return Collections.emptySet();
        }
        UserWxGroup wxGroup = userWxGroupMap.get(groupId);
        String wxGroupId = wxGroup.getGroupId();
        Set<String> unionIds = wxGroupMap.get(groupId);
        if (unionIds != null) {
            return unionIds;
        }
        WxCpUserExternalGroupChatInfo.GroupChat groupChat = groupChatDetail(wxGroupId);
        List<WxCpUserExternalGroupChatInfo.GroupMember> memberList = groupChat.getMemberList();
        unionIds =  memberList.stream().map(WxCpUserExternalGroupChatInfo.GroupMember::getUnionId)
                .filter(Objects::nonNull).collect(Collectors.toSet());
        wxGroupMap.put(groupId, unionIds);
        return unionIds;
    }

    public List<UserWxGroup> getUserWxGroup(Long userId) throws WxErrorException {
        if (!"prod".equals(profiles)) {
            return Collections.emptyList();
        }
        User user = userMapper.selectOne(new QueryWrapper<User>().select(
                User.PHONE_NUM
        ).eq(User.ID, userId));
        if (user == null) {
            return Collections.emptyList();
        }
        List<UserWxGroup> groupList = userWxGroupMapper.selectList(new QueryWrapper<UserWxGroup>().select(
                UserWxGroup.ID, UserWxGroup.GROUP_ID, UserWxGroup.GROUP_NAME, UserWxGroup.GROUP_CREATED_TIME
        ).eq(UserWxGroup.USER_ID, userId));
        Set<String> groupIdSet = groupList.stream().map(UserWxGroup::getGroupId).collect(Collectors.toSet());
        List<String> groupIds = wxGroupList(String.valueOf(user.getPhoneNum()));
        List<UserWxGroup> addGroupList = new ArrayList<>();
        for (String groupId : groupIds) {
            if (groupIdSet.contains(groupId)) {
                continue;
            }
            WxCpUserExternalGroupChatInfo.GroupChat groupChat = groupChatDetail(groupId);
            UserWxGroup wxGroup = new UserWxGroup();
            wxGroup.setUserId(userId);
            wxGroup.setGroupId(groupChat.getChatId());
            wxGroup.setGroupName(groupChat.getName());
            wxGroup.setGroupCreatedTime(DateUtils.secondToLocalDateTime(groupChat.getCreateTime()));
            addGroupList.add(wxGroup);
        }
        if (!addGroupList.isEmpty()) {
            userWxGroupMapper.insert(addGroupList);
        }
        groupList.addAll(addGroupList);
        return groupList.stream().sorted(Comparator.comparing(UserWxGroup::getGroupCreatedTime).reversed()).toList();
    }
}
