package com.fuyingedu.training.admin.model.order;

import com.alibaba.excel.annotation.ExcelIgnore;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class RecordItemResp {
    private Long id;
    /**
     * 用户名
     */
    private String realName;
    /**
     * 用户手机号
     */
    private Long phoneNum;
    /**
     * 学员名称
     */
    private String studentName;

    /**
     * 用户头像
     */
    private String userIcon;

    /**
     * 学员证件号
     */
    private String cartNo;
    /**
     * 1-新训 2-复训
     */
    private Byte studentFlag;
    /**
     * 导师
     */
    private String teacherName;
    /**
     * 大班表主键
     */
    private Long gradeId;

    /**
     * 小班表主键
     */
    private Long clazzId;
    /**
     * 小组表主键
     */
    private Long groupId;
    /**
     * 助教
     */
    private String assistantName;
    /**
     * 班级
     */
    private String clazzName;
    /**
     * 组名
     */
    private String groupName;

    /**
     * 组内身份 1-学员 2-陪跑
     */
    private Byte studentType;

    /**
     * 签到次数
     */
    private Integer enrollmentNum;

    /**
     * 打卡次数
     */
    private Integer punchNum;

    /**
     * 作业次数
     */
    private Integer homeworkNum;

    /**
     * 点评他人作业次数
     */
    private Integer remarkNum;

    /**
     * 奖状次数
     */
    private Integer medalNum;

    /**
     * 任务积分
     */
    private Integer taskReward;

    /**
     * 作业点评次数
     */
    private Integer remarkedNum;

    /**
     * 直播完成次数
     */
    private Integer liveSignNum;

    /**
     * 直播答题得分
     */
    private Integer liveAnswerNum;

    /**
     * word鹰积分
     */
    private Integer wordNum;

    /**
     * 学练测完成数
     */
    private Integer studentNum;

    /**
     * 复习完成数
     */
    private Integer reviewNum;
}
