package com.fuyingedu.training.admin.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fuyingedu.training.admin.model.dict.DictTreeResp;
import com.fuyingedu.training.common.enums.RespMetaEnum;
import com.fuyingedu.training.common.exception.WebBaseException;
import com.fuyingedu.training.common.model.CommResp;
import com.fuyingedu.training.common.util.RespUtils;
import com.fuyingedu.training.entity.Dictionary;
import com.fuyingedu.training.mapper.DictionaryMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class DictionaryService {

    @Autowired
    private DictionaryMapper dictionaryMapper;

    public CommResp<List<DictTreeResp>> getDictList(List<String> dictKeyList) {
        List<Dictionary> pDictList = dictionaryMapper.selectList(
                new QueryWrapper<Dictionary>()
                    .select(
                            Dictionary.ID,
                            Dictionary.DICT_KEY, 
                            Dictionary.DICT_VALUE
                    )
                    .in(Dictionary.DICT_KEY, dictKeyList)
                    .eq(Dictionary.PARENT_ID, -1)
        );
        List<Integer> pIdList = pDictList.stream().map(Dictionary::getId).toList();
        if (pIdList.isEmpty()) {
            throw new WebBaseException(RespMetaEnum.PARAM_ERROR);
        }
        List<Dictionary> cDictList = dictionaryMapper.selectList(
                new QueryWrapper<Dictionary>()
                    .select(
                            Dictionary.DICT_KEY,
                            Dictionary.DICT_VALUE,
                            Dictionary.PARENT_ID
                    )
                    .in(Dictionary.PARENT_ID, pIdList)
        );
        Map<Integer, List<Dictionary>> cDictMap = cDictList.stream().collect(
                Collectors.groupingBy(Dictionary::getParentId)
        );
        List<DictTreeResp> respList = new ArrayList<>(pDictList.size());
        for (Dictionary dict : pDictList) {
            DictTreeResp resp = new DictTreeResp();
            resp.setDictKey(dict.getDictKey());
            resp.setDictValue(dict.getDictValue());
            List<Dictionary> childList = cDictMap.get(dict.getId());
            if (!CollectionUtils.isEmpty(childList)) {
                resp.setChildList(
                        childList.stream().map(child -> {
                            DictTreeResp childResp = new DictTreeResp();
                            childResp.setDictKey(child.getDictKey());
                            childResp.setDictValue(child.getDictValue());
                            return childResp;
                        }).toList()
                );
            }
            respList.add(resp);
        }
        return RespUtils.success(respList);
    }
}
