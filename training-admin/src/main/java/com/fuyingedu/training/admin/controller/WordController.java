package com.fuyingedu.training.admin.controller;

import com.fasterxml.jackson.databind.JsonNode;
import com.fuyingedu.training.admin.model.word.DelayReq;
import com.fuyingedu.training.admin.model.word.SaveReq;
import com.fuyingedu.training.admin.service.ScheduleService;
import com.fuyingedu.training.admin.service.WordService;
import com.fuyingedu.training.common.annotation.Login;
import com.fuyingedu.training.common.model.CommResp;
import com.fuyingedu.training.common.util.JsonUtils;
import com.fuyingedu.training.common.util.RespUtils;
import com.fuyingedu.training.entity.Schedule;
import com.fuyingedu.training.front.model.word.TaskItem;
import com.fuyingedu.training.front.service.FrontWordService;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import java.util.List;

/**
 * 单词训练营管理
 */
@RestController
@Slf4j
@RequestMapping("admin/word")
public class WordController {

    @Autowired
    private WordService wordService;
    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private ScheduleService scheduleService;
    @Value("${fxy.base-url}")
    private String baseUrl;
    @Autowired
    private FrontWordService frontWordService;

    /**
     * 获取词库列表
     */
    @GetMapping("list")
    public CommResp<?> list() {
        HttpHeaders headers = new HttpHeaders();
        headers.set("Access-Key", "3447CB6560E99791");
        HttpEntity<Object> httpEntity = new HttpEntity<>(headers);
        String url = baseUrl + "/traApi/admin/trainingCampPlan/onlineList";
        ResponseEntity<String> resp = restTemplate.exchange(url, HttpMethod.GET, httpEntity, String.class);
        JsonNode jsonNode = JsonUtils.parseJsonToJsonNode(resp.getBody());
        return RespUtils.success(jsonNode.get("data"));
    }

    /**
     * 任务列表
     */
    @GetMapping("task/list")
    public CommResp<List<TaskItem>> list(@Login Long userId, Long scheduleId) {
        return RespUtils.success(frontWordService.taskList(scheduleId));
    }

    /**
     * 单词训练营添加排期
     */
    @PostMapping("save")
    public CommResp<?> save(@Login Long userId, @RequestBody @Valid SaveReq saveReq) {
        Schedule schedule = wordService.save(userId, saveReq);
        scheduleService.syncOrder(schedule.getId());
        return RespUtils.success();
    }

    /**
     * 单词训练营延期
     */
    @PostMapping("delay")
    public CommResp<?> delay(@RequestBody @Valid DelayReq req) {
        wordService.delay(req.getScheduleId(), req.getDays(), req.getDelayDays());
        return RespUtils.success();
    }
}
