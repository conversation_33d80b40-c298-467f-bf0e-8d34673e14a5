package com.fuyingedu.training.admin.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fuyingedu.training.admin.model.role.*;
import com.fuyingedu.training.common.exception.WebBaseException;
import com.fuyingedu.training.common.model.CommResp;
import com.fuyingedu.training.common.util.RespUtils;
import com.fuyingedu.training.dto.fuying.UserInfoRet;
import com.fuyingedu.training.dto.role.UserItemRet;
import com.fuyingedu.training.entity.*;
import com.fuyingedu.training.mapper.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class RoleService {

    @Autowired
    private RoleMapper roleMapper;
    @Autowired
    private UserMapper userMapper;
    @Autowired
    private UserRoleMapper userRoleMapper;
    @Autowired
    private FuyingMapper fuyingMapper;
    @Autowired
    private ResourceMapper resourceMapper;
    @Autowired
    private RoleResourceMapper roleResourceMapper;

    public CommResp<List<UserItemRet>> manageList(UserReq userReq) {
        IPage<UserItemRet> page = new Page<>(userReq.getPageNum(), userReq.getPageSize());
        page = roleMapper.pageManagerList(page, userReq.getUserInfo());
        return RespUtils.success(page.getCurrent(), page.getSize(), page.getTotal(), page.getRecords());
    }

    public CommResp<List<ItemResp>> list() {
        List<ItemResp> respList = roleMapper.selectList(new QueryWrapper<Role>().select(
                Role.ID, Role.ROLE_NAME, Role.ROLE_DESC, Role.CREATED_TIME
        )).stream().map(role -> new ItemResp(role.getId(), role.getRoleName(), role.getRoleDesc(), role.getCreatedTime())).toList();
        return RespUtils.success(respList);
    }

    public CommResp<UserDetailResp> userDetail(Long userId) {
        User user = userMapper.selectOne(new QueryWrapper<User>().select(
                User.ID, User.REAL_NAME, User.PHONE_NUM, User.UNION_ID, User.USER_ICON
        ).eq(User.ID, userId));
        UserDetailResp resp = toDetailResp(user);
        List<Long> roleIds = userRoleMapper.selectList(new QueryWrapper<UserRole>().select(
                UserRole.ROLE_ID
        )).stream().map(UserRole::getRoleId).toList();
        resp.setRoleIds(roleIds);
        return RespUtils.success(resp);
    }

    private UserDetailResp toDetailResp(User user) {
        UserDetailResp detailResp = new UserDetailResp();
        detailResp.setUserIcon(user.getUserIcon());
        detailResp.setRealName(user.getRealName());
        detailResp.setPhoneNum(user.getPhoneNum());
        detailResp.setUnionId(user.getUnionId());
        detailResp.setOuterId(user.getId());
        return detailResp;
    }

    public void userSave(UserSaveReq saveReq) {
//        if (saveReq.getId() == null) {
//            UserInfoRet userInfoRet = fuyingMapper.queryUserInfo(String.valueOf(saveReq.getPhoneNum()), null);
//            if (userInfoRet == null) {
//                throw new WebBaseException(4000, "手机号不存在");
//            }
//            User user = userMapper.selectOne(new QueryWrapper<User>().select(
//                    User.ID
//            ).eq(User.OUTER_ID, userInfoRet.getUid()));
//            if (user == null) {
//                user = toUser(userInfoRet);
//            }
//            user.setPhoneNum(saveReq.getPhoneNum());
//            userMapper.insertOrUpdate(user);
//            Long userId = user.getId();
//            List<UserRole> roleList = saveReq.getRoleIds().stream().distinct().map(roleId -> {
//                UserRole userRole = new UserRole();
//                userRole.setUserId(userId);
//                userRole.setRoleId(roleId);
//                return userRole;
//            }).toList();
//            userRoleMapper.insert(roleList);
//        } else {
//            User user = userMapper.selectOne(new QueryWrapper<User>().select(
//                    User.ID
//            ).eq(User.ID, saveReq.getId()));
//            if (user == null) {
//                throw new WebBaseException(4000, "用户不存在");
//            }
//            Map<Long, UserRole> oldRoleIds = userRoleMapper.selectList(new QueryWrapper<UserRole>().select(
//                    UserRole.ID, UserRole.ROLE_ID
//            ).eq(UserRole.USER_ID, saveReq.getId())).stream().collect(Collectors.toMap(UserRole::getRoleId, userRole -> userRole));
//            Set<Long> newRoleIds = new HashSet<>(saveReq.getRoleIds());
//            List<Long> removeIds = oldRoleIds.values().stream().filter(role -> !newRoleIds.contains(role.getRoleId())).map(UserRole::getId).toList();
//            if (!removeIds.isEmpty()) {
//                userRoleMapper.deleteByIds(removeIds);
//            }
//            List<UserRole> addRoleList = newRoleIds.stream().filter(roleId -> !oldRoleIds.containsKey(roleId)).map(roleId -> {
//                UserRole userRole = new UserRole();
//                userRole.setUserId(saveReq.getId());
//                userRole.setRoleId(roleId);
//                return userRole;
//            }).toList();
//            if (!addRoleList.isEmpty()) {
//                userRoleMapper.insert(addRoleList);
//            }
//        }
    }

    public CommResp<DetailResp> detail(Long id) {
        Role role = roleMapper.selectOne(new QueryWrapper<Role>().select(
                Role.ID, Role.ROLE_NAME, Role.ROLE_DESC
        ).eq(Role.ID, id));
        DetailResp detailResp = toDetailResp(role);
        Set<Long> resourceIds = roleResourceMapper.selectList(new QueryWrapper<RoleResource>().select(
                RoleResource.RESOURCE_ID
        ).eq(RoleResource.ROLE_ID, id)).stream().map(RoleResource::getResourceId).collect(Collectors.toSet());
        detailResp.setResourceIds(resourceTree(resourceIds));
        return RespUtils.success(detailResp);
    }

    private DetailResp toDetailResp(Role role) {
        DetailResp detailResp = new DetailResp();
        detailResp.setId(role.getId());
        detailResp.setRoleName(role.getRoleName());
        detailResp.setRoleDesc(role.getRoleDesc());
        return detailResp;
    }

    public CommResp<List<ResourceTreeResp>> resourceTree() {
        return RespUtils.success(resourceTree(Collections.emptySet()));
    }

    private List<ResourceTreeResp> resourceTree(Set<Long> checkedSet) {
        List<Resource> resourceList = resourceMapper.selectList(new QueryWrapper<Resource>().select(
                Resource.ID, Resource.PARENT_ID, Resource.RESOURCE_LABEL
        ).eq(Resource.DELETED_FLAG, 0));
        Map<Long, List<Resource>> parentMap = resourceList.stream().collect(Collectors.groupingBy(Resource::getParentId));
        return childList(resourceList, parentMap, checkedSet);
    }

    private List<ResourceTreeResp> childList(List<Resource> resourceList, Map<Long, List<Resource>> parentMap, Set<Long> checkedSet) {
        if (resourceList == null || resourceList.isEmpty()) {
            return null;
        }
        List<ResourceTreeResp> respList = new ArrayList<>(resourceList.size());
        for (Resource resource : resourceList) {
            ResourceTreeResp resp = new ResourceTreeResp();
            resp.setId(resource.getId());
            resp.setResourceLabel(resource.getResourceLabel());
            resp.setChecked(checkedSet.contains(resource.getId()) ? (byte) 1 : (byte) 2);
            List<Resource> childrenList = parentMap.get(resource.getId());
            List<ResourceTreeResp> childList = childList(childrenList, parentMap, checkedSet);
            resp.setChildList(childList);
            respList.add(resp);
        }
        return respList;
    }

    public void save(SaveReq saveReq) {
        Role role = toRole(saveReq);
        if (saveReq.getId() == null) {
            roleMapper.insert(role);
            List<RoleResource> roleResourceList = saveReq.getResourceIds().stream().distinct().map(resourceId -> {
                RoleResource roleResource = new RoleResource();
                roleResource.setRoleId(role.getId());
                roleResource.setResourceId(resourceId);
                return roleResource;
            }).toList();
            roleResourceMapper.insert(roleResourceList);
        } else {
            Role oleRole = roleMapper.selectOne(new QueryWrapper<Role>().select(
                    Role.ID
            ).eq(Role.ID, saveReq.getId()));
            if (oleRole == null) {
                throw new WebBaseException(4000, "角色不存在，无法更新");
            }
            roleMapper.updateById(role);
            Map<Long, RoleResource> oldResourceIds = roleResourceMapper.selectList(new QueryWrapper<RoleResource>().select(
                    RoleResource.RESOURCE_ID, RoleResource.ID
            ).eq(RoleResource.ROLE_ID, saveReq.getId())).stream().collect(Collectors.toMap(RoleResource::getResourceId, v -> v));
            Set<Long> newResourceIds = new HashSet<>(saveReq.getResourceIds());
            List<Long> removeIds = oldResourceIds.values().stream()
                    .filter(roleResource -> !newResourceIds.contains(roleResource.getResourceId()))
                    .map(RoleResource::getId).toList();
            if (!removeIds.isEmpty()) {
                roleResourceMapper.deleteByIds(removeIds);
            }
            List<RoleResource> addRoleList = newResourceIds.stream()
                    .filter(resourceId -> !oldResourceIds.containsKey(resourceId)).map(resourceId -> {
                RoleResource roleResource = new RoleResource();
                roleResource.setRoleId(role.getId());
                roleResource.setResourceId(resourceId);
                return roleResource;
            }).toList();
            if (!addRoleList.isEmpty()) {
                roleResourceMapper.insert(addRoleList);
            }
        }
    }

    private Role toRole(SaveReq saveReq) {
        Role role = new Role();
        role.setId(saveReq.getId());
        role.setRoleName(saveReq.getRoleName());
        role.setRoleDesc(saveReq.getRoleDesc());
        return role;
    }
}
