package com.fuyingedu.training.admin.model.order.log;

import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
public class ItemResp {

    private LocalDateTime createdTime;

    private String username;
    /**
     * 1-改期 2-分配辅导老师 3-关闭订单 4-核销订单 5-删除备注 6-修改辅导老师 7-备注 8-分班 9-分组 12-修改订单状态-可能退单 13-修改订单状态-正常
     * 14-加小红花 15-加小太阳 16-加word鹰 17-设置优秀作业 18-获得奖状
     */
    private Integer operationType;

    /**
     * 操作之前的值
     */
    private String prevContent;

    /**
     * 操作之后的值
     */
    private String nextContent;
}
