package com.fuyingedu.training.admin.manager.camp;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.fuyingedu.training.entity.*;
import com.fuyingedu.training.mapper.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 王金海：防手机沉迷父母7天训练营
 */
@Component("camp_33")
public class Camp33RuleManager implements CampRuleManager {

    @Autowired
    private LiveMapper liveMapper;
    @Autowired
    private TaskMapper taskMapper;
    @Autowired
    private TaskTemplateMapper taskTemplateMapper;
    @Autowired
    private TaskRelationMapper taskRelationMapper;
    @Autowired
    private ScheduleMapper scheduleMapper;

    @Override
    public void initRule(Long userId, Long scheduleId, LocalDate startDate, LocalTime liveTime) {
        LocalDateTime startTime = LocalDateTime.of(startDate, liveTime);
        List<Live> liveList = new ArrayList<>();
        liveList.add(buildLive(scheduleId, "第一课 很无助！如何让父母越轻松，孩子越努力？", 90, startTime));
        liveList.add(buildLive(scheduleId, "第二课 不主动！激发孩子学习动力的内功心法！", 90, startTime.plusDays(1)));
        liveList.add(buildLive(scheduleId, "第三课 常吼叫！不吼不叫，教出上进的孩子！", 90, startTime.plusDays(2)));
        liveList.add(buildLive(scheduleId, "第四课 不自律！帮孩子拒绝手机诱惑，做学习王者！", 90, startTime.plusDays(3)));
        liveList.add(buildLive(scheduleId, "第五课 关系差！父母如何成为孩子学习的强大助力！", 90, startTime.plusDays(4)));
        liveList.add(buildLive(scheduleId, "第六课 难沟通！三言两语，化解孩子各种问题！", 90, startTime.plusDays(5)));
        liveList.add(buildLive(scheduleId, "第七课 很迷茫！未来如何引领孩子成为人生赢家？", 90, startTime.plusDays(6)));
        liveMapper.insert(liveList);
        List<Task> taskList = new ArrayList<>();
        taskList.add(buildTask(userId, scheduleId, "第一课课前思考", template(1000114L), startDate, startDate));
        taskList.add(buildTask(userId, scheduleId, "第一课课后作业", template(1000115L), startDate, startDate.plusDays(1)));
        taskList.add(buildTask(userId, scheduleId, "第二课课前思考", template(1000116L), startDate.plusDays(1), startDate.plusDays(1)));
        taskList.add(buildTask(userId, scheduleId, "第二课课后作业", template(1000117L), startDate.plusDays(1), startDate.plusDays(2)));
        taskList.add(buildTask(userId, scheduleId, "第三课课前思考", template(1000118L), startDate.plusDays(2), startDate.plusDays(2)));
        taskList.add(buildTask(userId, scheduleId, "第三课课后作业", template(1000119L), startDate.plusDays(2), startDate.plusDays(3)));
        taskList.add(buildTask(userId, scheduleId, "第四课课前思考", template(1000120L), startDate.plusDays(3), startDate.plusDays(3)));
        taskList.add(buildTask(userId, scheduleId, "第四课课后作业", template(1000121L), startDate.plusDays(3), startDate.plusDays(4)));
        taskList.add(buildTask(userId, scheduleId, "第五课课前思考", template(1000122L), startDate.plusDays(4), startDate.plusDays(4)));
        taskList.add(buildTask(userId, scheduleId, "第五课课后作业", template(1000123L), startDate.plusDays(4), startDate.plusDays(5)));
        taskList.add(buildTask(userId, scheduleId, "第六课课前思考", template(1000124L), startDate.plusDays(5), startDate.plusDays(5)));
        taskList.add(buildTask(userId, scheduleId, "第六课课后作业", template(1000125L), startDate.plusDays(5), startDate.plusDays(6)));
        taskList.add(buildTask(userId, scheduleId, "第七课课前思考", template(1000126L), startDate.plusDays(6), startDate.plusDays(6)));
        taskList.add(buildTask(userId, scheduleId, "第七课课后作业", template(1000127L), startDate.plusDays(7), startDate.plusDays(7)));
        taskMapper.insert(taskList);
        List<TaskRelation> relationList = new ArrayList<>();
        for (Task task : taskList) {
            TaskRelation relation = new TaskRelation();
            relation.setTaskId(task.getId());
            relation.setScheduleId(scheduleId);
            relation.setTeacherId(-1L);
            relation.setAssistantId(-1L);
            relation.setClazzId(-1L);
            relationList.add(relation);
        }
        taskRelationMapper.insert(relationList);
        scheduleMapper.update(new UpdateWrapper<Schedule>().setSql(String.format("%s = %s + %d", Schedule.TASK_NUM, Schedule.TASK_NUM, taskList.size()))
                .eq(Schedule.ID, scheduleId));
    }

    private TaskTemplate template(Long templateId) {
        return taskTemplateMapper.selectOne(new QueryWrapper<TaskTemplate>().eq(TaskTemplate.ID, templateId));
    }
}
