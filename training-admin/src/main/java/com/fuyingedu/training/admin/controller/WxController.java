package com.fuyingedu.training.admin.controller;

import com.fuyingedu.training.admin.model.wx.ExternalUserResp;
import com.fuyingedu.training.admin.model.wx.RemarkReq;
import com.fuyingedu.training.admin.model.wx.StatusResp;
import com.fuyingedu.training.admin.service.WxEnterpriseService;
import com.fuyingedu.training.common.annotation.Login;
import com.fuyingedu.training.common.model.CommResp;
import com.fuyingedu.training.common.util.RespUtils;
import com.fuyingedu.training.entity.UserWxGroup;
import jakarta.validation.Valid;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 企业微信相关接口
 */
@RestController
@RequestMapping("admin/wx")
public class WxController {

    @Autowired
    private WxEnterpriseService wxEnterpriseService;

    /**
     * 刷新企业微信状态
     */
    @GetMapping("flush")
    public CommResp<StatusResp> flushStatus(@RequestParam("orderId") Long orderId) throws WxErrorException {
        return wxEnterpriseService.flushWxStatus(orderId);
    }

    /**
     * 刷新所有企业微信状态
     */
    @GetMapping("flush/all")
    public CommResp<?> flushAllStatus(@Login Long userId, @RequestParam("scheduleId") Long scheduleId) throws WxErrorException {
        wxEnterpriseService.flushAllWxStatus(userId, scheduleId);
        return RespUtils.success();
    }

    /**
     * 企业微信备注
     */
    @PostMapping("remark")
    public CommResp<?> remark(@Valid @RequestBody RemarkReq remarkReq) throws WxErrorException {
        if (remarkReq.getRemark() == null && remarkReq.getDescription() == null) {
            return RespUtils.success();
        }
        wxEnterpriseService.remark(remarkReq);
        return RespUtils.success();
    }

    /**
     * 获取企业微信用户信息
     * @param orderId 服务单ID
     * @param teacherType 1-辅导老师 2-助教
     */
    @GetMapping("user/info")
    public CommResp<ExternalUserResp> getUserInfo(@RequestParam("orderId") Long orderId,
                                                  @RequestParam("teacherType") Byte teacherType) throws WxErrorException {
        return wxEnterpriseService.getUserInfo(orderId, teacherType);
    }

    /**
     * 获取企业微信用户群列表
     */
    @GetMapping("group/list")
    public CommResp<List<UserWxGroup>> getUserWxGroup(@Login Long userId) throws WxErrorException {
        return RespUtils.success(wxEnterpriseService.getUserWxGroup(userId));
    }
}
