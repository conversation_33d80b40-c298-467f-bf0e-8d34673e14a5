package com.fuyingedu.training.admin.interceptor;

import com.auth0.jwt.exceptions.JWTVerificationException;
import com.fuyingedu.training.common.enums.RespMetaEnum;
import com.fuyingedu.training.common.exception.WebBaseException;
import com.fuyingedu.training.common.model.CommResp;
import com.fuyingedu.training.common.util.RespUtils;
import io.micrometer.core.instrument.config.validate.ValidationException;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.util.Map;

@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    @ExceptionHandler(MissingServletRequestParameterException.class)
    public CommResp<?> missingServletRequestParameterException(HttpServletRequest request, MissingServletRequestParameterException e) {
        printLog(request.getRequestURI(), request, e);
        return RespUtils.warning(RespMetaEnum.PARAM_ERROR);
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public CommResp<?> methodArgumentNotValidException(HttpServletRequest request, MethodArgumentNotValidException e) {
        printLog(request.getRequestURI(), request, e);
        String errMsg = "参数错误";
        FieldError fieldError = e.getBindingResult().getFieldError();
        if (fieldError != null && StringUtils.hasLength(fieldError.getDefaultMessage())) {
            errMsg = fieldError.getField() + fieldError.getDefaultMessage();
        }
        return RespUtils.warning(4000, errMsg);
    }

    @ExceptionHandler(ValidationException.class)
    public CommResp<?> validationException(HttpServletRequest request, ValidationException e) {
        printLog(request.getRequestURI(), request, e);
        return RespUtils.warning(RespMetaEnum.PARAM_ERROR);
    }

    @ExceptionHandler(JWTVerificationException.class)
    public CommResp<?> tokenExpiredException(HttpServletRequest request, JWTVerificationException e) {
        printLog(request.getRequestURI(), request, e);
        return RespUtils.warning(RespMetaEnum.NOT_LOGIN);
    }

    @ExceptionHandler(WebBaseException.class)
    public CommResp<?> webBaseException(HttpServletRequest request, WebBaseException e) {
        printLog(request.getRequestURI(), request, e);
        return RespUtils.warning(e.getStatus(), e.getMsg());
    }

    @ExceptionHandler(Throwable.class)
    public CommResp<?> defaultErrorView(HttpServletRequest request, Throwable e) {
        printLog(request.getRequestURI(), request, e);
        return RespUtils.warning(RespMetaEnum.TIMEOUT);
    }

    private void printLog(String uri, HttpServletRequest request, Throwable e) {
        if (log.isWarnEnabled()) {
            if (uri != null && uri.length() > 14) {
                uri = uri.substring(14);
            }
            if (e instanceof WebBaseException resp && e.getCause() == null) {
                log.error("[{}][{}]", uri, resp.getMsg());
            } else {
                log.error("======[{}]======", uri, e);
            }
            Map<String, String[]> parameterMap = request.getParameterMap();
            for (Map.Entry<String, String[]> entry : parameterMap.entrySet()) {
                StringBuilder sb = new StringBuilder();
                for (String s : entry.getValue()) {
                    sb.append(s).append(",");
                }
                log.error("[{}]:[{}]", entry.getKey(), sb);
            }
        }
    }
}
