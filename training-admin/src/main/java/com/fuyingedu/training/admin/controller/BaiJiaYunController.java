package com.fuyingedu.training.admin.controller;

import com.fuyingedu.training.admin.model.bjy.*;
import com.fuyingedu.training.admin.service.BaiJiaYunService;
import com.fuyingedu.training.admin.service.LiveSignService;
import com.fuyingedu.training.common.annotation.Login;
import com.fuyingedu.training.common.annotation.UserInfo;
import com.fuyingedu.training.common.model.CommResp;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 百家云相关接口管理
 */
@RestController
@RequestMapping("admin/bjy")
public class BaiJiaYunController {

    @Autowired
    private BaiJiaYunService baiJiaYunService;
    @Autowired
    private LiveSignService liveSignService;

    /**
     * 获取直播房间列表
     */
    @GetMapping("room/list")
    public CommResp<List<RoomResp>> roomList(@Login UserInfo userInfo, RoomReq req) {
        return baiJiaYunService.roomList(userInfo, req);
    }

    /**
     * 没有绑定的房间列表
     */
    @GetMapping("room/unbind/list")
    public CommResp<List<UnbindRoomResp>> unbindRoomList(@Login Long userId, @RequestParam("scheduleId") Long scheduleId) {
        return baiJiaYunService.unbindRoomList(scheduleId);
    }

    /**
     * 刷新直播房间
     */
    @PostMapping("room/flush")
    public CommResp<?> flushRoom(@Login Long userId) {
        return baiJiaYunService.flushRoom();
    }

    /**
     * 获取直播分组列表
     */
    @GetMapping("group/list")
    public CommResp<List<GroupItemResp>> groupList(@Login Long userId, @RequestParam("roomId") Long roomId) {
        return baiJiaYunService.groupList(roomId);
    }

    /**
     * 修改分组名称
     */
    @PostMapping("group/update")
    public CommResp<?> updateGroup(@Login Long userId, @RequestBody GroupUpdateReq req) {
        return baiJiaYunService.updateGroup(req);
    }

    /**
     * 直播回放列表
     */
    @GetMapping("playback/list")
    public CommResp<List<PlaybackResp>> playbackList(@Login Long userId, @RequestParam("roomId") Long roomId) {
        return baiJiaYunService.playbackList(roomId);
    }

    /**
     * 可关联的直播计划
     */
    @GetMapping("live/list")
    public CommResp<List<LiveResp>> liveList(@Login Long userId, @RequestParam("roomId") Long roomId) {
        return baiJiaYunService.liveList(roomId);
    }

    /**
     * 关联直播计划
     */
    @PostMapping("playback/bind")
    public CommResp<?> bind(@Login Long userId, @RequestBody BindReq req) {
        return baiJiaYunService.bind(req);
    }

    /**
     * 解绑直播计划
     */
    @PostMapping("playback/unbind")
    public CommResp<?> unbind(@Login Long userId, @RequestParam("liveId") Long liveId) {
        return baiJiaYunService.unbind(liveId);
    }

    /**
     * 试卷列表
     */
    @GetMapping("quiz/list")
    public CommResp<List<QuizResp>> quizList(@Login Long userId, @RequestParam("roomId") Long roomId) {
        return baiJiaYunService.quizList(roomId);
    }

    /**
     * 显示配置签到奖励
     */
    @GetMapping("sign/setting")
    public CommResp<SignResp> signSetting(@Login Long userId, @RequestParam("liveId") Long liveId) {
        return baiJiaYunService.signSetting(liveId);
    }

    /**
     * 配置签到奖励
     */
    @PostMapping("sign/update")
    public CommResp<?> signUpdate(@Login Long userId, @RequestBody @Valid SignUpdateReq req) {
        return baiJiaYunService.updateSign(req);
    }

    /**
     * 测试试卷加分配置
     */
    @GetMapping("quiz/setting")
    public CommResp<QuizSettingResp> quizSetting(@Login Long userId, @RequestParam("liveId") Long liveId,
                                                 @RequestParam("roomId") Long roomId) {
        return baiJiaYunService.quizSetting(roomId, liveId);
    }

    /**
     * 测试试卷加分配置
     */
    @PostMapping("quiz/update")
    public CommResp<?> quizUpdate(@Login Long userId, @RequestBody @Valid QuizSettingReq req) {
        return baiJiaYunService.updateQuiz(req);
    }

    /**
     * 添加题目
     */
    @PostMapping("quiz/add")
    public CommResp<?> addQuiz(@Login Long userId, @RequestBody @Valid QuizLiveReq req) {
        return baiJiaYunService.addQuiz(req);
    }

    /**
     * 删除题目
     */
    @PostMapping("quiz/delete")
    public CommResp<?> deleteQuiz(@Login Long userId, @RequestParam("liveId") Long liveId,
                                  @RequestParam("quizId") Long quizId) {
        return baiJiaYunService.deleteQuiz(liveId, quizId);
    }

    /**
     * 答题器配置回显
     */
    @GetMapping("answer/setting")
    public CommResp<SignResp> answerSetting(@Login Long userId, @RequestParam("liveId") Long liveId) {
        return baiJiaYunService.answerSetting(liveId);
    }

    /**
     * 配置答题器奖励
     */
    @PostMapping("answer/update")
    public CommResp<?> answerUpdate(@Login Long userId, @RequestBody @Valid SignUpdateReq req) {
        return baiJiaYunService.updateAnswer(req);
    }

    /**
     * 同步
     */
    @PostMapping("sync")
    public CommResp<?> sync(@RequestParam("scheduleId") Long scheduleId, @RequestParam("liveId") Long liveId) {
        return liveSignService.syncReward(scheduleId, liveId);
    }

    /**
     * 回调接口
     */
    @PostMapping("callback")
    public CommResp<?> callback(@RequestBody String messageBody) {
        return liveSignService.callback(messageBody);
    }
}
