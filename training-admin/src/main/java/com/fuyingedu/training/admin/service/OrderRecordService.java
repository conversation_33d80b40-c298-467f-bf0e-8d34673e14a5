package com.fuyingedu.training.admin.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fuyingedu.training.admin.model.order.AddRewardReq;
import com.fuyingedu.training.common.enums.OrderStatus;
import com.fuyingedu.training.common.enums.RespMetaEnum;
import com.fuyingedu.training.common.exception.WebBaseException;
import com.fuyingedu.training.entity.Order;
import com.fuyingedu.training.entity.UserRemark;
import com.fuyingedu.training.front.manager.OperationManager;
import com.fuyingedu.training.front.manager.RewardManager;
import com.fuyingedu.training.mapper.OrderMapper;
import com.fuyingedu.training.mapper.UserRemarkMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Service
public class OrderRecordService {

    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private UserRemarkMapper userRemarkMapper;
    @Autowired
    private RewardManager rewardManager;
    @Autowired
    private OperationManager operationManager;

    @Transactional(rollbackFor = Exception.class)
    public void addReward(Long userId, AddRewardReq req) {
        List<Order> orderList = orderMapper.selectList(new QueryWrapper<Order>().select(
                        Order.ID, Order.USER_ID, Order.SCHEDULE_ID
                ).eq(Order.CLAZZ_ID, req.getClazzId())
                .in(Order.ORDER_STATUS, OrderStatus.NORMAL.getCode(), OrderStatus.MAY_REFUND.getCode())
                .in(Order.ID, req.getOrderIdList()));
        if (orderList.size() != req.getOrderIdList().size()) {
            throw new WebBaseException(RespMetaEnum.PARAM_ERROR);
        }
        List<UserRemark> remarkList = new ArrayList<>(req.getOrderIdList().size());
        Map<Long, Long> orderMap = new HashMap<>();
        for (Order order : orderList) {
            // 老师手动增加积分
            String remark = String.format("手动添加积分-%s", req.getRemark());
            Long objId = null;
            if (req.getType() == 1) {
                objId = rewardManager.saveReward(order.getId(), userId, req.getReward(), remark);
            } else if (req.getType() == 2) {
                objId = rewardManager.saveFxyReward(order.getId(), userId, order.getUserId(), req.getReward(), remark);
            } else if (req.getType() == 3) {
                objId = rewardManager.saveWordReward(order.getId(), userId, order.getUserId(), req.getReward(), remark);
            }
            if (objId == null) {
                continue;
            }
            orderMap.put(order.getId(), objId);
            UserRemark userRemark = new UserRemark();
            userRemark.setScheduleId(order.getScheduleId());
            userRemark.setUserId(order.getUserId());
            userRemark.setRemarkUserId(userId);
            userRemark.setRemarkContent(req.getRemark());
            remarkList.add(userRemark);
        }
        operationManager.saveLog(userId, orderMap, req.getType() + 13);
        userRemarkMapper.insert(remarkList);
    }
}
