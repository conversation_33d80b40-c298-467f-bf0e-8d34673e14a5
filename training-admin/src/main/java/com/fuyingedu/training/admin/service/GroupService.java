package com.fuyingedu.training.admin.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.fuyingedu.training.admin.model.group.*;
import com.fuyingedu.training.common.enums.*;
import com.fuyingedu.training.common.exception.WebBaseException;
import com.fuyingedu.training.common.model.CommResp;
import com.fuyingedu.training.common.util.RedisKey;
import com.fuyingedu.training.common.util.RespUtils;
import com.fuyingedu.training.entity.Clazz;
import com.fuyingedu.training.entity.Group;
import com.fuyingedu.training.entity.Order;
import com.fuyingedu.training.entity.User;
import com.fuyingedu.training.front.manager.OperationManager;
import com.fuyingedu.training.mapper.ClazzMapper;
import com.fuyingedu.training.mapper.GroupMapper;
import com.fuyingedu.training.mapper.OrderMapper;
import com.fuyingedu.training.mapper.UserMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Predicate;
import java.util.stream.Collectors;

@Service
@Slf4j
public class GroupService {

    @Autowired
    private GroupMapper groupMapper;
    @Autowired
    private ClazzMapper clazzMapper;
    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private UserMapper userMapper;
    @Autowired
    private RedisTemplate<String, String> redisTemplate;
    @Autowired
    private OperationManager operationManager;

    @Transactional(rollbackFor = Exception.class)
    public void save(SaveReq saveReq) {
        Clazz clazz = clazzMapper.selectOne(new QueryWrapper<Clazz>().select(
                Clazz.ID, Clazz.SCHEDULE_ID
        ).eq(Clazz.ID, saveReq.getClazzId()));
        if (clazz == null) {
            throw new WebBaseException(4000, "还没有分班，不能添加小组");
        }
        String hashKey = clazz.getScheduleId() + ":" + clazz.getId();
        long groupSeq = redisTemplate.opsForHash().increment(RedisKey.GROUP_SEQ, hashKey, saveReq.getGroupNum()) - saveReq.getGroupNum();
        List<Group> groupList = new ArrayList<>(saveReq.getGroupNum());
        for (int i = 1; i <= saveReq.getGroupNum(); i++) {
            Group group = new Group();
            group.setClazzId(saveReq.getClazzId());
            group.setGroupName("第" + (groupSeq + i) + "组");
            groupList.add(group);
        }
        groupMapper.insert(groupList);
    }

    @Transactional(rollbackFor = Exception.class)
    public void update(UpdateReq updateReq) {
        Group group = groupMapper.selectOne(new QueryWrapper<Group>().select(
                Group.ID, Group.CLAZZ_ID, Group.MONITOR_ID
        ).eq(Group.ID, updateReq.getId()));
        if (group == null) {
            throw new WebBaseException(RespMetaEnum.PARAM_ERROR);
        }
        Clazz clazz = clazzMapper.selectOne(new QueryWrapper<Clazz>().select(
                Clazz.ID, Clazz.SCHEDULE_ID
        ).eq(Clazz.ID, group.getClazzId()));
        if (clazz == null) {
            throw new WebBaseException(RespMetaEnum.PARAM_ERROR);
        }
        Order order = orderMapper.selectOne(new QueryWrapper<Order>().select(
                        Order.ID
                ).eq(Order.SCHEDULE_ID, clazz.getScheduleId()).eq(Order.GROUP_ID, updateReq.getId())
                .eq(Order.ID, updateReq.getOrderId()));
        if (order == null) {
            throw new WebBaseException(RespMetaEnum.PARAM_ERROR);
        }
        groupMapper.update(new UpdateWrapper<Group>().set(Group.GROUP_NAME, updateReq.getGroupName())
                .set(updateReq.getOrderId() != null, Group.MONITOR_ID, updateReq.getOrderId())
                .eq(Group.ID, updateReq.getId()));
        if (updateReq.getOrderId() != null &&
                (group.getMonitorId() == null || !group.getMonitorId().equals(updateReq.getOrderId()))) {
            orderMapper.update(new UpdateWrapper<Order>()
                    .set(Order.STUDENT_TYPE, StudentType.VOLUNTEER.getCode())
                    .eq(Order.ID, order.getId())
            );
            if (group.getMonitorId() != null) {
                order = orderMapper.selectOne(new QueryWrapper<Order>().select(
                        Order.ID
                ).eq(Order.ID, group.getMonitorId()));
                orderMapper.update(new UpdateWrapper<Order>().set(Order.STUDENT_TYPE, StudentType.NORMAL.getCode())
                        .eq(Order.ID, order.getId()));
            }
        }
    }

    public CommResp<List<UserItemResp>> userList(Long groupId) {
        List<Order> orderList = orderMapper.selectList(new QueryWrapper<Order>().select(
                Order.USER_ID, Order.ID, Order.ORDER_REMARK
        ).eq(Order.GROUP_ID, groupId));
        if (orderList.isEmpty()) {
            return RespUtils.success(Collections.emptyList());
        }
        Map<Long, String> userMap = userMapper.selectList(new QueryWrapper<User>().select(
                User.ID, User.REAL_NAME
        ).in(User.ID, orderList.stream().map(Order::getUserId).toList())).stream().collect(Collectors.toMap(User::getId, User::getRealName));
        return RespUtils.success(orderList.stream().map(order -> {
            UserItemResp userItemResp = new UserItemResp();
            userItemResp.setOrderId(order.getId());
            if (StringUtils.hasText(order.getOrderRemark())) {
                userItemResp.setRealName(order.getOrderRemark());
            } else {
                userItemResp.setRealName(userMap.get(order.getUserId()));
            }
            return userItemResp;
        }).toList());
    }

    @Transactional(rollbackFor = Exception.class)
    public CommResp<List<AllocItemResp>> autoAlloc(Long userId, AllocReq allocReq) {
        Long clazzId = allocReq.getClazzId();
        Clazz clazz = clazzMapper.selectOne(new QueryWrapper<Clazz>().select(
                Clazz.ID
        ).eq(Clazz.ID, clazzId));
        if (clazz == null) {
            throw new WebBaseException(RespMetaEnum.PARAM_ERROR);
        }
        List<Group> groupList = groupMapper.selectList(new QueryWrapper<Group>().select(
                Group.ID, Group.GROUP_NAME
        ).eq(Group.CLAZZ_ID, clazzId));
        if (groupList.isEmpty()) {
            throw new WebBaseException(4000, "没有小组，请先建小组");
        }
        List<Order> orderList = orderMapper.selectList(new QueryWrapper<Order>().select(
                                Order.ID,
                                Order.STUDENT_FLAG, Order.GROUP_ID
                        ).eq(Order.CLAZZ_ID, clazzId)
                        .in(Order.ORDER_STATUS, OrderStatus.NORMAL.getCode(), OrderStatus.MAY_REFUND.getCode())
        );
        if (CollectionUtils.isEmpty(orderList)) {
            throw new WebBaseException(4000, "没有学员，无法分组，请联系管理人员分配学员");
        }
        Boolean b = redisTemplate.opsForValue().setIfAbsent(String.format(RedisKey.ALLOC_GROUP_LOCK, clazz.getId()),
                "", 10, TimeUnit.MINUTES);
        if (Boolean.FALSE.equals(b)) {
            throw new WebBaseException(4000, "正在分配，请勿重复点击");
        }
        try {
            return RespUtils.success(autoAlloc(userId, allocReq, groupList, orderList));
        } finally {
            redisTemplate.delete(String.format(RedisKey.ALLOC_GROUP_LOCK, clazz.getId()));
        }
    }

    private List<AllocItemResp> autoAlloc(Long userId, AllocReq allocReq, List<Group> groupList, List<Order> orderList) {
        List<Order> needAllocOrderList = orderList.stream().filter(order -> {
            if (AllocType.ALL.getCode().equals(allocReq.getAllocType())) {
                return true;
            }
            return order.getGroupId() == null;
        }).toList();
        Map<Long, Map<Byte, List<Order>>> groupOrderMap = orderList.stream().filter(order -> {
                    if (AllocType.NOT_ALLOC.getCode().equals(allocReq.getAllocType())) {
                        return order.getGroupId() != null;
                    }
                    return false;
                })
                .collect(Collectors.groupingBy(Order::getGroupId, Collectors.groupingBy(Order::getStudentFlag)));
        Map<Long, Map<Byte, Long>> oldOrderNumMap = orderList.stream().filter(order -> order.getGroupId() != null)
                .collect(Collectors.groupingBy(Order::getGroupId, Collectors.groupingBy(Order::getStudentFlag, Collectors.counting())));
        long clazzStudentNum = orderList.size();
        long clazzNewNum = orderList.stream().filter(order -> order.getStudentFlag().equals(StudentFlag.NEW.getCode())).count();
        long clazzOldNum = clazzStudentNum - clazzNewNum;
        long groupNum = groupList.size();
        long maxNum = clazzStudentNum / groupNum, maxNewNum = clazzNewNum / groupNum, maxOldNum = clazzOldNum / groupNum;
        List<Order> newOrderList = needAllocOrderList.stream().filter(order -> order.getStudentFlag().equals(StudentFlag.NEW.getCode())).toList();
        List<Order> oldOrderList = needAllocOrderList.stream().filter(order -> order.getStudentFlag().equals(StudentFlag.OLD.getCode())).toList();

        List<Order> notAllocOrderList = alloc(groupOrderMap, newOrderList, groupList, maxNum,
                group -> {
                    int newNum = groupOrderMap.getOrDefault(group.getId(), Collections.emptyMap())
                            .getOrDefault(StudentFlag.NEW.getCode(), Collections.emptyList()).size();
                    return newNum < maxNewNum;
                });
        notAllocOrderList.addAll(alloc(groupOrderMap, oldOrderList, groupList, maxNum,
                group -> {
                    int oldNum = groupOrderMap.getOrDefault(group.getId(), Collections.emptyMap())
                            .getOrDefault(StudentFlag.OLD.getCode(), Collections.emptyList()).size();
                    return oldNum < maxOldNum;
                }));
        notAllocOrderList = alloc(groupOrderMap, notAllocOrderList, groupList, maxNum,
                group -> true);
        int clazzIdx = 0;
        for (Order order : notAllocOrderList) {
            Group group = groupList.get(clazzIdx++);
            alloc(groupOrderMap, order, group);
        }
        List<AllocItemResp> respList = new ArrayList<>(groupList.size());
        for (Group group : groupList) {
            Map<Byte, List<Order>> orderMap = groupOrderMap.getOrDefault(group.getId(), Collections.emptyMap());
            List<Order> oldList = orderMap.getOrDefault(StudentFlag.OLD.getCode(), Collections.emptyList());
            List<Order> newList = orderMap.getOrDefault(StudentFlag.NEW.getCode(), Collections.emptyList());
            Map<Byte, Long> orderNumMap = oldOrderNumMap.getOrDefault(group.getId(), Collections.emptyMap());
            int oldNum = orderNumMap.getOrDefault(StudentFlag.OLD.getCode(), 0L).intValue();
            int newNum = orderNumMap.getOrDefault(StudentFlag.NEW.getCode(), 0L).intValue();
            AllocItemResp resp = new AllocItemResp();
            resp.setGroupId(group.getId());
            resp.setGroupName(group.getGroupName());
            resp.setStudentNum(oldList.size() + newList.size());
            resp.setAllocNum(oldNum + newNum);
            resp.setUnAllocNum(oldList.size() + newList.size() - oldNum - newNum);
            resp.setNewNum(newList.size() - newNum);
            resp.setOldNum(oldList.size() - oldNum);
            respList.add(resp);
        }
        if (AllocSave.SAVE.getCode().equals(allocReq.getAllocSave())) {
            List<Order> updateOrderList = new ArrayList<>();
            for (Map.Entry<Long, Map<Byte, List<Order>>> entry : groupOrderMap.entrySet()) {
                entry.getValue().values().forEach(list -> list.forEach(
                        order -> {
                            Order updateOrder = new Order();
                            updateOrder.setId(order.getId());
                            updateOrder.setGroupId(entry.getKey());
                            updateOrderList.add(updateOrder);
                        }
                ));
            }
            if (!updateOrderList.isEmpty()) {
                orderMapper.updateById(updateOrderList);
                operationManager.allocGroup(userId, updateOrderList, orderList);
            }
        }
        return respList;
    }

    private List<Order> alloc(Map<Long, Map<Byte, List<Order>>> groupOrderMap,
                              List<Order> orderList, List<Group> groupList, long maxNum,
                              Predicate<Group> predicate) {
        List<Order> notAllocOrderList = new ArrayList<>();
        int clazzIdx = 0;
        for (Order order : orderList) {
            while (clazzIdx < groupList.size()) {
                Group group = groupList.get(clazzIdx);
                long studentNum = groupOrderMap.getOrDefault(group.getId(), Collections.emptyMap())
                        .values().stream().mapToInt(List::size).sum();
                if (studentNum < maxNum && predicate.test(group)) {
                    alloc(groupOrderMap, order, group);
                    break;
                } else {
                    clazzIdx++;
                }
            }
            if (clazzIdx >= groupList.size()) {
                notAllocOrderList.add(order);
            }
        }
        return notAllocOrderList;
    }

    private void alloc(Map<Long, Map<Byte, List<Order>>> groupOrderMap, Order order, Group group) {
        Map<Byte, List<Order>> orderFlagMap = groupOrderMap.computeIfAbsent(group.getId(), k -> new HashMap<>());
        List<Order> allocOrderList = orderFlagMap.computeIfAbsent(order.getStudentFlag(), k -> new ArrayList<>());
        allocOrderList.add(order);
    }

    public CommResp<InfoResp> info(Long userId, Long clazzId) {
        Clazz clazz = clazzMapper.selectOne(new QueryWrapper<Clazz>().select(
                Clazz.ID
        ).eq(Clazz.ID, clazzId));
        if (clazz == null) {
            throw new WebBaseException(RespMetaEnum.PARAM_ERROR);
        }
        List<Group> groupList = groupMapper.selectList(new QueryWrapper<Group>().select(
                Group.ID
        ).eq(Group.CLAZZ_ID, clazzId));
        InfoResp infoResp = new InfoResp();
        infoResp.setGroupNum(groupList.size());
        return RespUtils.success(infoResp);
    }
}
