package com.fuyingedu.training.common.util;

public interface RedisKey {

    String PREFIX = "";

    String PHONE = PREFIX + "phone:%s";

    String PHONE_NUM = PREFIX + "phone:num:%s";

    String FXY_TOKEN = PREFIX + "fxy:token:%s";

    String CLAZZ_SEQ = PREFIX + "clazz:seq";

    String GROUP_SEQ = PREFIX + "group:seq";

    String FLUSH_WX_LOCK = PREFIX + "flush:wx:%d";

    String ALLOC_TEACHER_LOCK = PREFIX + "alloc_teacher:%d";

    String ALLOC_ASSISTANT_LOCK = PREFIX + "alloc_assistant:%d";

    String ALLOC_GROUP_LOCK = PREFIX + "alloc_group:%d";

    String FRONT_LOGIN = PREFIX + "front:login:%s";

    String ALLOC_FAIL_RESP = PREFIX + "alloc:fail:%s";
    String SYNC_ORDER_LOCK = PREFIX + "sync_order:%s";

    String SYNC_BJY_LOCK = PREFIX + "sync_bjy:%d";

    String SIGN_LOCK = PREFIX + "sign:%d";
}
