package com.fuyingedu.training.common.model;

import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class CommResp<T> {

    /**
     * 状态码
     */
    private final int code;
    /**
     * 状态码描述
     */
    private final String message;
    /**
     * 数据
     */
    private T data;
    /**
     * 分页信息
     */
    private PageInfo pageInfo;

    public CommResp(int code, String message, T data) {
        this.code = code;
        this.message = message;
        this.data = data;
    }

    @Getter
    public static class PageInfo {
        /**
         * 当前页码
         */
        private long pageNum;
        /**
         * 每页数量
         */
        private long pageSize;
        /**
         * 总页数
         */
        private long totalPage;
        /**
         * 总记录数
         */
        private long totalRecord;

        public PageInfo() {}

        public PageInfo(long pageNum, long pageSize) {
            this.pageNum = pageNum;
            this.pageSize = pageSize;
        }

        public void setTotalRecord(long totalRecord) {
            this.totalRecord = totalRecord;
            this.totalPage = (totalRecord - 1) / getPageSize() + 1;
        }

        public long getPageNum() {
            return Math.max(pageNum, 1L);
        }

        public long getPageSize() {
            if (pageSize < 1) {
                return 12;
            }
            return pageSize;
        }
    }

}
