package com.fuyingedu.training.common.util;

public class StrUtils {

    public static String codeCartNum(String cartNum) {
        if (cartNum == null || cartNum.length() < 18) {
            return cartNum;
        }
        return cartNum.substring(0, 7) +
                "********" +
                cartNum.substring(14);
    }

    public static String underlineToCamel(String word) {
        if (word == null) {
            return null;
        }
        String[] split = word.split("_");
        StringBuilder sb = new StringBuilder(word.length());
        for (String s : split) {
            char[] chars = s.toCharArray();
            if(chars[0] >='a' && chars[0] <= 'z'){
                chars[0] -= 32;
            }
            sb.append(chars);
        }
        return sb.toString();
    }

    public static String camelToUnderline(String name) {
        StringBuilder buf = new StringBuilder();
        for (int i = 0; i < name.length(); ++i) {
            char ch = name.charAt(i);
            if (ch >= 'A' && ch <= 'Z') {
                char newChar = (char) (ch + 32);
                if (i > 0) {
                    buf.append('_');
                }
                buf.append(newChar);
            } else {
                buf.append(ch);
            }
        }
        return buf.toString();
    }

}
