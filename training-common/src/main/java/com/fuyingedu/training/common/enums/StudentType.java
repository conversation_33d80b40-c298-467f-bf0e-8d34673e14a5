package com.fuyingedu.training.common.enums;

public enum StudentType {

    NORMAL((byte) 1, "普通学员"),
    VOLUNTEER((byte) 2, "陪跑志愿者");

    private final Byte code;
    private final String desc;

    StudentType(Byte code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Byte getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

}
