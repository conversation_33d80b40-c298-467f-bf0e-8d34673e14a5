package com.fuyingedu.training.common.model;

import com.fuyingedu.training.common.util.StrUtils;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class SortPageReq extends PageReq {

    /**
     * 需要排序的字段名
     */
    private String sortField;
    /**
     * 排序方式 asc - 正序/desc - 倒叙
     */
    private String sortOrder;

    public String getSortField() {
        if (sortField == null) {
            return "id";
        }
        return StrUtils.camelToUnderline(sortField);
    }

    public String getSortOrder() {
        if (sortOrder == null) {
            return "desc";
        }
        return sortOrder;
    }
}
