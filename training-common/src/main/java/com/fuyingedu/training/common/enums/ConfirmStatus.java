package com.fuyingedu.training.common.enums;

public enum ConfirmStatus {
    NOT_CONFIRM((byte) 1, "未确认"),
    MY_CONFIRM((byte) 2, "自己确认"),
    TEACHER_CONFIRM((byte) 3, "助教确认");

    ConfirmStatus(Byte code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    private final Byte code;
    private final String desc;

    public String getDesc() {
        return desc;
    }

    public Byte getCode() {
        return code;
    }
}
