package com.fuyingedu.training.common.enums;

import java.time.LocalDateTime;

public enum ScheduleStatus {

    UN_START((byte) 1, "未开始"),

    START((byte) 2, "进行中"),

    END((byte) 3, "已结束");

    private final Byte code;

    private final String desc;

    ScheduleStatus(Byte code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Byte getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static ScheduleStatus convert(LocalDateTime beginTime, LocalDateTime endTime, LocalDateTime now) {
        if (beginTime.isAfter(now)) {
            return UN_START;
        } else if (endTime.isAfter(now)) {
            return START;
        }
        return END;
    }
}
