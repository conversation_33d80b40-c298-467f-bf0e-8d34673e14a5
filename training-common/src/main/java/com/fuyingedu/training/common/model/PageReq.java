package com.fuyingedu.training.common.model;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class PageReq {

    /**
     * 当前页码
     */
    private long pageNum;
    /**
     * 每页数量
     */
    private long pageSize;

    public long getPageNum() {
        if (pageNum < 1) {
            return 1;
        }
        return pageNum;
    }

    public long getOffset() {
        return (getPageNum() - 1) * getPageSize();
    }

    public long getPageSize() {
        if (pageSize < 1) {
            return 100;
        }
        return pageSize;
    }
}
