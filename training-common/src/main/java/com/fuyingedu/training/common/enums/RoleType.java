package com.fuyingedu.training.common.enums;

import lombok.Getter;

@Getter
public enum RoleType {

    UNKNOWN((long) -1, "未知"),
    TEACHER(1L, "老师"),
    ADMIN(2L, "管理员"),
    ;
    private final Long code;
    private final String desc;
    RoleType(Long code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static RoleType convert(Long code) {
        for (RoleType role : RoleType.values()) {
            if (role.getCode().equals(code)) {
                return role;
            }
        }
        return UNKNOWN;
    }
}
