package com.fuyingedu.training.common.exception;


import com.fuyingedu.training.common.enums.RespMetaEnum;
import lombok.Getter;

@Getter
public class WebBaseException extends RuntimeException {

    private final int status;
    private final String msg;

    public WebBaseException(int status, String msg) {
        super(msg);
        this.status = status;
        this.msg = msg;
    }

    public WebBaseException(int status, String msg, Throwable throwable) {
        super(msg, throwable);
        this.status = status;
        this.msg = msg;
    }

    public WebBaseException(RespMetaEnum code) {
        this(code.getStatus(), code.getMsg());
    }

    public WebBaseException(RespMetaEnum code, Throwable throwable) {
        this(code.getStatus(), code.getMsg(), throwable);
    }

}
