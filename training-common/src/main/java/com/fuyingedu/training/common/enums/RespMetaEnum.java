package com.fuyingedu.training.common.enums;

import lombok.Getter;

@Getter
public enum RespMetaEnum {

    SUCCESS(200, "请求成功"),
    NOT_LOGIN(444, "未登陆或登陆过期"),
    NO_AUTH(445, "没有权限"),


    PARAM_ERROR(1000, "参数错误"),
    TIMEOUT(1001, "网络繁忙，请稍后再试"),
    NO_REGISTER(1002, "您还不是扶鹰教育公司的用户，无法进入训练营服务"),
    NO_SCHEDULE(1003, "没有排期"),

    LOGIN_FAIL(2001, "登陆失败"),
    REGISTER_FAIL_REPEAT_USERNAME(2002, "用户名重复"),
    NO_USER(2003, "用户不存在"),
    REPEAT_PWD_ERROR(2004, "密码错误超过当日上限，请明日再试"),
    PWD_ERROR(2005, "账户或密码错误"),
    USER_FROZEN(2006, "用户被冻结"),
    PASSWORD_FAILED(2008, "密码错误"),

    NO_CLAZZ(3001, "没有分班，请先进行分班"),
    ;

    private final int status;
    private final String msg;

    RespMetaEnum(int status, String msg) {
        this.status = status;
        this.msg = msg;
    }

}
