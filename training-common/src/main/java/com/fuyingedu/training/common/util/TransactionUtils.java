package com.fuyingedu.training.common.util;

import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;

import java.util.function.Supplier;

public class TransactionUtils {

    private static PlatformTransactionManager transactionManager;
    private static TransactionDefinition transactionDefinition;

    public static void init(PlatformTransactionManager transactionManager, TransactionDefinition transactionDefinition) {
        TransactionUtils.transactionManager = transactionManager;
        TransactionUtils.transactionDefinition = transactionDefinition;
    }

    public static TransactionStatus open() {
        return transactionManager.getTransaction(transactionDefinition);
    }

    public static void commit(TransactionStatus transactionStatus) {
        transactionManager.commit(transactionStatus);
    }

    public static void rollback(TransactionStatus transactionStatus) {
        transactionManager.rollback(transactionStatus);
    }

    public static void execute(Runnable runnable) {
        TransactionStatus transactionStatus = open();
        try {
            runnable.run();
            commit(transactionStatus);
        } catch (Exception e) {
            rollback(transactionStatus);
            throw e;
        }
    }
}
