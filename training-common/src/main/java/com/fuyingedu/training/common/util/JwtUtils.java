package com.fuyingedu.training.common.util;

import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.interfaces.Claim;
import com.auth0.jwt.interfaces.DecodedJWT;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.Map;

public class JwtUtils {

    private static Algorithm algorithm;
    private static Map<String, Object> header;
    private static JWTVerifier verifier;


    public static void init(String secret) {
        algorithm = Algorithm.HMAC256(secret);
        header = Map.of("typ", "JWT", "alg", "HS256");
        verifier = JWT.require(algorithm).build();
    }

    public static String createAdminToken(Object userId) {
        LocalDateTime expiredTime = LocalDateTime.now().plusDays(1);
        return createToken(Map.of("userId", userId, "type", "Admin",
                "expiredTime", DateUtils.toMillis(expiredTime)), expiredTime);
    }

    public static String createFrontToken(Object userId) {
        LocalDateTime expiredTime = LocalDateTime.now().plusDays(14);
        return JwtUtils.createToken(Map.of("userId", userId,
                "expiredTime", DateUtils.toMillis(expiredTime)), expiredTime);
    }

    public static String createToken(Map<String, Object> param, LocalDateTime expireDate) {
        return createToken(param, DateUtils.asDate(expireDate));
    }

    public static String createToken(Map<String, Object> params, Date expireTime) {
        var builder = JWT.create().withHeader(header);
        // 设置 载荷 Payload
        builder.withClaim("params", params);
        return builder
                // 生成签名的时间
                .withIssuedAt(new Date())
                // 签名过期的时间
                .withExpiresAt(expireTime)
                // 签名Signature
                .sign(algorithm);
    }

    public static Map<String, Object> verifyToken(String token) {
        DecodedJWT jwt = verifier.verify(token);
        Map<String, Claim> claims = jwt.getClaims();
        Claim claim = claims.get("params");
        return claim.asMap();
    }
}
