package com.fuyingedu.training.common.util;

import com.fuyingedu.training.common.enums.RespMetaEnum;
import com.fuyingedu.training.common.model.CommResp;
import lombok.Getter;

import java.util.List;

@Getter
public class RespUtils {

    public static CommResp<?> success() {
        return success(null);
    }

    public static <T> CommResp<T> success(T data) {
        return new CommResp<>(RespMetaEnum.SUCCESS.getStatus(), RespMetaEnum.SUCCESS.getMsg(), data);
    }

    public static <T> CommResp<List<T>> success(long current, long size, long total, List<T> data) {
        CommResp.PageInfo pageInfo = new CommResp.PageInfo(current, size);
        pageInfo.setTotalRecord(total);
        CommResp<List<T>> resp = new CommResp<>(RespMetaEnum.SUCCESS.getStatus(), RespMetaEnum.SUCCESS.getMsg(), data);
        resp.setPageInfo(pageInfo);
        return resp;
    }

    public static <T> CommResp<T> success(String msg, T data) {
        return new CommResp<>(RespMetaEnum.SUCCESS.getStatus(), msg, data);
    }

    public static <T> CommResp<T> warning(RespMetaEnum meta) {
        return new CommResp<>(meta.getStatus(), meta.getMsg(), null);
    }

    public static <T> CommResp<T> warning(int status, String msg) {
        return new CommResp<>(status, msg, null);
    }
}
