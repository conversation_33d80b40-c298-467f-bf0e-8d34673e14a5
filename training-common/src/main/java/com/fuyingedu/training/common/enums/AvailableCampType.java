package com.fuyingedu.training.common.enums;

public enum AvailableCampType {
    ALL((byte) 1, "所有"),
    SPECIFIED((byte) 2, "指定");

    private final Byte code;
    private final String desc;

    AvailableCampType(Byte code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Byte getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
