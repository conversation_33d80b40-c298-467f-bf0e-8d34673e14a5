package com.fuyingedu.training.common.enums;

public enum TeacherType {
    TEACHER((byte) 1, "辅导老师"),
    ASSISTANT((byte) 2, "助教");

    private final Byte code;
    private final String desc;

    TeacherType(Byte code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Byte getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
