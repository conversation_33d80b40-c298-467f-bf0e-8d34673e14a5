package com.fuyingedu.training.common.util;

import com.fuyingedu.training.common.enums.RespMetaEnum;
import com.fuyingedu.training.common.exception.WebBaseException;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ThreadPoolExecutor;

@Slf4j
public class AsyncUtils {

    private static ThreadPoolExecutor executor;

    public static void init(ThreadPoolExecutor executor) {
        AsyncUtils.executor = executor;
    }

    public static void execute(Runnable runnable, String taskName) {
        try {
            executor.execute(runnable);
        } catch (Exception e) {
            log.error(taskName + "异步任务执行失败", e);
        }
    }

    public static void waitAllTasks(CompletableFuture<?>... tasks) {
        try {
            CompletableFuture.allOf(tasks).get();
        } catch (InterruptedException | ExecutionException e) {
            throw new WebBaseException(RespMetaEnum.TIMEOUT, e);
        }
    }

    public static void waitAllTasks(List<CompletableFuture<?>> futureList) {
        waitAllTasks(futureList.toArray(new CompletableFuture[0]));
    }
}
