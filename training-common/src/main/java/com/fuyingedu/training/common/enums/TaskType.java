package com.fuyingedu.training.common.enums;

public enum TaskType {
    ENROLLMENT((byte) 1, "签到"),
    PUNCH((byte) 2, "打卡"),
    HOMEWORK((byte) 3, "作业"),
    OUT_PUNCH((byte) 5, "接口打卡"),
    WORD((byte) 6, "单词训练营"),
    ENGLISH((byte) 7, "英语母语式训练营"),
    ;

    private final Byte code;
    private final String desc;

    TaskType(Byte code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Byte getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static TaskType getByCode(Byte code) {
        for (TaskType taskType : TaskType.values()) {
            if (taskType.getCode().equals(code)) {
                return taskType;
            }
        }
        return null;
    }
}
