package com.fuyingedu.training.common.enums;

public enum RewardLogType {
    PLUS((byte) 1, "加积分"),
    MINUS((byte) 2, "减积分");

    private final Byte code;
    private final String desc;

    RewardLogType(Byte code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Byte getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
