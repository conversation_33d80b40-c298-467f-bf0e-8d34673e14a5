package com.fuyingedu.training.common.enums;

public enum CardType {

    ID_CARD((byte) 1, "身份证"),
    PASSPORT((byte) 2, "护照"),
    HK_CARD((byte) 3, "港澳通行证");

    private final Byte code;
    private final String desc;

    CardType(Byte code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Byte getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
