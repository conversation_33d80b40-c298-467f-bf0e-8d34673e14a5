package com.fuyingedu.training.common.enums;

public enum AvailableType {
    TEACHER((byte) 1, "老师"),
    MEDAL((byte) 2, "勋章"),
    TASK_TEMPLATE((byte) 3, "任务模板"),
    ;

    final Byte code;
    final String desc;

    public Byte getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    AvailableType(Byte code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
