package com.fuyingedu.training.common.util;

import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/12/26 20:22
 */
public class IpUtils {

    /**
     * ip地址转成long型数字
     * 将IP地址转化成整数的方法如下：
     * 1、通过String的split方法按.分隔得到4个长度的数组
     * 2、通过左移位操作（<<）给每一段的数字加权，第一段的权为2的24次方，第二段的权为2的16次方，第三段的权为2的8次方，最后一段的权为1
     */
    public static long ipToLong(String strIp) {
        String[] ip = strIp.split("\\.");
        return (Long.parseLong(ip[0]) << 24) + (Long.parseLong(ip[1]) << 16) + (Long.parseLong(ip[2]) << 8) + Long.parseLong(ip[3]);
    }

    /**
     * 将十进制整数形式转换成127.0.0.1形式的ip地址
     * 将整数形式的IP地址转化成字符串的方法如下：
     * 1、将整数值进行右移位操作（>>>），右移24位，右移时高位补0，得到的数字即为第一段IP。
     * 2、通过与操作符（&）将整数值的高8位设为0，再右移16位，得到的数字即为第二段IP。
     * 3、通过与操作符吧整数值的高16位设为0，再右移8位，得到的数字即为第三段IP。
     * 4、通过与操作符吧整数值的高24位设为0，得到的数字即为第四段IP。
     */
    public static String longToIP(long longIp) {
        // 直接右移24位
        return (longIp >>> 24) +
                "." +
                // 将高8位置0，然后右移16位
                ((longIp & 0x00FFFFFF) >>> 16) +
                "." +
                // 将高16位置0，然后右移8位
                ((longIp & 0x0000FFFF) >>> 8) +
                "." +
                // 将高24位置0
                (longIp & 0x000000FF);
    }

    public static List<String> getAllIP() {
        try {
            List<String> ipList = new ArrayList<>();
            Enumeration<NetworkInterface> networkInterfaces = NetworkInterface.getNetworkInterfaces();
            while (networkInterfaces.hasMoreElements()) {
                NetworkInterface ni = networkInterfaces.nextElement();
                Enumeration<InetAddress> inetAddresses = ni.getInetAddresses();
                while (inetAddresses.hasMoreElements()) {
                    InetAddress ia = inetAddresses.nextElement();
                    if (!ia.isLinkLocalAddress() && !ia.isLoopbackAddress() && ia.isSiteLocalAddress()) {
                        ipList.add(ia.getHostAddress());
                    }
                }
            }
            return ipList;
        } catch (SocketException e) {
            throw new IPException("Get ip failed", e);
        }
    }

    public static String getIP(String prefix) {
        List<String> allIP = getAllIP();
        for (String ip : allIP) {
            if (ip.startsWith(prefix)) {
                return ip;
            }
        }
        return null;
    }

    private static class IPException extends RuntimeException {

        public IPException(String msg, Throwable cause) {
            super(msg, cause);
        }

    }

    public static void main(String[] args) {
        System.out.println(ipToLong("127.0.0.1"));
    }
}
