package com.fuyingedu.training.mapper;

import com.fuyingedu.training.dto.word.OrderGroupRet;
import com.fuyingedu.training.entity.LiveSignRecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 直播签到记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-16
 */
public interface LiveSignRecordMapper extends BaseMapper<LiveSignRecord> {

    List<OrderGroupRet> groupByOrderIds(@Param("orderIds") Set<Long> orderIds);
}
