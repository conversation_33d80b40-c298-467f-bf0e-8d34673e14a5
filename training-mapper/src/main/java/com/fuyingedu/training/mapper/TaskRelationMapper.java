package com.fuyingedu.training.mapper;

import com.fuyingedu.training.dto.task.RelationTaskRet;
import com.fuyingedu.training.entity.TaskRelation;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 任务和班级关联关系表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-05
 */
public interface TaskRelationMapper extends BaseMapper<TaskRelation> {

    List<RelationTaskRet> groupTaskId(@Param("scheduleId") Long scheduleId, @Param("assistantIds") Set<Long> assistantIds);
}
