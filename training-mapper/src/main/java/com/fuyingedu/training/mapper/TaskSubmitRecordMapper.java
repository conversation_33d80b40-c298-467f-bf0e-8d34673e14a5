package com.fuyingedu.training.mapper;

import com.fuyingedu.training.dto.task.TaskDoneNumRet;
import com.fuyingedu.training.entity.TaskSubmitRecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 用户的打卡记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-20
 */
public interface TaskSubmitRecordMapper extends BaseMapper<TaskSubmitRecord> {

    List<TaskDoneNumRet> groupTaskDoneNum(@Param("orderId") Long orderId, @Param("taskIds") List<Long> taskIds);

}
