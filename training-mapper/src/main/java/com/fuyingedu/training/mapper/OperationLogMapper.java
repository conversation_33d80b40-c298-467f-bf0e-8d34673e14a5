package com.fuyingedu.training.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fuyingedu.training.dto.order.LogRet;
import com.fuyingedu.training.entity.OperationLog;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 操作日志表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-19
 */
public interface OperationLogMapper extends BaseMapper<OperationLog> {

    List<LogRet> groupByBatchId(
            @Param("type") byte type,
            @Param("scheduleId") Long scheduleId,
            @Param("teacherId") Long teacherId
    );

    IPage<OperationLog> pageSubList(
            IPage<OperationLog> page,
            @Param("scheduleId") Long scheduleId,
            @Param("teacherId") Long teacherId,
            @Param("teacherType") Byte teacherType,
            @Param("userInfo") String userInfo,
            @Param("studentInfo") String studentInfo,
            @Param("orderRemark") String orderRemark
            );
}
