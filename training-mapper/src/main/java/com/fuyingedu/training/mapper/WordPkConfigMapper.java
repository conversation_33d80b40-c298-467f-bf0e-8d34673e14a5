package com.fuyingedu.training.mapper;

import com.fuyingedu.training.dto.GroupNumRet;
import com.fuyingedu.training.entity.WordPkConfig;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 单词训练营PK配置表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-23
 */
public interface WordPkConfigMapper extends BaseMapper<WordPkConfig> {

    List<GroupNumRet> groupByScheduleId(@Param("list") List<Long> scheduleIds);
}
