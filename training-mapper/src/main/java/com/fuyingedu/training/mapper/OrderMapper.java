package com.fuyingedu.training.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fuyingedu.training.dto.order.*;
import com.fuyingedu.training.entity.Order;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 服务单表-学生的班级等关联关系 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-31
 */
public interface OrderMapper extends BaseMapper<Order> {

    /**
     * 大SQL数量大了之后建议优化
     */
    IPage<OrderItemRet> pageListForOrder(IPage<?> page, @Param("param") OrderListParam param);

    IPage<ContactItemRet> pageContactList(IPage<?> page, @Param("param")ContactListParam param);

    IPage<RecordItemRet> pageRecordList(IPage<?> page, @Param("param") RecordListParam param);

    List<StudentNumRet> groupStudentNum(@Param("ids") List<Long> studentIds);
}
