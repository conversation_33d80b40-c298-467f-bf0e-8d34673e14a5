package com.fuyingedu.training.mapper;

import com.fuyingedu.training.dto.word.OrderGroupRet;
import com.fuyingedu.training.entity.WordReward;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 单词训练营奖励积分表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-10
 */
public interface WordRewardMapper extends BaseMapper<WordReward> {

    Integer getRewardByUserIds(@Param("userIds") Set<Long> userIds);
    Integer getRewardByOrderIds(@Param("orderIds") Set<Long> orderIds);

    List<OrderGroupRet> groupRewardByOrderIds(@Param("orderIds") Set<Long> orderIds);
}
