package com.fuyingedu.training.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.fuyingedu.training.dto.fuying.FxyItemRet;
import com.fuyingedu.training.dto.fuying.FxyListParam;
import com.fuyingedu.training.dto.fuying.OrderRet;
import com.fuyingedu.training.dto.fuying.UserInfoRet;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

@DS("fuying")
public interface FuyingMapper {

    UserInfoRet queryUserInfo(@Param("phoneNum") String phone, @Param("unionId") String unionId);

    List<OrderRet> queryOrderList(@Param("pidList") Set<Long> pidList,
                                  @Param("scheduleIdList") Set<Long> scheduleIdList,
                                  @Param("payTime") Long payTime);

    List<FxyItemRet> queryFxyList(@Param("param") FxyListParam param);

    FxyItemRet queryFxyByOpenId(@Param("openId") String openId);
}
