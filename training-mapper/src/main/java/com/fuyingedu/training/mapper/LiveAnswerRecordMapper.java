package com.fuyingedu.training.mapper;

import com.fuyingedu.training.dto.word.OrderGroupRet;
import com.fuyingedu.training.entity.LiveAnswerRecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 直播答题器记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-21
 */
public interface LiveAnswerRecordMapper extends BaseMapper<LiveAnswerRecord> {

    List<OrderGroupRet> groupByOrderIds(@Param("orderIds") Set<Long> orderIds);
}
