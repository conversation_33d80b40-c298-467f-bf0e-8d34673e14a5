package com.fuyingedu.training.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 操作日志表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-21
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("training_operation_log")
public class OperationLog {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 数据新建时间
     */
    private LocalDateTime createdTime;

    /**
     * 批次ID
     */
    private Long batchId;

    /**
     * 服务单ID
     */
    private Long orderId;

    /**
     * 操作的用户ID
     */
    private Long userId;

    /**
     * 修改之前的排期
     */
    private Long prevSchedule;

    /**
     * 修改之后的排期
     */
    private Long nextSchedule;

    /**
     * 修改之前的辅导老师
     */
    private Long prevTeacher;

    /**
     * 修改之后的辅导老师
     */
    private Long nextTeacher;

    /**
     * 修改之前的助教
     */
    private Long prevAssistant;

    /**
     * 修改之后的助教
     */
    private Long nextAssistant;

    /**
     * 修改前的值
     */
    private Long prevContent;

    /**
     * 修改后的值
     */
    private Long nextContent;

    /**
     * 1-改期 2-分配辅导老师 3-关闭订单 4-核销订单 5-删除备注 6-修改辅导老师 7-备注 8-分班 9-分组 10-改班 11-改组 12-修改订单状态-可能退单 13-修改订单状态-正常
     * 14-加小红花 15-加小太阳 16-加word鹰 17-优秀作业 18-奖状
     */
    private Integer operationType;

    /**
     * 1-正常 2-删除
     */
    private Byte logStatus;

    public static final String ID = "id";

    public static final String CREATED_TIME = "created_time";

    public static final String BATCH_ID = "batch_id";

    public static final String ORDER_ID = "order_id";

    public static final String USER_ID = "user_id";

    public static final String PREV_SCHEDULE = "prev_schedule";

    public static final String NEXT_SCHEDULE = "next_schedule";

    public static final String PREV_TEACHER = "prev_teacher";

    public static final String NEXT_TEACHER = "next_teacher";

    public static final String PREV_ASSISTANT = "prev_assistant";

    public static final String NEXT_ASSISTANT = "next_assistant";

    public static final String PREV_CONTENT = "prev_content";

    public static final String NEXT_CONTENT = "next_content";

    public static final String OPERATION_TYPE = "operation_type";

    public static final String LOG_STATUS = "log_status";
}
