package com.fuyingedu.training.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 排期教室表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-16
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("training_schedule_room")
public class ScheduleRoom {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 数据新建时间
     */
    private LocalDateTime createdTime;

    /**
     * 房间创建时间
     */
    private LocalDateTime roomTime;

    /**
     * 教室id
     */
    private Long roomId;

    /**
     * 排课id
     */
    private Long scheduleId;

    /**
     * 教室名称
     */
    private String roomName;

    /**
     * 老师参与码
     */
    private String teacherCode;

    /**
     * 助教参与码
     */
    private String adminCode;

    /**
     * 学员参与码
     */
    private String studentCode;

    /**
     * 重播视频数
     */
    private Integer repeatNum;

    /**
     * 房间数
     */
    private Integer roomNum;

    /**
     * 测试题数
     */
    private Integer quizNum;

    public static final String ID = "id";

    public static final String CREATED_TIME = "created_time";

    public static final String ROOM_TIME = "room_time";

    public static final String ROOM_ID = "room_id";

    public static final String SCHEDULE_ID = "schedule_id";

    public static final String ROOM_NAME = "room_name";

    public static final String TEACHER_CODE = "teacher_code";

    public static final String ADMIN_CODE = "admin_code";

    public static final String STUDENT_CODE = "student_code";

    public static final String REPEAT_NUM = "repeat_num";

    public static final String ROOM_NUM = "room_num";

    public static final String QUIZ_NUM = "quiz_num";
}
