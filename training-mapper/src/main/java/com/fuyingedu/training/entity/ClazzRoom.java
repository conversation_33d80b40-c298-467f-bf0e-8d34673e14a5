package com.fuyingedu.training.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 班级教室表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-11
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("training_clazz_room")
public class ClazzRoom {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 数据新建时间
     */
    private LocalDateTime createdTime;

    /**
     * 排课id
     */
    private Long scheduleId;

    /**
     * 班级id
     */
    private Long clazzId;

    /**
     * 教室id
     */
    private Long roomId;

    private Integer groupId;

    /**
     * 教室名称
     */
    private String roomName;

    /**
     * 老师参与码
     */
    private String teacherCode;

    /**
     * 助教参与码
     */
    private String adminCode;

    /**
     * 学员参与码
     */
    private String studentCode;

    public static final String ID = "id";

    public static final String CREATED_TIME = "created_time";

    public static final String SCHEDULE_ID = "schedule_id";

    public static final String CLAZZ_ID = "clazz_id";

    public static final String ROOM_ID = "room_id";

    public static final String GROUP_ID = "group_id";

    public static final String ROOM_NAME = "room_name";

    public static final String TEACHER_CODE = "teacher_code";

    public static final String ADMIN_CODE = "admin_code";

    public static final String STUDENT_CODE = "student_code";
}
