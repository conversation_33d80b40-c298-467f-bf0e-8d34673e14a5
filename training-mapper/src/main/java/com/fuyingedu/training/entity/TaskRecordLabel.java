package com.fuyingedu.training.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 任务记录的标签表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-18
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("training_task_record_label")
public class TaskRecordLabel {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 数据新建时间
     */
    private LocalDateTime createdTime;

    /**
     * 点评表的ID
     */
    private Long remarkId;

    /**
     * 标签表的ID
     */
    private Long labelId;

    /**
     * 该标签所属的服务单ID
     */
    private Long orderId;

    /**
     * 添加该标签的用户ID
     */
    private Long createUserId;

    public static final String ID = "id";

    public static final String CREATED_TIME = "created_time";

    public static final String REMARK_ID = "remark_id";

    public static final String LABEL_ID = "label_id";

    public static final String ORDER_ID = "order_id";

    public static final String CREATE_USER_ID = "create_user_id";
}
