package com.fuyingedu.training.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 服务单表-学生的班级等关联关系
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-07
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("training_order")
public class Order {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 数据新建时间
     */
    private LocalDateTime createdTime;

    /**
     * 数据更新时间
     */
    private LocalDateTime updatedTime;

    /**
     * 来源服务单时间
     */
    private LocalDateTime orderTime;

    /**
     * 来源服务单号
     */
    private String orderNo;

    /**
     * 1-新训 2-复训
     */
    private Byte studentFlag;

    /**
     * 训练营表主键
     */
    private Long campId;

    /**
     * 排期表主键
     */
    private Long scheduleId;

    /**
     * 用户表主键
     */
    private Long userId;

    /**
     * 学员表主键
     */
    private Long studentId;

    /**
     * 辅导老师
     */
    private Long teacherId;

    /**
     * 小班表主键
     */
    private Long clazzId;

    /**
     * 小组表主键
     */
    private Long groupId;

    /**
     * 1-正常 2-已关闭 3-已退款 4-可能退款
     */
    private Byte orderStatus;

    /**
     * 1-普通学员 2-陪跑志愿者
     */
    private Byte studentType;

    /**
     * 是否加导师微信 1-没有加 2-已加
     */
    private Byte teacherWxStatus;

    /**
     * 是否加助教微信 1-没有加 2-已加
     */
    private Byte assistantWxStatus;

    /**
     * 是否加群微信 1-没有加 2-已加
     */
    private Byte groupWxStatus;

    /**
     * 确认情况 1-未确认 2-自己确认 3-助教确认 4-不来
     */
    private Byte confirmStatus;

    /**
     * 确认时间
     */
    private LocalDateTime confirmTime;

    /**
     * 服务单备注
     */
    private String orderRemark;

    /**
     * 核销时间 空表示未核销
     */
    private LocalDateTime signTime;

    /**
     * 来源订单号
     */
    private String realOrderNo;

    public static final String ID = "id";

    public static final String CREATED_TIME = "created_time";

    public static final String UPDATED_TIME = "updated_time";

    public static final String ORDER_TIME = "order_time";

    public static final String ORDER_NO = "order_no";

    public static final String STUDENT_FLAG = "student_flag";

    public static final String CAMP_ID = "camp_id";

    public static final String SCHEDULE_ID = "schedule_id";

    public static final String USER_ID = "user_id";

    public static final String STUDENT_ID = "student_id";

    public static final String TEACHER_ID = "teacher_id";

    public static final String CLAZZ_ID = "clazz_id";

    public static final String GROUP_ID = "group_id";

    public static final String ORDER_STATUS = "order_status";

    public static final String STUDENT_TYPE = "student_type";

    public static final String TEACHER_WX_STATUS = "teacher_wx_status";

    public static final String ASSISTANT_WX_STATUS = "assistant_wx_status";

    public static final String GROUP_WX_STATUS = "group_wx_status";

    public static final String CONFIRM_STATUS = "confirm_status";

    public static final String CONFIRM_TIME = "confirm_time";

    public static final String ORDER_REMARK = "order_remark";

    public static final String SIGN_TIME = "sign_time";

    public static final String REAL_ORDER_NO = "real_order_no";
}
