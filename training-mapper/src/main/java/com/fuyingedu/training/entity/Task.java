package com.fuyingedu.training.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 任务表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-10
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("training_task")
public class Task {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 数据新建时间
     */
    private LocalDateTime createdTime;

    /**
     * 数据更新时间
     */
    private LocalDateTime updatedTime;

    /**
     * 训练营表的主键ID
     */
    private Long scheduleId;

    /**
     * 任务级别 1-排期 2-大班 3-小班
     */
    private Byte taskLevel;

    /**
     * 1 所有班级 2 指定班级
     */
    private Byte clazzType;

    /**
     * 任务类型 1-签到 2-打卡 3-作业5-接口打卡
     */
    private Byte taskType;

    /**
     * 创建人的用户ID
     */
    private Long createdUserId;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 任务开启时间
     */
    private LocalDate startDate;

    /**
     * 任务截止时间
     */
    private LocalDate endDate;

    /**
     * 任务介绍
     */
    private String taskContent;

    /**
     * 任务积分
     */
    private Integer taskReward;

    /**
     * 扶小鹰小太阳
     */
    private Integer fxyReward;

    /**
     * Word鹰积分值
     */
    private Integer wordReward;

    /**
     * 任务（签到、打卡等）当天的开始时间
     */
    private LocalTime startTime;

    /**
     * 任务（签到、打卡等）当天的截止时间
     */
    private LocalTime endTime;

    /**
     * 上传案例，存放url的JSON数组
     */
    private String caseUrls;

    /**
     * 上传项目的约束条件，存放JSON数组
     */
    private String uploadItems;

    public static final String ID = "id";

    public static final String CREATED_TIME = "created_time";

    public static final String UPDATED_TIME = "updated_time";

    public static final String SCHEDULE_ID = "schedule_id";

    public static final String TASK_LEVEL = "task_level";

    public static final String CLAZZ_TYPE = "clazz_type";

    public static final String TASK_TYPE = "task_type";

    public static final String CREATED_USER_ID = "created_user_id";

    public static final String TASK_NAME = "task_name";

    public static final String START_DATE = "start_date";

    public static final String END_DATE = "end_date";

    public static final String TASK_CONTENT = "task_content";

    public static final String TASK_REWARD = "task_reward";

    public static final String FXY_REWARD = "fxy_reward";

    public static final String WORD_REWARD = "word_reward";

    public static final String START_TIME = "start_time";

    public static final String END_TIME = "end_time";

    public static final String CASE_URLS = "case_urls";

    public static final String UPLOAD_ITEMS = "upload_items";
}
