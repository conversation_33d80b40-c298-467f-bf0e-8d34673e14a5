package com.fuyingedu.training.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 单词训练营奖励积分记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-05
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("training_word_reward_log")
public class WordRewardLog {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 服务单ID
     */
    private Long orderId;

    /**
     * 奖励积分
     */
    private Integer rewardNum;

    /**
     * 备注
     */
    private String remark;

    /**
     * 数据新建时间
     */
    private LocalDateTime createdTime;

    /**
     * 1-已发送MQ 2-发送成功
     */
    private Byte logStatus;

    public static final String ID = "id";

    public static final String USER_ID = "user_id";

    public static final String ORDER_ID = "order_id";

    public static final String REWARD_NUM = "reward_num";

    public static final String REMARK = "remark";

    public static final String CREATED_TIME = "created_time";

    public static final String LOG_STATUS = "log_status";
}
