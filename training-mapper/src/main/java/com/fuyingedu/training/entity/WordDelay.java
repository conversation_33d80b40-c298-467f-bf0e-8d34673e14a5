package com.fuyingedu.training.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 排期延期天数记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-30
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("training_word_delay")
public class WordDelay {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 数据新建时间
     */
    private LocalDateTime createdTime;

    /**
     * 排课表ID
     */
    private Long scheduleId;

    /**
     * 实际天数
     */
    private Integer realDays;

    /**
     * 延期天数
     */
    private Integer delayDays;

    public static final String ID = "id";

    public static final String CREATED_TIME = "created_time";

    public static final String SCHEDULE_ID = "schedule_id";

    public static final String REAL_DAYS = "real_days";

    public static final String DELAY_DAYS = "delay_days";
}
