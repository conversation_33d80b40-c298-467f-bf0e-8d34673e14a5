package com.fuyingedu.training.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 小班表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-16
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("training_clazz")
public class Clazz {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 数据新建时间
     */
    private LocalDateTime createdTime;

    /**
     * 数据更新时间
     */
    private LocalDateTime updatedTime;

    /**
     * 排期表主键
     */
    private Long scheduleId;

    /**
     * 辅导老师-教师表主键
     */
    private Long teacherId;

    /**
     * 助教老师-教师表主键
     */
    private Long assistantId;

    /**
     * 小班名称
     */
    private String className;

    /**
     * 班级群二维码地址
     */
    private String wxUrl;

    /**
     * 班级群ID
     */
    private Long wxGroupId;

    public static final String ID = "id";

    public static final String CREATED_TIME = "created_time";

    public static final String UPDATED_TIME = "updated_time";

    public static final String SCHEDULE_ID = "schedule_id";

    public static final String TEACHER_ID = "teacher_id";

    public static final String ASSISTANT_ID = "assistant_id";

    public static final String CLASS_NAME = "class_name";

    public static final String WX_URL = "wx_url";

    public static final String WX_GROUP_ID = "wx_group_id";
}
