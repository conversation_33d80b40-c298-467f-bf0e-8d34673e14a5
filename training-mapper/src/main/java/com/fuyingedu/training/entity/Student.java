package com.fuyingedu.training.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 学生表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-28
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("training_student")
public class Student {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 数据新建时间
     */
    private LocalDateTime createdTime;

    /**
     * 数据更新时间
     */
    private LocalDateTime updatedTime;

    /**
     * 姓名
     */
    private String realName;

    /**
     * 新扶鹰学员id
     */
    private Long newOuterId;

    /**
     * 旧扶鹰学员id
     */
    private Long oldOuterId;

    /**
     * 证件类型 1-身份证 2-护照 3-港澳通行证
     */
    private Byte cardType;

    /**
     * 证件号
     */
    private String cardNum;

    public static final String ID = "id";

    public static final String CREATED_TIME = "created_time";

    public static final String UPDATED_TIME = "updated_time";

    public static final String REAL_NAME = "real_name";

    public static final String NEW_OUTER_ID = "new_outer_id";

    public static final String OLD_OUTER_ID = "old_outer_id";

    public static final String CARD_TYPE = "card_type";

    public static final String CARD_NUM = "card_num";
}
