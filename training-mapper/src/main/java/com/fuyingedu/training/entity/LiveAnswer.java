package com.fuyingedu.training.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 直播答题器配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-21
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("training_live_answer")
public class LiveAnswer {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 数据新建时间
     */
    private LocalDateTime createdTime;

    /**
     * 直播id
     */
    private Long liveId;

    /**
     * 任务积分
     */
    private Integer taskReward;

    /**
     * 扶小鹰小太阳
     */
    private Integer fxyReward;

    /**
     * Word鹰积分值
     */
    private Integer wordReward;

    /**
     * 1-关闭 2-开启
     */
    private Byte answerStatus;

    /**
     * 1-未同步 2-已同步
     */
    private Byte syncStatus;

    public static final String ID = "id";

    public static final String CREATED_TIME = "created_time";

    public static final String LIVE_ID = "live_id";

    public static final String TASK_REWARD = "task_reward";

    public static final String FXY_REWARD = "fxy_reward";

    public static final String WORD_REWARD = "word_reward";

    public static final String ANSWER_STATUS = "answer_status";

    public static final String SYNC_STATUS = "sync_status";
}
