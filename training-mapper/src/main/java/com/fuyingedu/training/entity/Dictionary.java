package com.fuyingedu.training.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 字典表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-22
 */
@Getter
@Setter
@TableName("training_dictionary")
public class Dictionary {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 字典键
     */
    private String dictKey;

    /**
     * 字典值
     */
    private String dictValue;

    /**
     * 父节点ID
     */
    private Integer parentId;

    /**
     * 字典顺序
     */
    private Integer dictOrder;

    /**
     * 删除标记
     */
    private Byte deletedFlag;

    public static final String ID = "id";

    public static final String DICT_KEY = "dict_key";

    public static final String DICT_VALUE = "dict_value";

    public static final String PARENT_ID = "parent_id";

    public static final String DICT_ORDER = "dict_order";

    public static final String DELETED_FLAG = "deleted_flag";
}
