package com.fuyingedu.training.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 单词训练营PK配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-23
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("training_word_pk_config")
public class WordPkConfig {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 排期表ID
     */
    private Long scheduleId;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime stopTime;

    public static final String ID = "id";

    public static final String SCHEDULE_ID = "schedule_id";

    public static final String START_TIME = "start_time";

    public static final String STOP_TIME = "stop_time";
}
