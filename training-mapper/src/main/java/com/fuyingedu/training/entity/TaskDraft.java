package com.fuyingedu.training.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 任务的草稿
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-12
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("training_task_draft")
public class TaskDraft {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 数据新建时间
     */
    private LocalDateTime createdTime;

    /**
     * 数据更新时间
     */
    private LocalDateTime updatedTime;

    /**
     * 服务单表主键
     */
    private Long orderId;

    /**
     * 任务表主键
     */
    private Long taskId;

    /**
     * 草稿内容
     */
    private String draftContent;

    public static final String ID = "id";

    public static final String CREATED_TIME = "created_time";

    public static final String UPDATED_TIME = "updated_time";

    public static final String ORDER_ID = "order_id";

    public static final String TASK_ID = "task_id";

    public static final String DRAFT_CONTENT = "draft_content";
}
