package com.fuyingedu.training.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 服务单和奖状关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-11
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("training_order_assistant")
public class OrderAssistant {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 数据新建时间
     */
    private LocalDateTime createdTime;

    /**
     * 数据更新时间
     */
    private LocalDateTime updatedTime;

    /**
     * 服务表主键
     */
    private Long orderId;

    /**
     * 用户表主键
     */
    private Long userId;

    public static final String ID = "id";

    public static final String CREATED_TIME = "created_time";

    public static final String UPDATED_TIME = "updated_time";

    public static final String ORDER_ID = "order_id";

    public static final String USER_ID = "user_id";
}
