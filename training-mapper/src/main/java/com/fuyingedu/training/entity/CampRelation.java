package com.fuyingedu.training.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 训练营和扶鹰关联关系表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-31
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("training_camp_relation")
public class CampRelation {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 数据新建时间
     */
    private LocalDateTime createdTime;

    /**
     * 数据更新时间
     */
    private LocalDateTime updatedTime;

    /**
     * 训练营表主键
     */
    private Long campId;

    /**
     * 对应的课程ID
     */
    private Long pid;

    /**
     * 对应的分类名称
     */
    private String relationName;

    /**
     * 对应的课程名称
     */
    private String relationTitle;

    /**
     * 1-有效 2-无效
     */
    private Byte relationStatus;

    public static final String ID = "id";

    public static final String CREATED_TIME = "created_time";

    public static final String UPDATED_TIME = "updated_time";

    public static final String CAMP_ID = "camp_id";

    public static final String PID = "pid";

    public static final String RELATION_NAME = "relation_name";

    public static final String RELATION_TITLE = "relation_title";

    public static final String RELATION_STATUS = "relation_status";
}
