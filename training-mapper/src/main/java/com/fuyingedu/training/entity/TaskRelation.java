package com.fuyingedu.training.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <p>
 * 任务和班级关联关系表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-09
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("training_task_relation")
public class TaskRelation {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 数据新建时间
     */
    private LocalDateTime createdTime;

    /**
     * 数据更新时间
     */
    private LocalDateTime updatedTime;

    /**
     * 任务表主键
     */
    private Long taskId;

    /**
     * 排期ID
     */
    private Long scheduleId;

    /**
     * 辅导老师ID
     */
    private Long teacherId;

    /**
     * 助教老师ID
     */
    private Long assistantId;

    /**
     * 班级ID
     */
    private Long clazzId;

    public static final String ID = "id";

    public static final String CREATED_TIME = "created_time";

    public static final String UPDATED_TIME = "updated_time";

    public static final String TASK_ID = "task_id";

    public static final String SCHEDULE_ID = "schedule_id";

    public static final String TEACHER_ID = "teacher_id";

    public static final String ASSISTANT_ID = "assistant_id";

    public static final String CLAZZ_ID = "clazz_id";
}
