package com.fuyingedu.training.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 直播签到记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-16
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("training_live_sign_record")
public class LiveSignRecord {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 数据新建时间
     */
    private LocalDateTime createdTime;

    /**
     * 直播id
     */
    private Long liveId;

    /**
     * 服务单id
     */
    private Long orderId;

    public static final String ID = "id";

    public static final String CREATED_TIME = "created_time";

    public static final String LIVE_ID = "live_id";

    public static final String ORDER_ID = "order_id";
}
