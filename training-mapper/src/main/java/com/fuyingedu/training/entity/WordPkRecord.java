package com.fuyingedu.training.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <p>
 * 单词训练营PK记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-30
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("training_word_pk_record")
public class WordPkRecord {

    /**
     * PK表ID
     */
    private Long pkId;

    /**
     * 服务单表ID
     */
    private Long orderId;

    /**
     * 数据新建时间
     */
    private LocalDateTime createdTime;

    /**
     * 数据更新时间
     */
    private LocalDateTime updatedTime;

    /**
     * 获胜次数
     */
    private Integer winningNum;

    public static final String PK_ID = "pk_id";

    public static final String ORDER_ID = "order_id";

    public static final String CREATED_TIME = "created_time";

    public static final String UPDATED_TIME = "updated_time";

    public static final String WINNING_NUM = "winning_num";
}
