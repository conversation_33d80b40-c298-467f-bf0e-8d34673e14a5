package com.fuyingedu.training.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 排期词书关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-30
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("training_word_schedule")
public class WordSchedule {

    /**
     * 数据新建时间
     */
    private LocalDateTime createdTime;

    /**
     * 排课表ID
     */
    @TableId("schedule_id")
    private Long scheduleId;

    /**
     * 词书ID
     */
    private Long wordId;

    /**
     * 单词书的天数
     */
    private Integer wordDays;

    /**
     * 单词书的直播天JSON列表
     */
    private String liveDays;

    public static final String CREATED_TIME = "created_time";

    public static final String SCHEDULE_ID = "schedule_id";

    public static final String WORD_ID = "word_id";

    public static final String WORD_DAYS = "word_days";

    public static final String LIVE_DAYS = "live_days";
}
