package com.fuyingedu.training.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import java.time.LocalTime;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 任务模板表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-10
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("training_task_template")
public class TaskTemplate {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 数据新建时间
     */
    private LocalDateTime createdTime;

    /**
     * 数据更新时间
     */
    private LocalDateTime updatedTime;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 适用的训练营类型 1-所有 2-指定
     */
    private Byte campType;

    /**
     * 任务类型 1-签到 2-打卡 3-作业
     */
    private Byte taskType;

    /**
     * 任务介绍
     */
    private String taskContent;

    /**
     * 任务积分
     */
    private Integer taskReward;

    /**
     * 扶小鹰小太阳
     */
    private Integer fxyReward;

    private Integer wordReward;

    /**
     * 任务（签到、打卡等）当天的开始时间
     */
    private LocalTime startTime;

    /**
     * 任务（签到、打卡等）当天的截止时间
     */
    private LocalTime endTime;

    /**
     * 上传案例，存放url的JSON数组
     */
    private String caseUrls;

    /**
     * 上传项目的约束条件，存放JSON数组
     */
    private String uploadItems;

    public static final String ID = "id";

    public static final String CREATED_TIME = "created_time";

    public static final String UPDATED_TIME = "updated_time";

    public static final String TASK_NAME = "task_name";

    public static final String CAMP_TYPE = "camp_type";

    public static final String TASK_TYPE = "task_type";

    public static final String TASK_CONTENT = "task_content";

    public static final String TASK_REWARD = "task_reward";

    public static final String FXY_REWARD = "fxy_reward";

    public static final String WORD_REWARD = "word_reward";

    public static final String START_TIME = "start_time";

    public static final String END_TIME = "end_time";

    public static final String CASE_URLS = "case_urls";

    public static final String UPLOAD_ITEMS = "upload_items";
}
