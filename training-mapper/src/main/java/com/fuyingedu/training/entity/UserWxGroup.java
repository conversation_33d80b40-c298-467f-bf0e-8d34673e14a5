package com.fuyingedu.training.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 用户的企业微信群信息
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-23
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("training_user_wx_group")
public class UserWxGroup {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 数据新建时间
     */
    private LocalDateTime createdTime;

    /**
     * 用户表ID
     */
    private Long userId;

    /**
     * 企业微信群ID
     */
    private String groupId;

    /**
     * 企业微信群名称
     */
    private String groupName;

    /**
     * 企业微信群新建时间
     */
    private LocalDateTime groupCreatedTime;

    public static final String ID = "id";

    public static final String CREATED_TIME = "created_time";

    public static final String USER_ID = "user_id";

    public static final String GROUP_ID = "group_id";

    public static final String GROUP_NAME = "group_name";

    public static final String GROUP_CREATED_TIME = "group_created_time";
}
