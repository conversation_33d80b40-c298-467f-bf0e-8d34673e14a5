package com.fuyingedu.training.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 排期老师表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-04
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("training_schedule_teacher")
public class ScheduleTeacher {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 数据新建时间
     */
    private LocalDateTime createdTime;

    /**
     * 数据更新时间
     */
    private LocalDateTime updatedTime;

    /**
     * 排期表主键
     */
    private Long scheduleId;

    /**
     * 辅导老师-教师表主键
     */
    private Long teacherId;

    /**
     * 讲师类型 1-辅导老师 2-助教
     */
    private Byte teacherType;

    /**
     * 班级最大人数，可超过
     */
    private Integer gradeNum;

    /**
     * 进行学员分配时的权重
     */
    private Integer gradeWeight;

    public static final String ID = "id";

    public static final String CREATED_TIME = "created_time";

    public static final String UPDATED_TIME = "updated_time";

    public static final String SCHEDULE_ID = "schedule_id";

    public static final String TEACHER_ID = "teacher_id";

    public static final String TEACHER_TYPE = "teacher_type";

    public static final String GRADE_NUM = "grade_num";

    public static final String GRADE_WEIGHT = "grade_weight";
}
