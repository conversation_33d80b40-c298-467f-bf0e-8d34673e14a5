package com.fuyingedu.training.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import java.time.LocalTime;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 排期表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-08
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("training_schedule")
public class Schedule {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 数据新建时间
     */
    private LocalDateTime createdTime;

    /**
     * 数据更新时间
     */
    private LocalDateTime updatedTime;

    /**
     * 训练营表主键
     */
    private Long campId;

    /**
     * 外部的排期ID
     */
    private Long scheduleId;

    /**
     * 外部的班级ID
     */
    private Long clazzId;

    /**
     * 外部班级名称
     */
    private String clazzName;

    /**
     * 排期名称
     */
    private String scheduleName;

    private String relationName;

    /**
     * 排期开启时间
     */
    private LocalDateTime startTime;

    /**
     * 排期截止时间
     */
    private LocalDateTime endTime;

    /**
     * 直播计划数量
     */
    private Integer liveNum;

    /**
     * 排期任务数量
     */
    private Integer taskNum;

    /**
     * 大班数量
     */
    private Integer gradeNum;

    /**
     * 小班数量
     */
    private Integer classNum;

    /**
     * 直播方式 1-公司直播 2-导师直播
     */
    private Byte liveType;

    /**
     * 直播端口 1-腾讯会议 2-保利威 3-百家云
     */
    private Byte livePort;

    /**
     * 直播房间号
     */
    private Long liveRoom;

    /**
     * 直播开始时间
     */
    private LocalTime liveTime;

    public static final String ID = "id";

    public static final String CREATED_TIME = "created_time";

    public static final String UPDATED_TIME = "updated_time";

    public static final String CAMP_ID = "camp_id";

    public static final String SCHEDULE_ID = "schedule_id";

    public static final String CLAZZ_ID = "clazz_id";

    public static final String CLAZZ_NAME = "clazz_name";

    public static final String SCHEDULE_NAME = "schedule_name";

    public static final String RELATION_NAME = "relation_name";

    public static final String START_TIME = "start_time";

    public static final String END_TIME = "end_time";

    public static final String LIVE_NUM = "live_num";

    public static final String TASK_NUM = "task_num";

    public static final String GRADE_NUM = "grade_num";

    public static final String CLASS_NUM = "class_num";

    public static final String LIVE_TYPE = "live_type";

    public static final String LIVE_PORT = "live_port";

    public static final String LIVE_ROOM = "live_room";

    public static final String LIVE_TIME = "live_time";
}
