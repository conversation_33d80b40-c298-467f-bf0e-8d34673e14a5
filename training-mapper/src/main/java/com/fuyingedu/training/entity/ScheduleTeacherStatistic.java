package com.fuyingedu.training.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 排期-教师学生数量统计表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-09
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("training_schedule_teacher_statistic")
public class ScheduleTeacherStatistic {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 数据新建时间
     */
    private LocalDateTime createdTime;

    /**
     * 排期表主键
     */
    private Long scheduleId;

    /**
     * 教师表主键
     */
    private Long teacherId;

    /**
     * 1 未开始 2 进行中 3 已结束
     */
    private Byte scheduleStatus;

    /**
     * 服务单数
     */
    private Integer orderNum;

    public static final String ID = "id";

    public static final String CREATED_TIME = "created_time";

    public static final String SCHEDULE_ID = "schedule_id";

    public static final String TEACHER_ID = "teacher_id";

    public static final String SCHEDULE_STATUS = "schedule_status";

    public static final String ORDER_NUM = "order_num";
}
