package com.fuyingedu.training.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-26
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("training_wx_external_user")
public class WxExternalUser {

    /**
     * 老师表ID
     */
    private String userId;

    private String unionId;

    private String externalUserId;

    /**
     * 外部联系人的类型，1表示该外部联系人是微信用户，2表示该外部联系人是企业微信用户
     */
    private Byte type;

    public static final String USER_ID = "user_id";

    public static final String UNION_ID = "union_id";

    public static final String EXTERNAL_USER_ID = "external_user_id";

    public static final String TYPE = "type";
}
