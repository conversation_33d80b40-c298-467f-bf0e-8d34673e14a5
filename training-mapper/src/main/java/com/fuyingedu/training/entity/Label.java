package com.fuyingedu.training.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 评价标签表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-28
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("training_label")
public class Label {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 数据新建时间
     */
    private LocalDateTime createdTime;

    /**
     * 数据更新时间
     */
    private LocalDateTime updatedTime;

    /**
     * 标签名称
     */
    private String labelName;

    /**
     * 标签类型 1-表扬 2-待改进
     */
    private Byte labelType;

    /**
     * 标签图
     */
    private String mediaUrl;

    /**
     * 标签状态 1-有效 2-无效
     */
    private Byte labelStatus;

    public static final String ID = "id";

    public static final String CREATED_TIME = "created_time";

    public static final String UPDATED_TIME = "updated_time";

    public static final String LABEL_NAME = "label_name";

    public static final String LABEL_TYPE = "label_type";

    public static final String MEDIA_URL = "media_url";

    public static final String LABEL_STATUS = "label_status";
}
