package com.fuyingedu.training.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 服务单联系记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-03
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("training_order_remark")
public class OrderRemark {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 数据新建时间
     */
    private LocalDateTime createdTime;

    /**
     * 用户表主键
     */
    private Long recordUserId;

    /**
     * 服务单表主键
     */
    private Long orderId;

    /**
     * 记录类型 1-待确认 3-助教确认 4-不来
     */
    private Byte remarkType;

    /**
     * 备注内容
     */
    private String remarkContent;

    public static final String ID = "id";

    public static final String CREATED_TIME = "created_time";

    public static final String RECORD_USER_ID = "record_user_id";

    public static final String ORDER_ID = "order_id";

    public static final String REMARK_TYPE = "remark_type";

    public static final String REMARK_CONTENT = "remark_content";
}
