package com.fuyingedu.training.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 学员备注表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-07
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("training_user_remark")
public class UserRemark {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 数据新建时间
     */
    private LocalDateTime createdTime;

    /**
     * 数据更新时间
     */
    private LocalDateTime updatedTime;

    /**
     * 排期表主键
     */
    private Long scheduleId;

    /**
     * 用户表主键
     */
    private Long userId;

    /**
     * 备注人，用户表主键
     */
    private Long remarkUserId;

    /**
     * 1-有效 2-无效
     */
    private Byte remarkStatus;

    /**
     * 备注
     */
    private String remarkContent;

    /**
     * 图片地址Json数组
     */
    private String imageUrls;

    /**
     * 视频地址Json数组
     */
    private String videoUrls;

    public static final String ID = "id";

    public static final String CREATED_TIME = "created_time";

    public static final String UPDATED_TIME = "updated_time";

    public static final String SCHEDULE_ID = "schedule_id";

    public static final String USER_ID = "user_id";

    public static final String REMARK_USER_ID = "remark_user_id";

    public static final String REMARK_STATUS = "remark_status";

    public static final String REMARK_CONTENT = "remark_content";

    public static final String IMAGE_URLS = "image_urls";

    public static final String VIDEO_URLS = "video_urls";
}
