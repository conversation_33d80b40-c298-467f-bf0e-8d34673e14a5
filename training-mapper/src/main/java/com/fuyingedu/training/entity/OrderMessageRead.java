package com.fuyingedu.training.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 消息读取记录
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-08
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("training_order_message_read")
public class OrderMessageRead {

    /**
     * orderId
     */
    private Long id;

    /**
     * 系统消息
     */
    private Long systemMessage;

    /**
     * 点赞消息
     */
    private Long likeMessage;

    /**
     * 点评消息
     */
    private Long remarkMessage;

    public static final String ID = "id";

    public static final String SYSTEM_MESSAGE = "system_message";

    public static final String LIKE_MESSAGE = "like_message";

    public static final String REMARK_MESSAGE = "remark_message";
}
