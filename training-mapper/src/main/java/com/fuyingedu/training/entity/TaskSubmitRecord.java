package com.fuyingedu.training.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDate;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 用户的打卡记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-23
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("training_task_submit_record")
public class TaskSubmitRecord {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 数据新建时间
     */
    private LocalDateTime createdTime;

    /**
     * 班级表主键
     */
    private Long clazzId;

    /**
     * 服务单表主键
     */
    private Long orderId;

    /**
     * 打卡时间
     */
    private LocalDate submitDate;

    /**
     * 任务表主键
     */
    private Long taskId;

    /**
     * 任务类型 1-签到 2-打卡 3-作业 5-接口打卡
     */
    private Byte taskType;

    /**
     * 提交的文本信息
     */
    private String submitContent;

    /**
     * 提交的素材，存放JSON数组
     */
    private String submitUrls;

    /**
     * 点赞数量
     */
    private Integer likeNum;

    /**
     * 1-普通作业 2-优秀作业
     */
    private Byte recordType;

    /**
     * 提交作业的用户Id
     */
    private Long userId;

    public static final String ID = "id";

    public static final String CREATED_TIME = "created_time";

    public static final String CLAZZ_ID = "clazz_id";

    public static final String ORDER_ID = "order_id";

    public static final String SUBMIT_DATE = "submit_date";

    public static final String TASK_ID = "task_id";

    public static final String TASK_TYPE = "task_type";

    public static final String SUBMIT_CONTENT = "submit_content";

    public static final String SUBMIT_URLS = "submit_urls";

    public static final String LIKE_NUM = "like_num";

    public static final String RECORD_TYPE = "record_type";

    public static final String USER_ID = "user_id";
}
