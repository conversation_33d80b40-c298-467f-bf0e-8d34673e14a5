package com.fuyingedu.training.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 直播记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-12
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("training_live")
public class Live {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 数据新建时间
     */
    private LocalDateTime createdTime;

    /**
     * 数据更新时间
     */
    private LocalDateTime updatedTime;

    /**
     * 排期表主键
     */
    private Long scheduleId;

    /**
     * 任务名称
     */
    private String liveName;

    /**
     * 直播开始时间
     */
    private LocalDateTime startTime;

    /**
     * 直播方式 1-公司直播 2-导师直播
     */
    private Byte liveType;

    /**
     * 直播端口 1-腾讯会议 2-保利威 3-百家云
     */
    private Byte livePort;

    /**
     * 直播房间号
     */
    private String liveRoom;

    /**
     * 直播持续时间
     */
    private Integer liveDuration;

    /**
     * 重播开始时间
     */
    private LocalDateTime repeatStartTime;

    /**
     * 重播截止时间
     */
    private LocalDateTime repeatEndTime;

    /**
     * 直播间信息
     */
    private String liveInfo;

    /**
     * 直播密码
     */
    private String livePassword;

    /**
     * 回放的视频ID
     */
    private Long repeatVideoId;

    /**
     * 回放链接
     */
    private String repeatUrl;

    public static final String ID = "id";

    public static final String CREATED_TIME = "created_time";

    public static final String UPDATED_TIME = "updated_time";

    public static final String SCHEDULE_ID = "schedule_id";

    public static final String LIVE_NAME = "live_name";

    public static final String START_TIME = "start_time";

    public static final String LIVE_TYPE = "live_type";

    public static final String LIVE_PORT = "live_port";

    public static final String LIVE_ROOM = "live_room";

    public static final String LIVE_DURATION = "live_duration";

    public static final String REPEAT_START_TIME = "repeat_start_time";

    public static final String REPEAT_END_TIME = "repeat_end_time";

    public static final String LIVE_INFO = "live_info";

    public static final String LIVE_PASSWORD = "live_password";

    public static final String REPEAT_VIDEO_ID = "repeat_video_id";

    public static final String REPEAT_URL = "repeat_url";
}
