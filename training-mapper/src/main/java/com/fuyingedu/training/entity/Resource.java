package com.fuyingedu.training.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 资源表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-03
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("training_resource")
public class Resource {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 资源
     */
    private String resourceKey;

    /**
     * 资源描述
     */
    private String resourceLabel;

    /**
     * 资源类型：1-一级菜单(目录)；2-二级菜单；3-操作; 4-数据查看
     */
    private Byte resourceType;

    /**
     * 父菜单id
     */
    private Long parentId;

    /**
     * 删除标记，0-未删除；1-删除
     */
    private Byte deletedFlag;

    public static final String ID = "id";

    public static final String RESOURCE_KEY = "resource_key";

    public static final String RESOURCE_LABEL = "resource_label";

    public static final String RESOURCE_TYPE = "resource_type";

    public static final String PARENT_ID = "parent_id";

    public static final String DELETED_FLAG = "deleted_flag";
}
