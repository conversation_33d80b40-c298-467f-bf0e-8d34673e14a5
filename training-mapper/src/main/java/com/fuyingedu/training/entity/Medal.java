package com.fuyingedu.training.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 奖状表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-07
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("training_medal")
public class Medal {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 数据新建时间
     */
    private LocalDateTime createdTime;

    /**
     * 数据更新时间
     */
    private LocalDateTime updatedTime;

    /**
     * 奖状名称
     */
    private String medalName;

    /**
     * 模板名称
     */
    private String templateName;

    /**
     * 奖状图片
     */
    private String medalIcon;

    /**
     * 奖状描述
     */
    private String medalContent;

    /**
     * 勋章状态：1-启用，2-停用
     */
    private Byte medalStatus;

    /**
     * 适用的训练营类型 1-所有 2-指定训练营 3-指定排期
     */
    private Byte medalType;

    public static final String ID = "id";

    public static final String CREATED_TIME = "created_time";

    public static final String UPDATED_TIME = "updated_time";

    public static final String MEDAL_NAME = "medal_name";

    public static final String TEMPLATE_NAME = "template_name";

    public static final String MEDAL_ICON = "medal_icon";

    public static final String MEDAL_CONTENT = "medal_content";

    public static final String MEDAL_STATUS = "medal_status";

    public static final String MEDAL_TYPE = "medal_type";
}
