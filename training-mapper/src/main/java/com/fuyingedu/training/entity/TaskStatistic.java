package com.fuyingedu.training.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDate;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 任务统计信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-08
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("training_task_statistic")
public class TaskStatistic {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 数据新建时间
     */
    private LocalDateTime createdTime;

    /**
     * 数据更新时间
     */
    private LocalDateTime updatedTime;

    /**
     * 任务表的主键ID
     */
    private Long taskId;

    /**
     * 班级表的主键ID
     */
    private Long clazzId;

    /**
     * 记录日期
     */
    private LocalDate recordDate;

    /**
     * 当日生成记录人数
     */
    private Integer recordNum;

    public static final String ID = "id";

    public static final String CREATED_TIME = "created_time";

    public static final String UPDATED_TIME = "updated_time";

    public static final String TASK_ID = "task_id";

    public static final String CLAZZ_ID = "clazz_id";

    public static final String RECORD_DATE = "record_date";

    public static final String RECORD_NUM = "record_num";
}
