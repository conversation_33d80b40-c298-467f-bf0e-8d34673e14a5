package com.fuyingedu.training.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 直播答题关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-17
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("training_live_quiz_relation")
public class LiveQuizRelation {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 数据新建时间
     */
    private LocalDateTime createdTime;

    /**
     * 直播id
     */
    private Long liveId;

    /**
     * 答题id
     */
    private Long quizId;

    /**
     * 教室id
     */
    private Long roomId;

    /**
     * 1-未同步 2-已同步
     */
    private Byte syncStatus;

    public static final String ID = "id";

    public static final String CREATED_TIME = "created_time";

    public static final String LIVE_ID = "live_id";

    public static final String QUIZ_ID = "quiz_id";

    public static final String ROOM_ID = "room_id";

    public static final String SYNC_STATUS = "sync_status";
}
