package com.fuyingedu.training.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 作业和打卡的点评表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-20
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("training_task_record_remark")
public class TaskRecordRemark {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 数据新建时间
     */
    private LocalDateTime createdTime;

    /**
     * 任务记录ID
     */
    private Long recordId;

    /**
     * 点评的文本信息
     */
    private String remarkContent;

    /**
     * 点评的素材，存放JSON数组
     */
    private String remarkUrls;

    /**
     * 点评创建人ID
     */
    private Long createUserId;

    public static final String ID = "id";

    public static final String CREATED_TIME = "created_time";

    public static final String RECORD_ID = "record_id";

    public static final String REMARK_CONTENT = "remark_content";

    public static final String REMARK_URLS = "remark_urls";

    public static final String CREATE_USER_ID = "create_user_id";
}
