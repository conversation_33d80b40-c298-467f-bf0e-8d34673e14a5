package com.fuyingedu.training.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 扶小鹰信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-18
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("training_fxy")
public class Fxy {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private LocalDateTime createdTime;

    private String openId;

    private String nickName;

    private String accountInfo;

    private String iconUrl;

    public static final String ID = "id";

    public static final String CREATED_TIME = "created_time";

    public static final String OPEN_ID = "open_id";

    public static final String NICK_NAME = "nick_name";

    public static final String ACCOUNT_INFO = "account_info";

    public static final String ICON_URL = "icon_url";
}
