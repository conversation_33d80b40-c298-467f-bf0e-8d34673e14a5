package com.fuyingedu.training.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 任务记录的点赞表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-19
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("training_task_record_like")
public class TaskRecordLike {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 数据新建时间
     */
    private LocalDateTime createdTime;

    /**
     * 任务记录ID
     */
    private Long recordId;

    /**
     * 用户表ID
     */
    private Long orderId;

    public static final String ID = "id";

    public static final String CREATED_TIME = "created_time";

    public static final String RECORD_ID = "record_id";

    public static final String ORDER_ID = "order_id";
}
