package com.fuyingedu.training.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 职务角色_资源映射表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-03
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("training_role_resource")
public class RoleResource {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 职务角色ID
     */
    private Long roleId;

    /**
     * 资源ID
     */
    private Long resourceId;

    public static final String ID = "id";

    public static final String ROLE_ID = "role_id";

    public static final String RESOURCE_ID = "resource_id";
}
