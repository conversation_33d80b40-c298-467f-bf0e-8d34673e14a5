package com.fuyingedu.training.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 服务单和奖状关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-07
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("training_order_medal")
public class OrderMedal {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 数据新建时间
     */
    private LocalDateTime createdTime;

    /**
     * 数据更新时间
     */
    private LocalDateTime updatedTime;

    /**
     * 排期表主键
     */
    private Long scheduleId;

    /**
     * 用户表主键
     */
    private Long userId;

    /**
     * 服务表主键
     */
    private Long orderId;

    /**
     * 奖状表表主键
     */
    private Long medalId;

    public static final String ID = "id";

    public static final String CREATED_TIME = "created_time";

    public static final String UPDATED_TIME = "updated_time";

    public static final String SCHEDULE_ID = "schedule_id";

    public static final String USER_ID = "user_id";

    public static final String ORDER_ID = "order_id";

    public static final String MEDAL_ID = "medal_id";
}
