package com.fuyingedu.training.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 训练营表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-03
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("training_camp")
public class Camp {

    private Long id;

    /**
     * 数据新建时间
     */
    private LocalDateTime createdTime;

    /**
     * 数据更新时间
     */
    private LocalDateTime updatedTime;

    /**
     * 训练营名称
     */
    private String campName;

    private String relationName;

    /**
     * 1-普通 2-扶小鹰陪跑 3-单词训练
     */
    private Byte campType;

    /**
     * 是否需要绑定学员 0-不需要 1-需要
     */
    private Byte needBinding;

    /**
     * 是否需要绑定扶小鹰 0-不需要 1-需要
     */
    private Byte needFxy;

    /**
     * 1-辅导老师 2-助教 3-都不
     */
    private Byte wxPriority;

    /**
     * 训练营图片
     */
    private String mainMediaUrl;

    /**
     * 辅导老师要求
     */
    private String campContent;

    /**
     * 排期数量
     */
    private Integer scheduleNum;

    /**
     * 自动核销天数 0表示不自动核销
     */
    private Integer signDays;

    public static final String ID = "id";

    public static final String CREATED_TIME = "created_time";

    public static final String UPDATED_TIME = "updated_time";

    public static final String CAMP_NAME = "camp_name";

    public static final String RELATION_NAME = "relation_name";

    public static final String CAMP_TYPE = "camp_type";

    public static final String NEED_BINDING = "need_binding";

    public static final String NEED_FXY = "need_fxy";

    public static final String WX_PRIORITY = "wx_priority";

    public static final String MAIN_MEDIA_URL = "main_media_url";

    public static final String CAMP_CONTENT = "camp_content";

    public static final String SCHEDULE_NUM = "schedule_num";

    public static final String SIGN_DAYS = "sign_days";
}
