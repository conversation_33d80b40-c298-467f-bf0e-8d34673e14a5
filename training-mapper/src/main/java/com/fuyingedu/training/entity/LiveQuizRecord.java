package com.fuyingedu.training.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 直播答题记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-17
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("training_live_quiz_record")
public class LiveQuizRecord {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 数据新建时间
     */
    private LocalDateTime createdTime;

    /**
     * 直播id
     */
    private Long relationId;

    /**
     * 服务单id
     */
    private Long orderId;

    public static final String ID = "id";

    public static final String CREATED_TIME = "created_time";

    public static final String RELATION_ID = "relation_id";

    public static final String ORDER_ID = "order_id";
}
