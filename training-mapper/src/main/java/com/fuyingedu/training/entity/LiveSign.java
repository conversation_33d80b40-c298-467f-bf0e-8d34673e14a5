package com.fuyingedu.training.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 直播签到表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-17
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("training_live_sign")
public class LiveSign {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 数据新建时间
     */
    private LocalDateTime createdTime;

    /**
     * 1-关闭 2-开启
     */
    private Byte signStatus;

    /**
     * 1-未同步 2-已同步
     */
    private Byte syncStatus;

    /**
     * 直播id
     */
    private Long liveId;

    /**
     * 任务积分
     */
    private Integer taskReward;

    /**
     * 扶小鹰小太阳
     */
    private Integer fxyReward;

    /**
     * Word鹰积分值
     */
    private Integer wordReward;

    public static final String ID = "id";

    public static final String CREATED_TIME = "created_time";

    public static final String SIGN_STATUS = "sign_status";

    public static final String SYNC_STATUS = "sync_status";

    public static final String LIVE_ID = "live_id";

    public static final String TASK_REWARD = "task_reward";

    public static final String FXY_REWARD = "fxy_reward";

    public static final String WORD_REWARD = "word_reward";
}
