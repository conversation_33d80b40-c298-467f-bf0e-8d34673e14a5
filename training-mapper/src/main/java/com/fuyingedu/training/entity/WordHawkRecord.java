package com.fuyingedu.training.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 单词训练营Word鹰解锁记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-10
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("training_word_hawk_record")
public class WordHawkRecord {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 数据新建时间
     */
    private LocalDateTime createdTime;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * Word鹰ID
     */
    private Long hawkId;

    public static final String ID = "id";

    public static final String CREATED_TIME = "created_time";

    public static final String USER_ID = "user_id";

    public static final String HAWK_ID = "hawk_id";
}
