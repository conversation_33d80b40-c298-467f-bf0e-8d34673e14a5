package com.fuyingedu.training.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 用户表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-29
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("training_user")
public class User {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 数据新建时间
     */
    private LocalDateTime createdTime;

    /**
     * 数据更新时间
     */
    private LocalDateTime updatedTime;

    /**
     * 用户头像
     */
    private String userIcon;

    /**
     * 昵称
     */
    private String nickName;

    /**
     * 姓名
     */
    private String realName;

    /**
     * 手机号
     */
    private Long phoneNum;

    /**
     * 微信unionid
     */
    private String unionId;

    private String openId;

    /**
     * 可发送直播消息数量
     */
    private Integer liveMessageNum;

    /**
     * 可作业和打卡消息数量
     */
    private Integer taskMessageNum;

    /**
     * 可发送的点评消息数量
     */
    private Integer remarkMessageNum;

    public static final String ID = "id";

    public static final String CREATED_TIME = "created_time";

    public static final String UPDATED_TIME = "updated_time";

    public static final String USER_ICON = "user_icon";

    public static final String NICK_NAME = "nick_name";

    public static final String REAL_NAME = "real_name";

    public static final String PHONE_NUM = "phone_num";

    public static final String UNION_ID = "union_id";

    public static final String OPEN_ID = "open_id";

    public static final String LIVE_MESSAGE_NUM = "live_message_num";

    public static final String TASK_MESSAGE_NUM = "task_message_num";

    public static final String REMARK_MESSAGE_NUM = "remark_message_num";
}
