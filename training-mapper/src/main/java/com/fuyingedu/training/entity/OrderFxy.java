package com.fuyingedu.training.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 服务单和扶小鹰关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-21
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("training_order_fxy")
public class OrderFxy {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 数据新增时间
     */
    private LocalDateTime createdTime;

    /**
     * 服务单表ID
     */
    private Long orderId;

    /**
     * 扶小鹰表ID
     */
    private Long fxyId;

    public static final String ID = "id";

    public static final String CREATED_TIME = "created_time";

    public static final String ORDER_ID = "order_id";

    public static final String FXY_ID = "fxy_id";
}
