package com.fuyingedu.training.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 奖状和班级关联关系表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-08
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("training_medal_relation")
public class MedalRelation {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 排期ID
     */
    private Long scheduleId;

    /**
     * 奖状表主键
     */
    private Long medalId;

    /**
     * 班级ID，-1表示所有
     */
    private Long clazzId;

    public static final String ID = "id";

    public static final String USER_ID = "user_id";

    public static final String SCHEDULE_ID = "schedule_id";

    public static final String MEDAL_ID = "medal_id";

    public static final String CLAZZ_ID = "clazz_id";
}
