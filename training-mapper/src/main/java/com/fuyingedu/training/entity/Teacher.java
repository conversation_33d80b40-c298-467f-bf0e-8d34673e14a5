package com.fuyingedu.training.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 老师表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-06
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("training_teacher")
public class Teacher {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 数据新建时间
     */
    private LocalDateTime createdTime;

    /**
     * 数据更新时间
     */
    private LocalDateTime updatedTime;

    /**
     * 用户表主键
     */
    private Long userId;

    /**
     * 可服务的训练营数量
     */
    private Integer campNum;

    /**
     * 老师状态 1-有效 2-无效
     */
    private Byte teacherStatus;

    /**
     * 姓名
     */
    private String realName;

    /**
     * 企业微信二维码
     */
    private String wxUrl;

    public static final String ID = "id";

    public static final String CREATED_TIME = "created_time";

    public static final String UPDATED_TIME = "updated_time";

    public static final String USER_ID = "user_id";

    public static final String CAMP_NUM = "camp_num";

    public static final String TEACHER_STATUS = "teacher_status";

    public static final String REAL_NAME = "real_name";

    public static final String WX_URL = "wx_url";
}
