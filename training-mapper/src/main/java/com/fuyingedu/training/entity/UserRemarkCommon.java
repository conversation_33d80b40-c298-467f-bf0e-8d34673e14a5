package com.fuyingedu.training.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 常用的学员备注表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-18
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("training_user_remark_common")
public class UserRemarkCommon {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 数据新建时间
     */
    private LocalDateTime createdTime;

    /**
     * 用户表主键
     */
    private Long userId;

    /**
     * 备注
     */
    private String remarkContent;

    public static final String ID = "id";

    public static final String CREATED_TIME = "created_time";

    public static final String USER_ID = "user_id";

    public static final String REMARK_CONTENT = "remark_content";
}
