package com.fuyingedu.training.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 老师奖状任务模板和训练营关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-28
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("training_available_camp")
public class AvailableCamp {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 老师奖状任务模板表主键
     */
    private Long availableId;

    /**
     * 1-老师 2-奖状 3-任务模板
     */
    private Byte availableType;

    /**
     * 训练营表主键
     */
    private Long campId;

    /**
     * 关联状态 1-有效 2-无效
     */
    private Byte campStatus;

    public static final String ID = "id";

    public static final String AVAILABLE_ID = "available_id";

    public static final String AVAILABLE_TYPE = "available_type";

    public static final String CAMP_ID = "camp_id";

    public static final String CAMP_STATUS = "camp_status";
}
