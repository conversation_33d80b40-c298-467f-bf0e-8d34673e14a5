package com.fuyingedu.training.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 积分流水表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-26
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("training_order_fxy_reward_log")
public class OrderFxyRewardLog {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 数据新建时间
     */
    private LocalDateTime createdTime;

    /**
     * 操作的用户
     */
    private Long createdUserId;

    /**
     * 服务单表主键
     */
    private Long orderId;

    /**
     * 1-增加 2-减少
     */
    private Byte logType;

    /**
     * 任务积分
     */
    private Integer taskReward;

    /**
     * 备注
     */
    private String logRemark;

    /**
     * 1-已发送MQ 2-发送成功
     */
    private Byte logStatus;

    public static final String ID = "id";

    public static final String CREATED_TIME = "created_time";

    public static final String CREATED_USER_ID = "created_user_id";

    public static final String ORDER_ID = "order_id";

    public static final String LOG_TYPE = "log_type";

    public static final String TASK_REWARD = "task_reward";

    public static final String LOG_REMARK = "log_remark";

    public static final String LOG_STATUS = "log_status";
}
