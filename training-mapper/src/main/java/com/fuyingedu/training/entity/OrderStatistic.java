package com.fuyingedu.training.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 学员的签到、打卡等次数统计信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-20
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("training_order_statistic")
public class OrderStatistic {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 数据新建时间
     */
    private LocalDateTime createdTime;

    /**
     * 数据更新时间
     */
    private LocalDateTime updatedTime;

    /**
     * 服务单表主键
     */
    private Long orderId;

    /**
     * 签到次数
     */
    private Integer enrollmentNum;

    /**
     * 打卡次数
     */
    private Integer punchNum;

    /**
     * 作业次数
     */
    @Deprecated
    private Integer homeworkNum;

    /**
     * 优秀作业次数
     */
    @Deprecated
    private Integer goodHomeworkNum;

    /**
     * 点评他人作业次数
     */
    private Integer remarkNum;

    /**
     * 奖状次数
     */
    private Integer medalNum;

    /**
     * 任务积分
     */
    private Integer taskReward;

    /**
     * 作业点评次数
     */
    private Integer remarkedNum;

    /**
     * 扶小鹰小太阳
     */
    private Integer fxyReward;

    public static final String ID = "id";

    public static final String CREATED_TIME = "created_time";

    public static final String UPDATED_TIME = "updated_time";

    public static final String ORDER_ID = "order_id";

    public static final String ENROLLMENT_NUM = "enrollment_num";

    public static final String PUNCH_NUM = "punch_num";

    public static final String HOMEWORK_NUM = "homework_num";

    public static final String GOOD_HOMEWORK_NUM = "good_homework_num";

    public static final String REMARK_NUM = "remark_num";

    public static final String MEDAL_NUM = "medal_num";

    public static final String TASK_REWARD = "task_reward";

    public static final String REMARKED_NUM = "remarked_num";

    public static final String FXY_REWARD = "fxy_reward";
}
