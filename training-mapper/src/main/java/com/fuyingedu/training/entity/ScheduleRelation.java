package com.fuyingedu.training.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 排期营和扶鹰关联关系表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-31
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("training_schedule_relation")
public class ScheduleRelation {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 数据新建时间
     */
    private LocalDateTime createdTime;

    /**
     * 数据更新时间
     */
    private LocalDateTime updatedTime;

    /**
     * 排期营表主键
     */
    private Long scheduleId;

    /**
     * 对应的排期ID
     */
    private Long relationId;

    /**
     * 对应的排期名称
     */
    private String relationName;

    /**
     * 1-有效 2-无效
     */
    private Byte relationStatus;

    public static final String ID = "id";

    public static final String CREATED_TIME = "created_time";

    public static final String UPDATED_TIME = "updated_time";

    public static final String SCHEDULE_ID = "schedule_id";

    public static final String RELATION_ID = "relation_id";

    public static final String RELATION_NAME = "relation_name";

    public static final String RELATION_STATUS = "relation_status";
}
