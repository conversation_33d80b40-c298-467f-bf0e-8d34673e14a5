package com.fuyingedu.training.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 单词训练营Word鹰表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-27
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("training_word_hawk")
public class WordHawk {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 数据新建时间
     */
    private LocalDateTime createdTime;

    /**
     * Word鹰的名字
     */
    private String hawkName;

    private String lockUrl;

    private String unlockUrl;

    /**
     * Word鹰的图片地址
     */
    private String hawkUrl;

    /**
     * 封面图
     */
    private String coverUrl;

    /**
     * Word鹰介绍
     */
    private String hawkContent;

    /**
     * 解锁需要的积分
     */
    private Integer rewardNum;

    public static final String ID = "id";

    public static final String CREATED_TIME = "created_time";

    public static final String HAWK_NAME = "hawk_name";

    public static final String LOCK_URL = "lock_url";

    public static final String UNLOCK_URL = "unlock_url";

    public static final String HAWK_URL = "hawk_url";

    public static final String COVER_URL = "cover_url";

    public static final String HAWK_CONTENT = "hawk_content";

    public static final String REWARD_NUM = "reward_num";
}
