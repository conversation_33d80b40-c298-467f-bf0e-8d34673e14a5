package com.fuyingedu.training.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 奖状颁发记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-03
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("training_medal_record")
public class MedalRecord {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 数据新建时间
     */
    private LocalDateTime createdTime;

    /**
     * 创建人ID
     */
    private Long createdUserId;

    /**
     * 排期表主键
     */
    private Long scheduleId;

    /**
     * 奖状名称
     */
    private String medalName;

    /**
     * 模板名称
     */
    private String templateName;

    /**
     * 奖状图片
     */
    private String medalIcon;

    /**
     * 奖状内容
     */
    private String medalContent;

    public static final String ID = "id";

    public static final String CREATED_TIME = "created_time";

    public static final String CREATED_USER_ID = "created_user_id";

    public static final String SCHEDULE_ID = "schedule_id";

    public static final String MEDAL_NAME = "medal_name";

    public static final String TEMPLATE_NAME = "template_name";

    public static final String MEDAL_ICON = "medal_icon";

    public static final String MEDAL_CONTENT = "medal_content";
}
