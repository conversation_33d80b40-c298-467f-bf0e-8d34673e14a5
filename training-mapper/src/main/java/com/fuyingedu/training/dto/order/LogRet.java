package com.fuyingedu.training.dto.order;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class LogRet {


    /**
     * 批次ID
     */
    private Long batchId;

    private Integer num;

    /**
     * 1-改期 2-分配辅导老师 3-关闭订单 4-核销订单 5-删除备注 6-修改辅导老师 7-备注 8-分班 9-分组 10-改班 11-改组 12-修改订单状态-可能退单 13-修改订单状态-正常
     */
    private Integer operationType;
    /**
     * 操作的用户ID
     */
    private Long userId;
}
