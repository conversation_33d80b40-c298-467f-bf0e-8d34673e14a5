package com.fuyingedu.training.dto.order;

import com.fuyingedu.training.common.model.PageReq;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 服务单查询列表参数
 */
@Getter
@Setter
public class OrderListParam extends PageReq {

    /**
     * 来源服务单号
     */
    private String orderNo;

    /**
     * 学员姓名
     */
    private String realName;

    /**
     * 手机号
     */
    private Long phoneNum;

    /**
     * 学员证件号
     */
    private String cardNum;
    /**
     * 训练营ID
     */
    private Long campId;

    private List<Long> campIds;

    /**
     * 训练营排期ID
     */
    private Long scheduleId;
    /**
     * 服务状态 1-正常 2-已关闭 3-已退款
     */
    private Byte orderStatus;
    /**
     * 排期状态 1-未开始 2-进行中 3-已结束
     */
    private Byte scheduleStatus;
    /**
     * 核销状态 1 - 未核销 2 - 已核销
     */
    private Byte signStatus;

    /**
     * 分配状态 1 - 未分配 2 - 已分配
     */
    private Byte allocStatus;
}
