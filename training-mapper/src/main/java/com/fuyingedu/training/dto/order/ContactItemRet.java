package com.fuyingedu.training.dto.order;

import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
public class ContactItemRet {

    private Long id;
    /**
     * 用户名
     */
    private String realName;
    /**
     * 用户手机号
     */
    private Long phoneNum;
    /**
     * 学员名称
     */
    private String studentName;

    private String userIcon;
    /**
     * 学员证件号
     */
    private String cartNo;

    /**
     * 1-正常 2-已关闭 3-已退款
     */
    private Byte orderStatus;

    /**
     * 小班表主键
     */
    private Long clazzId;

    private Long groupId;

    /**
     * 是否加导师微信 1-没有加 2-已加
     */
    private Byte teacherWxStatus;

    /**
     * 是否加助教微信 1-没有加 2-已加
     */
    private Byte assistantWxStatus;

    /**
     * 是否加群微信 1-没有加 2-已加
     */
    private Byte groupWxStatus;

    /**
     * 确认情况 1-未确认 2-自己确认 3-助教确认 4-不来
     */
    private Byte confirmStatus;

    private String orderRemark;

    private LocalDateTime signTime;
}
