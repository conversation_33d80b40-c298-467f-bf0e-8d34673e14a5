package com.fuyingedu.training.dto.order;

import com.fuyingedu.training.common.model.PageReq;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class ContactListParam extends PageReq {

    /**
     * 排期ID
     */
    private Long scheduleId;
    private Long teacherId;
    /**
     * 用户信息
     */
    private String userInfo;

    /**
     * 学员信息
     */
    private String studentInfo;

    /**
     * 助教ID
     */
    private Long assistantId;

    /**
     * 班级ID
     */
    private List<Long> clazzIds;

    /**
     * 服务状态：1-正常 2-已关闭 3-已退款
     */
    private Byte orderStatus;

    /**
     * 加导师微信 1-未加 2-已加
     */
    private Byte teacherWxStatus;

    /**
     * 加助教微信 1-未加 2-已加
     */
    private Byte assistantWxStatus;

    /**
     * 加群 1-未加 2-已加
     */
    private Byte groupWxStatus;

    /**
     * 最近联系记录 1-未确认 2-自己确认 3-助教确认 4-不来
     */
    private Byte contactStatus;

    /**
     * 班级状态 1-未分班 2-已分班
     */
    private Byte clazzStatus;

    /**
     * 用户备注
     */
    private String orderRemark;
}
