<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fuyingedu.training.mapper.TaskRelationMapper">


    <select id="groupTaskId" resultType="com.fuyingedu.training.dto.task.RelationTaskRet">
        select task_id, assistant_id, teacher_id from training_task_relation where
        schedule_id = #{scheduleId} and assistant_id in
        <foreach collection="assistantIds" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        and clazz_id = -1 group by teacher_id, assistant_id, task_id
    </select>
</mapper>
