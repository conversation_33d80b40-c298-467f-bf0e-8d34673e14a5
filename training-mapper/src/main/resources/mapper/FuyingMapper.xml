<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fuyingedu.training.mapper.FuyingMapper">

    <select id="queryUserInfo" resultType="com.fuyingedu.training.dto.fuying.UserInfoRet" parameterType="string">
        select a.uid uid, nickname, real_name, photo_url, union_id, open_id, a.phone phoneNum from jm_user_info a inner join jm_user_weixin b on a.uid = b.uid
        where
        <if test="phoneNum != null">
            a.phone = #{phoneNum}
        </if>
        <if test="unionId != null">
            union_id = #{unionId}
        </if>
    </select>

    <select id="queryCampNameList" resultType="string">
        select distinct name from jm_user_tmp where type = 3
    </select>

    <select id="queryCampList" resultType="com.fuyingedu.training.dto.fuying.CampRet">
        select a.pid, a.name, b.title, b.type from jm_user_tmp a inner join jm_product b on a.pid = b.id
        where a.type = 3 order by a.pid desc
    </select>

    <select id="queryScheduleList" resultType="com.fuyingedu.training.dto.fuying.ScheduleRet"
            parameterType="com.fuyingedu.training.dto.fuying.FyListParam"
    >
        select id, pid, title, start_time from jm_product_course_detail where pid in
        <foreach collection="param.pidList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        <if test="param.startTime != null">
            and start_time >= #{param.startTime}
        </if>
        <if test="param.endTime != null">
                and start_time &lt;= #{param.endTime}
        </if>
        order by id desc
    </select>

    <select id="queryOrderList" resultType="com.fuyingedu.training.dto.fuying.OrderRet">
        SELECT
        jui.uid uid,
        jui.nickname nickName,
        jui.real_name realName,
        jui.phone phoneNum,
        jui.photo_url userIcon,
        juw.union_id unionId,
        jod.pid pid,
        jod.type studentFlag,
        jooca.pcd_id scheduleId,
        jufm.id studentId,
        jufm.NAME studentName,
        jufm.idcard idCard,
        jo.order_no orderNo,
        jo.create_time createdTime,
        jo.refund_status refundStatus
        FROM
        jm_order jo
        LEFT JOIN jm_order_detail jod ON jo.id = jod.oid /*子订单表 课程与订单关联在此表中*/
        LEFT JOIN jm_product jp ON jp.id = jod.pid /*课程基础表*/
        LEFT JOIN jm_user_info jui ON jui.uid = jo.uid /*用户详情表*/
        LEFT join jm_user_weixin juw on jui.uid = juw.uid
        LEFT JOIN jm_order_offline_course_arrange jooca ON jooca.oid = jod.oid /*订单排期表*/
        LEFT JOIN jm_order_offline_course_member joocm ON joocm.oid = jo.id and joocm.status=0/*学员关联表*/
        LEFT JOIN jm_user_family_member jufm ON jufm.id = joocm.family_id /*学员详情表*/
        WHERE
        jod.pid in
        <foreach collection="pidList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        and jooca.pcd_id in
        <foreach collection="scheduleIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        AND jo.pay_status = 1
        and jod.type in (0,6)
        and jod.pid is not null
        and jo.order_no is not null
        and jui.phone is not null and jui.phone != ''
        and juw.union_id is not null
        and jo.pay_time >= #{payTime}
    </select>

    <select id="queryStudentList" resultType="com.fuyingedu.training.dto.fuying.StudentRet">
        select jufm.id id, jufm.name studentName, jufm.idcard cartNum
        from jm_user_family_member jufm
        left join jm_person_info jpi on jpi.id_card=jufm.idcard
        where jpi.id is not null and jufm.status in (0,1) and jufm.uid = #{uid}
    </select>

    <select id="queryStudentById" resultType="com.fuyingedu.training.entity.Student">
        select jufm.id oldOuterId, jufm.name realName, jufm.idcard cardNum, jui.phone phoneNum
        from jm_user_family_member jufm left join jm_user_info jui on jufm.uid = jui.uid where jufm.id = #{id}
    </select>

    <select id="queryFxyList" parameterType="com.fuyingedu.training.dto.fuying.FxyListParam"
            resultType="com.fuyingedu.training.dto.fuying.FxyItemRet">
        select jpud.uid uid,
        jps.id outerId,
        jps.nickname nickname,
        jps.account fxyAccount,
        jps.open_id openId,
        jps.photo_url photoUrl,
        jfps.sn fxySn,
        jfps.imei fxyImei
        from jm_pad_user_device jpud
        left join jm_pad_student jps on jps.id=jpud.student_id
        left join jm_fxy_product_storage jfps on jfps.id=jpud.device_id
        where jpud.status=0
            <if test="param.uid != null">
                and jpud.uid = #{param.uid}
            </if>
            <if test="param.code != null">
                and (jfps.sn = #{param.code} or jfps.imei = #{param.code})
            </if>
            <if test="param.account != null">
                and jps.account = #{param.account}
            </if>
    </select>

    <select id="queryFxyByOpenId" resultType="com.fuyingedu.training.dto.fuying.FxyItemRet">
        select
        jps.nickname nickname,
        jps.account fxyAccount,
        jps.open_id openId,
        jps.photo_url photoUrl
        from jm_pad_student jps where jps.open_id = #{openId}
    </select>
</mapper>
