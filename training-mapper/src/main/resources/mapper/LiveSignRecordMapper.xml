<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fuyingedu.training.mapper.LiveSignRecordMapper">

    <select id="groupByOrderIds" resultType="com.fuyingedu.training.dto.word.OrderGroupRet">
        select order_id, count(*) as num from training_live_sign_record where order_id in
        <foreach collection="orderIds" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        group by order_id
    </select>
</mapper>
