<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fuyingedu.training.mapper.TaskSubmitRecordMapper">

    <select id="groupTaskDoneNum" resultType="com.fuyingedu.training.dto.task.TaskDoneNumRet">
        select task_id, count(*) num from training_task_submit_record where order_id = #{orderId}
        and task_id in
        <foreach collection="taskIds" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        group by task_id
    </select>
</mapper>
