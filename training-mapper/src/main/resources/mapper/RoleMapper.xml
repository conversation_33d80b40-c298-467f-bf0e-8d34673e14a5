<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fuyingedu.training.mapper.RoleMapper">

    <select id="pageManagerList" resultType="com.fuyingedu.training.dto.role.UserItemRet">
        select b.id userId, c.role_name roleName, b.phone_num phoneNum, b.real_name realName, b.user_icon userIcon, b.outer_id outerId
        from training_user_role a left join training_user b on a.user_id = b.id
        left join training_role c on a.role_id = c.id
        where a.role_id != 1
        <if test="userInfo != null">
            and (b.phone_num = #{userInfo} or b.real_name like concat('%',#{userInfo},'%'))
        </if>
        order by a.id desc
    </select>
</mapper>
