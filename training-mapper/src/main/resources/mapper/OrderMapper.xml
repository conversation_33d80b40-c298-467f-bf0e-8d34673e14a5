<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fuyingedu.training.mapper.OrderMapper">

    <select id="pageListForOrder"
            parameterType="com.fuyingedu.training.dto.order.OrderListParam"
            resultType="com.fuyingedu.training.dto.order.OrderItemRet">
        select a.id, a.order_no orderNo, a.real_order_no realOrderNo, a.teacher_id teacherId, a.user_id userId, a.order_status orderStatus, e.start_time startTime, e.end_time endTime,
        a.camp_id campId, e.id scheduleId, b.real_name realName,b.phone_num phoneNum, c.real_name studentName, a.order_remark orderRemark, a.sign_time signTime,
        a.clazz_id clazzId, a.group_id groupId, e.schedule_name scheduleName
        from training_order a
        inner join training_user b on a.user_id = b.id
        left join training_student c on a.student_id = c.id
        LEFT join training_schedule e on e.id = a.schedule_id
        <where>
            <if test="param.orderNo != null and param.orderNo != ''">
                and a.order_no = #{param.orderNo}
            </if>
            <if test="param.realName != null and param.realName != ''">
                and (c.real_name like concat('%', #{param.realName}, '%'))
            </if>
            <if test="param.phoneNum != null">
                and b.phone_num = #{param.phoneNum}
            </if>
            <if test="param.cardNum != null and param.cardNum != ''">
                and c.card_num = #{param.cardNum}
            </if>
            <if test="param.campIds != null">
                and a.camp_id in
                <foreach collection="param.campIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="param.scheduleId != null">
                and a.schedule_id = #{param.scheduleId}
            </if>
            <if test="param.orderStatus != null">
                and a.order_status = #{param.orderStatus}
            </if>
            <if test="param.scheduleStatus != null">
                <if test="param.scheduleStatus == 1">
                    and e.start_time &gt; now()
                </if>
                <if test="param.scheduleStatus == 2">
                    and e.start_time &lt;= now() and e.end_time &gt; now()
                </if>
                <if test="param.scheduleStatus == 3">
                    and e.end_time &lt;= now()
                </if>
            </if>
            <if test="param.signStatus!= null">
                <if test="param.signStatus == 1">
                    and a.sign_time is null
                </if>
                <if test="param.signStatus == 2">
                    and a.sign_time is not null
                </if>
            </if>
            <if test="param.allocStatus != null">
                <if test="param.allocStatus == 1">
                    and a.teacher_id is null
                </if>
                <if test="param.allocStatus == 2">
                    and a.teacher_id is not null
                </if>
            </if>
        </where>
        order by a.id desc
    </select>

    <select id="pageContactList" resultType="com.fuyingedu.training.dto.order.ContactItemRet"
            parameterType="com.fuyingedu.training.dto.order.ContactListParam"
    >
        select a.id, a.order_remark, b.real_name realName, b.phone_num phoneNum, c.real_name studentName,
        c.card_num cardNum, b.user_icon userIcon, a.group_id groupId,
        a.order_status orderStatus, a.clazz_id clazzId, a.assistant_wx_status assistantWxStatus,
        a.teacher_wx_status teacherWxStatus, a.group_wx_status groupWxStatus, a.confirm_status confirmStatus,
        a.sign_time signTime
        from training_order a
        left join training_user b on a.user_id = b.id
        left join training_student c on a.student_id = c.id
        where a.schedule_id = #{param.scheduleId}
        <if test="param.teacherId != null">
            and a.teacher_id = #{param.teacherId}
        </if>
        <if test="param.userInfo != null and param.userInfo != ''">
            and (b.real_name like concat('%', #{param.userInfo}, '%') or
            b.phone_num = #{param.userInfo})
        </if>
        <if test="param.orderRemark != null and param.orderRemark != ''">
            and a.order_remark like concat('%', #{param.orderRemark}, '%')
        </if>
        <if test="param.studentInfo != null and param.studentInfo!= ''">
            and (c.real_name like concat('%', #{param.studentInfo}, '%') or
            c.card_num = #{param.studentInfo})
        </if>
        <if test="param.clazzIds != null">
            and a.clazz_id in
            <foreach collection="param.clazzIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="param.orderStatus != null">
            and a.order_status = #{param.orderStatus}
        </if>
        <if test="param.orderStatus == null">
            and a.order_status in (1, 4)
        </if>
        <if test="param.teacherWxStatus != null">
            and a.teacher_wx_status = #{param.teacherWxStatus}
        </if>
        <if test="param.assistantWxStatus != null">
            and a.assistant_wx_status = #{param.assistantWxStatus}
        </if>
        <if test="param.groupWxStatus != null">
            and a.group_wx_status = #{param.groupWxStatus}
        </if>
        <if test="param.clazzStatus != null">
            <if test="param.clazzStatus == 1">
                and a.clazz_id is not null
            </if>
            <if test="param.clazzStatus == 2">
                and a.clazz_id is null
            </if>
        </if>
        <if test="param.contactStatus != null">
            and a.confirm_status = #{param.contactStatus}
        </if>
    </select>

    <select id="pageRecordList" resultType="com.fuyingedu.training.dto.order.RecordItemRet"
            parameterType="com.fuyingedu.training.dto.order.RecordListParam">
        select a.id id, b.real_name realName, b.phone_num phoneNum, c.real_name studentName,
        c.card_num cartNo, a.student_flag studentFlag, a.teacher_id teacherId, a.schedule_id scheduleId, a.clazz_id clazzId,
        a.group_id groupId, a.student_type studentType, b.user_icon userIcon, a.order_remark orderRemark,
        d.medal_num medalNum, d.task_reward taskReward, d.remark_num remarkNum, d.enrollment_num enrollmentNum,
        d.punch_num punchNum, d.homework_num homeworkNum,d.remarked_num remarkedNum
        from training_order a
        left join training_user b on a.user_id = b.id
        left join training_student c on a.student_id = c.id
        left join training_order_statistic d on a.id = d.order_id
        where a.schedule_id = #{param.scheduleId} and a.clazz_id = #{param.clazzId} and a.order_status in (1, 4)
        <if test="param.userInfo != null and param.userInfo != ''">
            and (b.real_name like concat('%', #{param.userInfo}, '%') or
            b.phone_num = #{param.userInfo})
        </if>
        <if test="param.studentInfo != null and param.studentInfo!= ''">
            and (c.real_name like concat('%', #{param.studentInfo}, '%') or
            c.card_num = #{param.studentInfo})
        </if>
        <if test="param.groupId != null">
            <if test="param.groupId == -1">
                and a.group_id is null
            </if>
            <if test="param.groupId != -1">
                and a.group_id = #{param.groupId}
            </if>
        </if>
        <if test="param.studentType != null">
                and a.student_type = #{param.studentType}
        </if>
        order by ${param.sortField} ${param.sortOrder}
    </select>

    <select id="groupStudentNum" resultType="com.fuyingedu.training.dto.order.StudentNumRet" parameterType="long">
        select student_id studentId, count(*) studentNum from training_order where student_id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        group by student_id
    </select>
</mapper>
