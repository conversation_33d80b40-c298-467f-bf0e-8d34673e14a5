<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fuyingedu.training.mapper.WordPkConfigMapper">

    <select id="groupByScheduleId" resultType="com.fuyingedu.training.dto.GroupNumRet">
        select schedule_id id, count(*) num from training_word_pk_config where schedule_id in
        <foreach item="item" collection="list" separator="," open="(" close=")" index="index">
            #{item}
        </foreach>
        group by schedule_id
    </select>
</mapper>
