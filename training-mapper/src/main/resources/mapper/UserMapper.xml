<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fuyingedu.training.mapper.UserMapper">

    <select id="pageAdmin" resultType="com.fuyingedu.training.entity.User">
        select a.id, user_icon, nick_name, real_name, phone_num, b.role_id liveMessageNum
        from training_user a right join training_user_role b on a.id = b.user_id
        where b.role_id in (2, 3)
        <if test="name != null and name != ''">
            and (nick_name like concat('%', #{name}, '%') or real_name like concat('%', #{name}, '%'))
        </if>
        <if test="phoneNum != null">
            and phone_num = #{phoneNum}
        </if>
        order by a.id desc
    </select>

</mapper>
