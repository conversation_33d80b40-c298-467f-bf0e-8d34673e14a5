<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fuyingedu.training.mapper.OperationLogMapper">

    <select id="groupByBatchId" resultType="com.fuyingedu.training.dto.order.LogRet">
        select batch_id, count(*) num, operation_type, user_id from training_operation_log
        where log_status = 1 and next_schedule = #{scheduleId}
        <if test="type == 1">
            and next_teacher = #{teacherId} and prev_teacher != #{teacherId}
        </if>
        <if test="type == 2">
            and next_assistant = #{teacherId} and prev_assistant != #{teacherId}
        </if>
        group by batch_id, operation_type, user_id order by batch_id desc
    </select>
    
    <select id="pageSubList" resultType="com.fuyingedu.training.entity.OperationLog">
        select a.id, a.order_id, a.operation_type, a.user_id, a.created_time, a.prev_schedule, a.next_schedule,
        a.prev_teacher, a.next_teacher, a.prev_assistant, a.next_assistant, a.prev_content, a.next_content
        from training_operation_log a
        left join training_order b on a.order_id = b.id
        left join training_user c on b.user_id = c.id
        left join training_student d on b.student_id = d.id
        where a.log_status = 1 and a.prev_schedule = #{scheduleId}
        <if test="teacherType == 1">
            and a.prev_teacher = #{teacherId} and a.next_teacher != #{teacherId}
        </if>
        <if test="teacherType == 2">
            and a.prev_assistant = #{teacherId} and a.next_assistant != #{teacherId}
        </if>
        <if test="userInfo != null and userInfo != ''">
            and (c.real_name like concat('%', #{userInfo}, '%') or
            c.phone_num = #{userInfo})
        </if>
        <if test="orderRemark != null and orderRemark != ''">
            and b.order_remark like concat('%', #{orderRemark}, '%')
        </if>
        <if test="studentInfo != null and studentInfo!= ''">
            and (d.real_name like concat('%', #{studentInfo}, '%') or
            d.card_num = #{studentInfo})
        </if>
        order by a.id desc
    </select>
</mapper>
