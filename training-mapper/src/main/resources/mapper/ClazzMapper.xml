<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fuyingedu.training.mapper.ClazzMapper">

    <select id="groupOrderNum" resultType="com.fuyingedu.training.dto.clazz.OrderNumRet">
        select clazz_id, student_flag, count(*) studentNum from training_order where clazz_id in
        <foreach collection="clazzIds" item="clazzId" open="(" separator="," close=")">
            #{clazzId}
        </foreach>
        and order_status in (1, 4) group by clazz_id, student_flag
    </select>
</mapper>
