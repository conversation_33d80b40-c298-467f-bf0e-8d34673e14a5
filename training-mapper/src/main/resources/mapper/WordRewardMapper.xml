<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fuyingedu.training.mapper.WordRewardMapper">

    <select id="getRewardByUserIds" resultType="int">
        select sum(reward_num) from training_word_reward where user_id in
        <foreach collection="userIds" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="getRewardByOrderIds" resultType="int">
        select sum(reward_num) from training_word_reward where order_id in
        <foreach collection="orderIds" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="groupRewardByOrderIds" resultType="com.fuyingedu.training.dto.word.OrderGroupRet">
        select order_id, sum(reward_num) num from training_word_reward where order_id in
        <foreach collection="orderIds" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        group by order_id
    </select>
</mapper>
